export class Lock {
  private isLocked: boolean;
  private queue: ((value: void | PromiseLike<void>) => void)[];
  constructor() {
    this.isLocked = false;
    this.queue = [];
  }

  async acquire() {
    return new Promise<void>((resolve) => {
      if (!this.isLocked) {
        this.isLocked = true;
        resolve();
      } else {
        this.queue.push(resolve);
      }
    });
  }

  release() {
    if (this.queue.length > 0) {
      const resolve = this.queue.shift();
      resolve!();
    } else {
      this.isLocked = false;
    }
  }
}
