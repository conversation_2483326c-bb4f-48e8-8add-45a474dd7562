// import AutoClient from '../auto-client';
import UserManager from '../rpa/user-manager';
// import { Logger } from '../../log';
// import { createPromiseCapability, PromiseCapability } from '../promise-capability';
import { createPromiseCapability } from '../../utils/promise';
import PQueue from 'p-queue';
import { msgCreate } from './utils/msg-create';
import { toggleLock } from './utils/toggle-lock';
import { sendMessage } from './utils/send-message';
import { updateAgentStatue } from './utils/update-agent-statue';
import { transferUser } from './utils/transfer-user';
import { getSessionMessage } from './utils/get-session-message';
import { getCurrentUesrMessage, getCurrentUserMessageApi } from './utils/get-current-uesr-message';
import { statusSwitchBtn } from './utils/status-switch-btn';
import { loadConfig } from '../rpa/agent-config';
import { sendImage } from './utils/send-image';
import ms from 'ms';
import { TimerActivate } from './utils/press-for-payment';
// import { DingtalkBot } from 'dd-bot';
import { setupReminders } from './utils/setup-remindersr';
import { currentReceptInfo, uploadBaseInfo } from './utils/current-recept-info';
import { SystemStatusManager } from './utils/agent-info';
import { uploadGoodsInfo, formatGoodsList } from './utils/shop-Info';
import ExpressTracker from '../rpa/kuaidi100';
import _ from 'lodash';
import { needTransfer } from '../rpa/utils/need-transfer';
import { Integration } from '../Integration';
import { WebContentsView } from 'electron';
import Tab from '../../tab';
// import { IntegrationConfig } from '../index';
// import { transpile } from 'typescript';
import path from 'path';
import fs from 'fs';
import { Logger } from 'log4js';
class Doudian extends Integration {
  // @ts-ignore maybe use later
  #config: PddConfig | undefined;
  #runningPromise: ReturnType<typeof createPromiseCapability>;
  #ctx: {
    env?: any;
    emit?: any;
    userManager?: UserManager;
    log?: Logger;
    agentConfig: {
      transer_chat: string;
      transfer_blacklist: string[];
      press_interval: number[];
      press_payment_list: string[];
      pressImgs: string[];
      ding_token: string;
      greeting: string;
      kuaidi: {
        customer: string;
        key: string;
        template_name: string;
        queryUrl: string;
      };
    };
    expressTracker?: ExpressTracker;
    currentReceptInfoInterval?: NodeJS.Timeout;
    userName: string;
    goodsList: any[];
  };
  #myqaSentMessage: string[];
  #waitingToBeSent: null | NodeJS.Timeout;
  #isSilence: boolean;
  #agentStatue: string;
  // #dingtalkBot: DingtalkBot;
  #shopInfo: any;
  #msgQueue = new PQueue({
    concurrency: 1,
    timeout: ms('1m'),
    throwOnTimeout: false
  });
  #durations: number[];
  #systemStatusManager: SystemStatusManager;
  #userList: {
    [key: string]: {
      user_id: string;
      timerActivate?: TimerActivate;
      orderInfo: unknown[];
    };
  };
  #webContentsView: WebContentsView;

  constructor(tab: Tab, config) {
    super(tab, config);
    this.#config = config;
    this.#webContentsView = tab.view;
    this.#ctx = {
      agentConfig: {
        transer_chat: '',
        transfer_blacklist: [],
        press_interval: [],
        press_payment_list: [],
        pressImgs: [],
        ding_token: '',
        greeting: '',
        kuaidi: {
          customer: '',
          key: '',
          template_name: '',
          queryUrl: ''
        }
      },
      userName: '',
      goodsList: []
    };
    this.#ctx.env = {
      endpoint: config.options.endpoint,
      assistantId: config.options.assistantId,
      secretKey: config.options.secretKey
    };
    this.#ctx.log = this.getLogger();
    this.#ctx.emit = this.emit.bind(this);
    this.#ctx.userManager = new UserManager(this.#ctx);
    this.#runningPromise = createPromiseCapability();
    this.#myqaSentMessage = [];
    this.#isSilence = config.options.isSilence;
    this.#waitingToBeSent = null;
    this.#agentStatue = 'pending';
    this.#userList = {};
    this.#durations = [];
    this.#systemStatusManager = new SystemStatusManager(
      this.#config?.version,
      this.#ctx.userManager.startAt
    );
  }

  async userMsgCreate(postData) {
    // msgCreate(
    //   this,
    //   postData,
    //   this.#isSilence,
    //   (arr) => this.setMyqaSentMessage(arr),
    //   () => this.#systemStatusManager.addTotalReceiveMsgCount()
    // );
    if (!this.#myqaSentMessage?.includes(postData.msg_body_list[0].serverId)) {
      this.setMyqaSentMessage(postData.msg_body_list.map((item) => item.serverId));
      this.#systemStatusManager.addTotalReceiveMsgCount();
      await this.evaluate(() => {
        (window as any).toggleLock(false);
      });
      const resultData: any[] = await this.evaluate(
        ({ id, isSilence, postData, agentConfig }) => {
          const messageList = document.querySelector('.messageList');
          const result: any[] = [];
          if (messageList) {
            const childElements = messageList.children[1].children;
            const childEnd = childElements[childElements.length - 1];
            const key = Object.keys(childEnd).find(
              (item) => item.indexOf('__reactInternalInstance') !== -1
            );
            if (key) {
              const data = childEnd[key].memoizedProps.children[0].props.data;
              const index = data.findIndex((item) => item.serverId === id);
              // 判断时候是新用户发送欢迎语
              if (data.length < 5) {
                (window as any).setTextAreaAndSend(
                  postData.uid,
                  agentConfig.greeting,
                  agentConfig,
                  'txt'
                );
              }
              const sender_role = JSON.parse(JSON.stringify(data[index] ?? {}))?.ext?.sender_role;

              if (sender_role === '1') {
                for (let i = index; i > 0; i--) {
                  // sender_role 1 用户消息 2客服消息 3系统消息
                  if (data[i].ext.sender_role === '1') {
                    result.push(data[i]);
                  } else {
                    break;
                  }
                }
              }

              if (sender_role === '2' && isSilence) {
                for (let i = index; i > 0; i--) {
                  // sender_role 1 用户消息 2客服消息 3系统消息
                  if (data[i].ext.sender_role === '2') {
                    result.push(data[i]);
                  } else {
                    break;
                  }
                }
              }
            }
          }
          return JSON.parse(JSON.stringify(result.reverse()));
        },
        {
          id: postData.msg_body_list[0].serverId,
          isSilence: this.#isSilence,
          postData,
          agentConfig: this.#ctx.agentConfig
        }
      );

      if (this.#waitingToBeSent) {
        clearTimeout(this.#waitingToBeSent);
        this.#waitingToBeSent = null;
      }
      this.#waitingToBeSent = setTimeout(async () => {
        const resultDataFilter = resultData.filter((item) => {
          return !this.#myqaSentMessage.includes(item.serverId);
        });
        if (this.#waitingToBeSent) {
          clearTimeout(this.#waitingToBeSent);
          this.#waitingToBeSent = null;
        }
        await this.evaluate(() => {
          (window as any).toggleLock(true);
        });
        await this.#ctx.userManager!.get(postData.uid).addMsgAndRun(
          resultDataFilter.map((item) => {
            const typeMap = new Map()
              .set('[图片]', 'image')
              .set('[商品]', 'card')
              .set('[订单]', 'card')
              .set('[待收款]收到用户打款，请及时收款', 'card');
            const static_data = JSON.parse(item.ext.static_data ?? '{}');
            // 订单消息中的 order_id  普通消息中的shark_order_id
            return {
              msg: item.content,
              id: item.serverId,
              type: typeMap.get(item.content) || 'txt',
              user: item.sender,
              time: item.createTime,
              metadata: {
                shop: item.ext.shop_id || '',
                goods: {
                  id: item?.ext?.shark_product_id || item?.ext?.goods_id || '',
                  name: static_data?.goods_name ?? '',
                  skuName: static_data?.goods_spec_desc ?? '',
                  skuId: static_data?.sku_id ?? ''
                },
                ...(item.content === '[图片]'
                  ? {
                      image: {
                        url: item?.ext?.imageUrl,
                        txt: item.content
                      }
                    }
                  : {})
              }
            };
          }) as any[]
        );
      }, 500);
    }
  }
  async setMyqaSentMessage(arr) {
    this.#myqaSentMessage = [...this.#myqaSentMessage, ...arr];
  }

  // 加载阿根廷配置文件
  async setAgentConfig() {
    const conf: any = await loadConfig(
      this.#ctx.env.endpoint,
      this.#ctx.env.assistantId,
      this.#ctx.log
    );
    if (!conf?.meta_data) {
      const e = new Error();
      e.name = '配置文件加载失败';
      throw e;
    }
    this.#ctx.agentConfig = conf.meta_data;
    this.#ctx.agentConfig = conf.meta_data;
    this.#ctx.log?.debug('配置文件加载完成');
    // this.dingtalkBot(this.#ctx.agentConfig.ding_token);
    this.#ctx.expressTracker = new ExpressTracker(this.#ctx.agentConfig.kuaidi, this.#ctx.log);
  }

  // 初始化钉钉机器人
  dingtalkBot(ding_token) {
    if (ding_token) {
      // this.#ctx.log?.debug('初始化钉钉机器人', ding_token);
      // this.#dingtalkBot = new DingtalkBot(
      //   `https://oapi.dingtalk.com/robot/send?access_token=${ding_token}`
      // );
    }
  }

  async sendMessage(uid, content, type, sendType: string = 'dialogue') {
    if (!this.#isSilence) {
      this.#msgQueue.add(async () => {
        // 上锁防止新消息进线切换会话
        await this.evaluate(() => {
          (window as any).toggleLock(false);
        });
        if (sendType === 'rush') {
          if (this.#userList[uid]?.orderInfo?.length > 0) {
            this.#ctx.log?.debug(uid, '有订单不催付');
            return;
          }
        }
        this.#systemStatusManager.addMyqaAnswered();
        // 发送消息
        await this.evaluate(
          ({ id, msg, agentConfig, type }) => {
            (window as any).setTextAreaAndSend(id, msg, agentConfig, type);
          },
          {
            id: uid,
            msg: content,
            agentConfig: this.#ctx.agentConfig,
            type
          }
        );
        // 开锁
        await this.evaluate(() => {
          (window as any).toggleLock(true);
        });
      });
    }
  }

  async start() {
    this.#ctx.log?.debug('doudian-start');
    this.useDebuggerOnRequestAndResponse();
    this.run();
  }

  async stop() {
    this.#ctx.log?.debug('doudian-close');
    this.doStop();
  }

  async run(): Promise<void> {
    // 抖店 为 0 小休(忙碌) 1 在线 2离线 => 号服保状态 0 小休(忙碌) 1 在线 3 离线
    const hfbStatusMap = new Map().set(0, 0).set(1, 1).set(2, 3);
    try {
      this.setAgentConfig();
      this.on('response-to-user', async (user, content: any, { duration, res }) => {
        console.log(res, duration, 'durationdurationdurationduration');
        if (res) {
          const { metadata } = res;
          const age = _.get(metadata, 'init.state.user_info.age');
          const region = _.get(metadata, 'init.state.user_info.region');
          const proc_state = _.get(metadata, 'init.state.proc_state', '售前');
          if (region) {
            this.#ctx.log?.debug('识别出region，跳过催付', user, region, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
          if (age && (age < 18 || age > 60)) {
            this.#ctx.log?.debug('识别到无法办理年龄，跳过催付', user, age, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
          if (proc_state !== '售前') {
            this.#ctx.log?.debug('识别到订单状态，跳过催付', user, proc_state, res.id);
            this.#userList[user].timerActivate?.cancel();
          }
        }

        const msg = content
          .map((item) => (item.type === 'text' ? item.text[0].value : ''))
          .join(',');
        if (duration) {
          this.#durations.push(duration);
          const sum = this.#durations.reduce(
            (accumulator, currentValue) => accumulator + currentValue,
            0
          );
          const average = sum / this.#durations.length;
          this.#systemStatusManager.setAvgMyqaElapsed(average);
          this.#systemStatusManager.setMinMyqaElapsed(duration);
          this.#systemStatusManager.setMaxMyqaElapsed(duration);
        }

        // if (this.#userList[user].orderInfo.length) {
        //   await this.#expressTracker.formatKuadiContent(user, msg, {
        //     code: "",
        //     phone: "",
        //   });
        // }

        this.sendMessage(user, msg, 'txt');
      });

      // 记录超时与错误
      this.on('myqa-error', async (uid, err) => {
        this.#systemStatusManager.addMyqaError();
        if (err?.includes('超时')) {
          this.#systemStatusManager.addmyqaTimeout();
        }
        this.#ctx.log?.error(uid, 'myqa-error', err);
        // this.#dingtalkBot?.markdown(
        //   '阿根廷请求Myqa失败报警(抖店)',
        //   [
        //     '### 阿根廷请求Myqa失败报警(抖店)',
        //     `> 客服: ${this.#userName}`,
        //     `> uid: ${uid}`,
        //     `> 问题: ${err}`
        //   ].join('\n\n')
        // );
      });

      // ctx.emit("update-status", {
      //   status: state,
      // });

      // 监听请求事件
      this.on('request', async (request) => {
        try {
          if (this.#agentStatue === 'running' || this.#isSilence) {
            const data = getCurrentUserMessageApi(request, this.#isSilence, () => {
              this.#systemStatusManager.addSendMsgCount();
            });
            if (data) {
              await this.userMsgCreate(data);
            }
          }
          // // 设置催付
          // setupReminders(
          //   request,
          //   this.#ctx.agentConfig,
          //   this.#isSilence,
          //   this.#userList,
          //   this.#ctx.log,
          //   (id, userList) => {
          //     this.#userList[id] = userList;
          //   },
          //   (uid, content, type) => {
          //     this.sendMessage(uid, content, type, 'rush');
          //   }
          // );

          // 客服状态
          if (request.url.includes('/backstage/uponlineservice')) {
            const postData = JSON.parse(request.postData ?? '[]');
            if (Object.keys(postData).length) {
              // this.#ctx.emit('update-status', {
              //   status: hfbStatusMap.get(postData.status)
              // });
              this.#ctx.log?.debug('客服状态切换', postData, hfbStatusMap.get(postData.status));
            }
          }
        } catch (err) {
          this.#ctx.log?.error(err);
        }
      });
      // 监听响应事件
      this.on('response', async (response) => {
        const { url } = response;
        // 获取当前用户订单信息
        if (url.includes('/backstage/cmpoent/order/query')) {
          try {
            const data = await response.json();
            this.#ctx.log?.debug(data?.[0]?.user_id, '获取订单信息成功', data);
            if (data.length > 0) {
              this.#userList[data[0].user_id] = {
                orderInfo: data,
                user_id: data[0].user_id
              };
            }
          } catch (err) {
            //err
          }
        }

        // 获取当前用户店铺信息
        if (url.includes('/backstage/currentuser')) {
          try {
            const data = await response.json();
            this.#shopInfo = data;
            this.#ctx.log?.debug('当前用户店铺信息', this.#shopInfo);
            const tabName = this.#shopInfo?.CustomerServiceInfo?.screen_name;
            if (tabName) {
              this.#ctx.emit('update-config', {
                csName: tabName
              });
            }
          } catch (err) {
            // err
          }
        }
        // 获取商品列表
        if (url.includes('/backstage/workstation/get_product_list')) {
          try {
            const data = await response.json();
            this.#ctx.goodsList = data;
            this.#ctx.log?.debug('获取商品信息成功');
            // 上传给myqa商品信息
            uploadGoodsInfo(
              formatGoodsList(this.#ctx.goodsList, this.#shopInfo.ShopName),
              this.#ctx,
              this.#shopInfo
            );
          } catch (err) {
            // err
          }
        }

        // 获取当前客服状态
        if (url.includes('/backstage/getMainCustomerServiceInfo')) {
          try {
            const data = await response.json();
            this.#ctx.log?.debug('获取当前客服状态信息', data);
            // 抖店 0 小休(忙碌) 1 在线  2 离线
            const OnlineStatus = data.OnlineStatus;
            this.#ctx.emit('update-status', {
              status: hfbStatusMap.get(OnlineStatus)
            });
          } catch (err) {
            // err
          }
        }
      });

      this.#webContentsView.webContents.debugger.sendCommand('Runtime.enable');

      // 监听即将导航的事件
      this.#webContentsView.webContents.on('will-navigate', async (_, url) => {
        // 如果 URL 包含指定的地址，则跳转到新的页面
        if (url.includes('https://fxg.jinritemai.com/ffa/mshop/homepage/index')) {
          const pigeonCid = await this.getPigeonCid();
          if (pigeonCid) {
            await this.#webContentsView.webContents.loadURL(
              `https://im.jinritemai.com/pc_seller_v2/main/workspace?selfId=${pigeonCid}`
            );
            // 切换执行状态
            await this.exposeFunction('updateAgentStatue', async (state) => {
              await updateAgentStatue(this, state);
              if (state === 'running') {
                this.#systemStatusManager.start();
              }
              if (state === 'pause') {
                this.#systemStatusManager.pause();
              }
              this.#agentStatue = state;
              console.log('执行了updateAgentStatue', state);
            });

            // // 获取当前客服数据
            // await this.exposeFunction('currentReceptInfo', async () => {
            //   this.#ctx.currentReceptInfoInterval = setInterval(async () => {
            //     const data = await currentReceptInfo(this);
            //     uploadBaseInfo(
            //       data,
            //       this.#ctx,
            //       this.#shopInfo,
            //       this.#systemStatusManager.getStatus()
            //     );
            //   }, 60 * 1000);
            // });
            // 模拟点击页面切换按钮抓取商品信息
            await this.evaluate(() => {
              const pigeonChatScrollBox: HTMLDivElement | null = document.querySelector(
                "div[data-workstation-side-tab-key='商品']"
              );
              if (pigeonChatScrollBox) {
                pigeonChatScrollBox.click();
              }
            });

            if (!this.#isSilence) {
              // 注入运行状态切换按钮
              await statusSwitchBtn(this);
            }

            // 设置发送内容并发送
            await this.exposeFunction('setTextAreaAndSend', async (uid, msg, agentConfig, type) => {
              await sendMessage(this, agentConfig, uid, msg, type);
            });
            // 切换锁
            await this.exposeFunction('toggleLock', async (state) => {
              await toggleLock(this, state);
            });

            // 创建用户消息
            await this.exposeFunction('userMsgCreate', async (postData) => {
              await this.userMsgCreate(postData);
            });

            // 转交用户
            await this.exposeFunction('transferUser', async (uid) => {
              this.#systemStatusManager.addTransferCount();
              const error = await transferUser(this, this.#ctx.agentConfig);
              if (error) {
                this.#ctx.log?.debug('转人工失败', uid, error);
                // this.#dingtalkBot?.markdown(
                //   '阿根廷转人工失败报警(抖店)',
                //   [
                //     '### 阿根廷转人工失败报警(抖店)',
                //     `> 客服: ${this.#userName}`,
                //     `> uid: ${uid}`,
                //     `> 问题: ${error}`
                //   ].join('\n\n')
                // );
              }
            });

            // 累计敏感词次数
            await this.exposeFunction('addSenceCount', async () => {
              this.#systemStatusManager.addSenceCount();
            });

            // 发送图片
            await this.exposeFunction('sendImage', async (imgurl) => {
              console.log('sendImage');

              // await sendImage(this, imgurl, this.#ctx.log);
              try {
                // 使用node-fetch获取图片数据
                const response = await fetch(imgurl);
                const arrayBuffer = await response.arrayBuffer();
                const buffer = Buffer.from(arrayBuffer);

                // 解析文件路径，并指定文件名
                const filePath = path.resolve(__dirname, `${Date.now()}.jpg`);
                fs.writeFileSync(filePath, buffer);

                // 获取文档结构
                const { root } =
                  await this.#webContentsView.webContents.debugger.sendCommand('DOM.getDocument');
                console.log('root:', root);

                // 查找文件上传的input[type=file]元素
                const { nodeId } = await this.#webContentsView.webContents.debugger.sendCommand(
                  'DOM.querySelector',
                  {
                    nodeId: root.nodeId,
                    selector: 'input[type="file"][accept=".png,.jpg,.jpeg,.gif"]' // CSS selector of input[type=file] element
                  }
                );
                // 将文件路径注入文件输入框
                await this.#webContentsView.webContents.debugger.sendCommand(
                  'DOM.setFileInputFiles',
                  {
                    nodeId,
                    files: [filePath]
                  }
                );

                const page = await this.getPptrPage();
                await page.evaluate(async () => {
                  // TODO fixme click需要参数
                  // page.click();
                });
                await this.evaluate(() => {
                  setTimeout(() => {
                    const button: HTMLDivElement | null = document.querySelector(
                      "div[data-qa-id='qa-send-img-popup-submit']"
                    );
                    if (button) {
                      button.click();
                    }
                  }, 1000);
                });

                console.log('文件上传成功');

                // 删除临时文件
                fs.unlinkSync(filePath);
              } catch (error) {
                console.error('上传失败:', error);
              }
            });

            // 是否需要转接
            await this.exposeFunction('needTransfer', (uid, content) => {
              return needTransfer(uid, content, this.#ctx.log);
            });

            // 获取新进线的对话消息
            await getSessionMessage(this);

            // 监听当前用户消息变化为接口监听兜底获取用户消息
            await getCurrentUesrMessage(this, this.#isSilence);

            await this.evaluate(() => {
              (window as any).updateAgentStatue('pause');
              (window as any).currentReceptInfo();
            });

            await this.evaluate(() => {
              console.log('开始了老弟');

              setTimeout(() => {
                (window as any).sendImage(
                  'https://fuss10.elemecdn.com/e/5d/4a731a90594a4af544c0c25941171jpeg.jpeg'
                );
              }, 10000);
            });
          }
        }
      });

      // // 监听页面导航事件
      // this.#page!.on('framenavigated', async (frame) => {
      //   const url = frame.url();
      //   if (url.startsWith('https://fxg.jinritemai.com/ffa/mshop/homepage/index')) {
      //     const pigeonCid = await this.getPigeonCid();
      //     if (pigeonCid) {
      //       await this.#page!.goto(
      //         `https://im.jinritemai.com/pc_seller_v2/main/workspace?selfId=${pigeonCid}`,
      //         {
      //           waitUntil: 'networkidle0'
      //         }
      //       );

      //       // 切换执行状态
      //       await this.#page!.exposeFunction('updateAgentStatue', async (state) => {
      //         await updateAgentStatue(this.#page, state);
      //         if (state === 'running') {
      //           this.#systemStatusManager.start();
      //         }
      //         if (state === 'pause') {
      //           this.#systemStatusManager.pause();
      //         }
      //         this.#agentStatue = state;
      //       });

      //       // 获取当前客服数据
      //       await this.#page!.exposeFunction('currentReceptInfo', async () => {
      //         this.#ctx.currentReceptInfoInterval = setInterval(async () => {
      //           const data = await currentReceptInfo(this.#page);
      //           uploadBaseInfo(
      //             data,
      //             this.#ctx,
      //             this.#shopInfo,
      //             this.#systemStatusManager.getStatus()
      //           );
      //         }, 60 * 1000);
      //       });

      //       // 模拟点击页面切换按钮抓取商品信息
      //       await this.#page!.evaluate(() => {
      //         const pigeonChatScrollBox: HTMLDivElement | null = document.querySelector(
      //           "div[data-workstation-side-tab-key='商品']"
      //         );
      //         if (pigeonChatScrollBox) {
      //           pigeonChatScrollBox.click();
      //         }
      //       });

      //       if (!this.#isSilence) {
      //         // 注入运行状态切换按钮
      //         statusSwitchBtn(this.#page);
      //       }

      //       // 设置发送内容并发送
      //       await this.#page!.exposeFunction(
      //         'setTextAreaAndSend',
      //         async (uid, msg, agentConfig, type) => {
      //           await sendMessage(this.#page, agentConfig, uid, msg, type);
      //         }
      //       );

      //       // 切换锁
      //       await this.#page!.exposeFunction('toggleLock', async (state) => {
      //         await toggleLock(this.#page, state);
      //       });

      //       // 创建用户消息
      //       await this.#page!.exposeFunction('userMsgCreate', async (postData) => {
      //         await this.userMsgCreate(postData);
      //       });

      //       // 转交用户
      //       await this.#page!.exposeFunction('transferUser', async (uid) => {
      //         this.#systemStatusManager.addTransferCount();
      //         const error = await transferUser(this.#page, this.#ctx.agentConfig);
      //         if (error) {
      //           this.#ctx.log?.debug('转人工失败', uid, error);
      //           // this.#dingtalkBot?.markdown(
      //           //   '阿根廷转人工失败报警(抖店)',
      //           //   [
      //           //     '### 阿根廷转人工失败报警(抖店)',
      //           //     `> 客服: ${this.#userName}`,
      //           //     `> uid: ${uid}`,
      //           //     `> 问题: ${error}`
      //           //   ].join('\n\n')
      //           // );
      //         }
      //       });

      //       // 累计敏感词次数
      //       await this.#page!.exposeFunction('addSenceCount', async () => {
      //         this.#systemStatusManager.addSenceCount();
      //       });

      //       // 发送图片
      //       await this.#page!.exposeFunction('sendImage', async (imgurl) => {
      //         await sendImage(this.#page, imgurl, this.#ctx.log);
      //       });

      //       // 是否需要转接
      //       await this.#page!.exposeFunction('needTransfer', (uid, content) => {
      //         return needTransfer(uid, content, this.#ctx.log);
      //       });

      //       // 获取新进线的对话消息
      //       await getSessionMessage(this.#page);

      //       // 监听当前用户消息变化为接口监听兜底获取用户消息
      //       await getCurrentUesrMessage(this.#page, this.#isSilence);

      //       await this.#page!.evaluate(() => {
      //         // window.updateAgentStatue('pause');
      //         (window as any).currentReceptInfo();
      //       });
      //     }
      //   }
      // });
    } catch (err) {
      console.error('Error in run method:', err);
      this.#systemStatusManager.addUnhandledRejectionCount();
    } finally {
      this.#runningPromise.resolve();
    }
  }

  async getPigeonCid(): Promise<string | undefined> {
    try {
      const data = await this.runCodeInView('localStorage.getItem("__tea_sdk_ab_version_512225");');
      const uuid = JSON.parse(data ?? '{}')?.uuid;
      return uuid;
    } catch (err) {
      this.#ctx.log?.error('跳转至客服页面失败', err);
      return undefined;
    }
  }

  async doStop() {
    try {
      await this.#runningPromise.promise;
      clearInterval(this.#ctx.currentReceptInfoInterval);
    } catch (err) {
      console.error('Error in stop method:', err);
    } finally {
      this.#config = undefined;
    }
  }
}

export default Doudian;
