/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import style from './index.module.css';
import dayjs from 'dayjs';
import { Tooltip } from 'antd';
import userAvatar from '@/assets/icons/user_avatar.svg';

type Props = {
  active?: boolean;
  data: any;
  extract: (val: boolean) => void;
};
const ListItem: React.FC<Props> = ({ active, data }) => {
  const flexStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center'
  };
  const stateMap = new Map()
    .set('cls', '#A344FF')
    .set('original', '#44D961')
    .set('reject', '#FF4444')
    .set('reason_fail', '#FFA344')
    .set('rag', '#086eff');

  const stateTipMap = new Map()
    .set('rag', '知识学习')
    .set('original', '原话回复')
    .set('reject', '失败')
    .set('reason_fail', '推理失败')
    .set('cls', '预设消息回复');
  return (
    <div className={`${style.item} ${active ? style.active : ''}`}>
      <Tooltip
        title={stateTipMap.get(data?.answer_type) ?? '未知状态'}
        color={stateMap.get(data?.answer_type) ?? '#808080'}
      >
        <div
          className={style.state}
          style={{
            backgroundColor: stateMap.get(data?.answer_type) ?? '#808080'
          }}
        />
      </Tooltip>

      <div style={flexStyle}>
        <div style={flexStyle}>
          <img className={style.icon} src={userAvatar} />
          <div className={style.title}>{data?.origin?.metadata_?.uid ?? ''}</div>
        </div>
        {/* <div className={style.tip}>消耗150算力</div> */}
      </div>
      <div style={flexStyle}>
        <div className={style.time}>
          {dayjs(data?.origin?.created_at).format('YYYY/MM/DD HH:mm:ss')}
        </div>
        {/* {active && (
          <div
            style={{
              color: "#2E74FF",
              position: "absolute",
              bottom: "10px",
              right: "16px",
              cursor: "pointer",
            }}
            onClick={(e) => {
              extract(true);
              e.stopPropagation();
            }}
          >
            {data?.origin?.metadata_?.extract ? "重新提取" : "提取"}
          </div>
        )} */}
      </div>
    </div>
  );
};

export default ListItem;
