import { app, MenuItemConstructorOptions, shell } from 'electron';

const isMac = process.platform === 'darwin';

interface CustomMenu {
  index: number;
  deleteCount: number;
  menu: MenuItemConstructorOptions;
}

interface CustomMenuItem {
  menuIndex: number;
  index: number;
  deleteCount: number;
  menu: MenuItemConstructorOptions;
}

export default function menuTemplate(menu: CustomMenu[] = [], menuItem: CustomMenuItem[] = []) {
  const template = [
    // { role: 'appMenu' }
    ...(isMac
      ? [
          {
            label: app.name,
            submenu: [
              { role: 'about' },
              { type: 'separator' },
              { role: 'services' },
              { type: 'separator' },
              { role: 'hide' },
              { role: 'hideOthers' },
              { role: 'unhide' },
              { type: 'separator' },
              { role: 'quit' }
            ]
          }
        ]
      : []),
    // { role: 'fileMenu' }
    {
      label: 'File',
      submenu: [isMac ? { role: 'close' } : { role: 'quit' }]
    },
    // { role: 'editMenu' }
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        ...(isMac
          ? [
              { role: 'pasteAndMatchStyle' },
              { role: 'delete' },
              { role: 'selectAll' },
              { type: 'separator' },
              {
                label: 'Speech',
                submenu: [{ role: 'startSpeaking' }, { role: 'stopSpeaking' }]
              }
            ]
          : [{ role: 'delete' }, { type: 'separator' }, { role: 'selectAll' }])
      ]
    },
    // { role: 'viewMenu' }
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { role: 'toggleDevTools' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    // { role: 'windowMenu' }
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'zoom' },
        ...(isMac
          ? [{ type: 'separator' }, { role: 'front' }, { type: 'separator' }, { role: 'window' }]
          : [{ role: 'close' }])
      ]
    },
    ...(isMac
      ? []
      : [
          {
            role: 'help',
            submenu: [
              {
                role: 'about'
              }
            ]
          }
        ])
  ] as unknown as MenuItemConstructorOptions[];

  menu.forEach((m) => {
    template.splice(m.index, m.deleteCount, m.menu);
  });

  menuItem.forEach((m) => {
    (template[m.menuIndex].submenu as MenuItemConstructorOptions[]).splice(
      m.index,
      m.deleteCount,
      m.menu
    );
  });

  return template;
}
