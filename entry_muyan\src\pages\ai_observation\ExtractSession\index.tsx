import { observer } from 'mobx-react-lite';
import Modal from '@/components/Modal';
import { useEffect, useRef, useState } from 'react';
import { ToBewithdrawn } from './ToBewithdrawn';
import { Knowledge } from './Knowledge';
import { store } from '@/store';
import { message, Button } from 'antd';
import cloneDeep from 'lodash-es/cloneDeep';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import { getAssistants } from '@/api/myqaApi';
import { getFileItem, updateAssistant, updateThread, getThread } from '@/api/myqaApi';
type Props = {
  open: boolean;
  currentThread: any;
  messageData: any[];
  onOpenChange: (open: boolean) => void;
  onSubmit: () => void;
};
export const ExtractSession = observer(
  ({ open, onOpenChange, currentThread, messageData, onSubmit }: Props) => {
    const { knowledge, extractSession } = store;
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [obj, setObj] = useState<any>(null);
    const [kb_name, setKbName] = useState('');
    const knowledgeRef = useRef<any>(null);

    useEffect(() => {
      if (open) {
        getAssistants().then((res) => {
          const item =
            res.data.find((item) => item.id === localStorage.getItem('assistantId')) ?? {};
          if (item?.file_ids) {
            getFileItem(item?.file_ids[0]).then((fileItem: any) => {
              setObj(fileItem);
              setKbName(fileItem.filename.split('.json')[0]);
            });
          }
        });
      }
      setObj(null);
    }, [open]);
    function onChange(val: boolean) {
      if (knowledge.modifier > 0) {
        ConfirmationDialog({
          title: '是否关闭?关闭弹窗后此次修改不会被保存!'
        })
          .then(() => {
            onOpenChange(val);
            if (!val) {
              clear();
            }
          })
          .catch(() => {});
      } else {
        onOpenChange(val);
        if (!val) {
          clear();
        }
      }
    }

    function clear() {
      knowledge.setModifier(0);
      knowledge.setList([]);
      knowledge.setVersionId('');
      knowledge.deleteKb(kb_name, knowledge.versionId);
      extractSession.setIntentionList([]);
      extractSession.setEditList([]);
      extractSession.setCurrentIntentionId('');
      extractSession.setNewIntention([]);
      extractSession.setCurrenQuerytList([]);
    }
    function submit() {
      setLoading(true);
      knowledge
        .getDocPath(kb_name, kb_name)
        .then(() => {
          const metadata = cloneDeep(obj?.metadata ?? {});
          delete metadata.embeddings;
          getAssistants()
            .then((assRes) => {
              knowledge
                .knowledge({
                  file_name: kb_name,
                  embeddings: obj.metadata.embeddings,
                  metadata: metadata
                })
                .then((resq) => {
                  const data =
                    assRes.data.find((item) => item.id === localStorage.getItem('assistantId')) ??
                    {};
                  if (Array.isArray(data?.file_ids)) {
                    data?.file_ids.splice(data?.file_ids?.indexOf(obj.id), 1, resq.id);
                  }
                  updateAssistant(data)
                    .then(() => {
                      knowledge.setModifier(0);
                      messageApi.success('更新知识库成功');
                      getThread(currentThread.id).then((thres: any) => {
                        updateThread(currentThread.id, {
                          ...thres.metadata,
                          extract: true,
                          intent_ids: extractSession.intentionList,
                          editList: extractSession.editList
                        }).then(() => {
                          onChange(false);
                          onSubmit();
                        });
                      });
                    })
                    .finally(() => {
                      setLoading(false);
                    });
                });
            })
            .catch(() => {
              messageApi.error('更新知识库失败');
              setLoading(false);
            });
        })
        .catch(() => {
          messageApi.error('更新知识库失败');
          setLoading(false);
        });
    }
    function reset() {
      setLoading(true);
      knowledge
        .deleteKb(kb_name, knowledge.versionId)
        .then(() => {
          if (knowledgeRef.current) {
            extractSession.setEditList([]);
            knowledgeRef.current.init();
            extractSession.setCurrentIntentionId('');
            extractSession.setNewIntention([]);
            extractSession.setIntentionList([]);
            knowledge.setModifier(0);
            knowledge.setVersionId('');
            extractSession.setCurrenQuerytList([]);
          }
        })
        .finally(() => {
          setLoading(false);
        });
    }
    return (
      <Modal
        title="知识学习 - 提取"
        width={'95%'}
        footer={false}
        open={open}
        setOpen={onChange}
        top="20px"
      >
        <div
          style={{
            padding: '0px 20px 20px'
          }}
        >
          {contextHolder}
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between'
            }}
          >
            {open && <ToBewithdrawn kb_name={kb_name} messageData={messageData} />}
            {open && obj && (
              <Knowledge
                ref={knowledgeRef}
                obj={obj}
                kb_name={kb_name}
                currentThread={currentThread}
              />
            )}
          </div>
          <div
            style={{
              display: 'flex',
              justifyContent: 'center'
            }}
          >
            <Button
              type="default"
              onClick={reset}
              style={{
                width: '60px',
                marginRight: '10px'
              }}
              disabled={loading || !knowledge.versionId}
            >
              重置
            </Button>
            <Button
              type="primary"
              onClick={submit}
              style={{
                width: '90px',
                marginRight: '10px'
              }}
              disabled={loading || !knowledge.versionId}
            >
              {loading ? '提取中...' : '完成提取'}
            </Button>
            <Button
              type="default"
              onClick={() => {
                onChange(false);
              }}
              style={{
                width: '60px'
              }}
              disabled={loading}
            >
              关闭
            </Button>
          </div>
        </div>
      </Modal>
    );
  }
);
