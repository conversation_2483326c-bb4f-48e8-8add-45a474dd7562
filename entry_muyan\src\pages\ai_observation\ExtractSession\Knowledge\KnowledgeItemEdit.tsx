'use client';
import { useEffect, useState } from 'react';
import { observer } from 'mobx-react-lite';
import { store } from '@/store';
import { message, Input } from 'antd';
import Modal from '@/components/Modal';
import Button from '@/components/Button';
type Props = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: string;
  data: any;
  product_qa_id: string;
  kb_name: string;
};
export const KnowledgeItemEdit = observer(
  ({ open, onOpenChange, type, data, product_qa_id, kb_name }: Props) => {
    const { knowledge } = store;
    const [loading, setLoading] = useState(false);
    const [messageApi, contextHolder] = message.useMessage();
    const [messageText, setMessage] = useState('');
    const [responseData, setResponseData] = useState({
      kb_category: '',
      item_id: [],
      shop_name: [],
      attr_info: {}
    });
    useEffect(() => {
      if (data.value) setMessage(data.value);
      if (data.responseItem) {
        setResponseData({
          kb_category: data.responseItem?.kb_category ?? '',
          item_id: data.responseItem?.pid ?? [],
          shop_name: data.responseItem?.shop_name ?? [],
          attr_info: data.responseItem?.attr_info ?? {}
        });
      } else {
        setResponseData({
          kb_category: '',
          item_id: [],
          shop_name: [],
          attr_info: {}
        });
      }
    }, [data]);

    const onChange = (val: boolean) => {
      if (!val) setMessage('');
      onOpenChange(val);
    };

    const submit = () => {
      setLoading(true);
      let http;
      switch (type) {
        case 'responseUpdate':
          http = knowledge.updateResponse(
            {
              product_qa_id,
              response: messageText,
              response_id: data.response_id,
              ...responseData
            },
            data.id
          );
          break;
        case 'responseAdd':
          http = knowledge.addResponse({
            product_qa_id,
            response: messageText,
            ...responseData
          });
          break;
        case 'queryUpdate':
          http = knowledge.updateProductQuery({
            product_qa_id,
            query: messageText,
            id: data.id,
            product_id: '',
            cate_id: '',
            query_id: data.query_id,
            meta_data: {}
          });
          break;
        case 'queryAdd':
          http = knowledge.addProductQuery({
            product_qa_id,
            query: messageText,
            product_id: '',
            cate_id: ''
          });
          break;
        case 'termsUpdate':
          http = knowledge.updateProductQa({
            product_id: undefined,
            terms: messageText,
            answer_rule: undefined,
            cate_kb_id: undefined,
            cate_id: undefined,
            kb_name: kb_name,
            id: data.id,
            intent_id: data.intent_id
          });
          break;
        default:
          setLoading(false);
          return;
      }
      http
        ?.then((res) => {
          if (['queryUpdate', 'queryAdd'].includes(type)) {
            knowledge.querySim(res.data?.query_id ?? data.query_id ?? '').then(() => {
              knowledge.loadDomain(kb_name, knowledge.versionId).then((res) => {
                knowledge.setList(res.data);
              });
            });
          }
          knowledge.loadDomain(kb_name, knowledge.versionId).then((res) => {
            knowledge.setList(res.data);
          });
          onOpenChange(false);
          setMessage('');
          knowledge.setModifier(knowledge.modifier + 1);
        })
        ?.catch(() => {
          messageApi.open({
            type: 'error',
            content: 'error'
          });
        })
        .finally(() => {
          setLoading(false);
        });
    };
    return (
      <Modal title={data.title ?? ''} open={open} setOpen={onChange}>
        {contextHolder}
        <Input.TextArea
          placeholder="请输入"
          onChange={(e) => {
            setMessage(e.target.value);
          }}
        />
        <Button type="primary" onClick={submit} disabled={!messageText || loading}>
          {loading ? '保存中...' : '保存'}
        </Button>
      </Modal>
    );
  }
);
