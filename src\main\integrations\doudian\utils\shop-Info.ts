import axios from 'axios';
import ms from 'ms';
export const getShopInfo = async () => {};
export const uploadGoodsInfo = async (goodsList: any[], ctx, shopInfo) => {
  const endpoint = ctx.env.endpoint;
  const url = `${endpoint}/myoracle/platform/batch`;
  const { ShopId, ShopName } = shopInfo || {};
  const data = {
    loglist: [
      {
        assistant_id: ctx.env.assistantId,
        platform: 'doudian',
        batch_id: Date.now(),
        mall_id: ShopId + '',
        mall_name: ShopName,
        data_type: 'product',
        meta_data: { goodsList }
      }
    ]
  };
  const res = await axios.post(url, data, {
    responseType: 'json',
    timeout: ms('30s')
  });
  ctx.log.debug('上传商品信息成功', res.data);
  ctx.emit('sync_goods_state', {
    state: 'upload_goods_done',
    mallId: ShopId,
    mallName: ShopName
  });
};

export const formatGoodsList = (goodsList, shopName) => {
  return goodsList.map((item) => {
    const product_item = item.product_item;
    return {
      goods_id: product_item.product_id,
      mall_name: shopName,
      goods_name: product_item.product_base_info.title,
      skus: product_item.sku_info.sku_ids.map((skuid) => {
        return {
          sku_id: skuid,
          spec: [],
          is_onsale: 1,
          gantanhao: {},
          kedang: {}
        };
      })
    };
  });
};
