/* eslint-disable @typescript-eslint/no-explicit-any */
import Item from './Item';
// import Modal from "../../../components/Modal";
import { useEffect, useState } from 'react';
import style from './index.module.css';
// import { Select } from "antd";
import { getProduct } from '@/api/myqaApi';
import { getPageAll } from './getPageAll';
import { Empty, Spin } from 'antd';
import default_content from '@/assets/imgs/default_content.png';
type Props = {
  setKey: (key: string) => void;
  shopKey: string;
  setShopKey: (key: string) => void;
};
const MyShop: React.FC<Props> = ({ setKey, setShopKey }: Props) => {
  const [meta, setMeta] = useState<any[]>([]);
  const handleOpen = (id: string): void => {
    setKey('3');
    setShopKey(id);
  };
  const [shopData, setShopData] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    getPageAll(1000, 1, getProduct, (res) => res.data.data).then((res: any[]) => {
      setMeta(res);
      setShopData([
        ...new Set(
          res
            .filter((item) => item?.meta_data?.mall_name)
            .map((item) => item?.meta_data?.mall_name ?? '')
        )
      ]);
      setLoading(false);
    });
  }, []);
  return (
    <div
      style={{
        height: '100%',
        padding: '20px',
        width: '100%',
        backgroundColor: '#FFFFFF'
      }}
    >
      <div className={style.titleCss}>我的店铺</div>
      <div className={style.list}>
        {shopData.map((item, index) => {
          return (
            <Item
              key={index}
              title={item}
              total={
                meta.filter((i) => {
                  return i?.meta_data?.mall_name === item && i?.meta_data?.sub_type === '1';
                }).length
              }
              handleOpen={handleOpen}
            />
          );
        })}
      </div>
      {shopData.length === 0 && (
        <Spin spinning={loading}>
          <Empty
            description={
              <div
                style={{
                  fontSize: '12px',
                  color: '#666666',
                  marginTop: '10px'
                }}
              >
                暂无店铺
              </div>
            }
            style={{
              marginTop: '15%'
            }}
            imageStyle={{
              width: '160px',
              height: '160px',
              display: 'inline-block'
            }}
            image={default_content}
          />
        </Spin>
      )}

      {/* <Modal open={open} setOpen={setOpen} title="设置主推商品" width={500}>
        <div
          className="filter"
          style={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            margin: "40px 0px 48px",
          }}
        >
          <div className={style.label}>选择商品:</div>
          <Select
            defaultValue="lucy"
            style={{ width: 268 }}
            onChange={handleChange}
            options={[
              { value: "jack", label: "Jack" },
              { value: "lucy", label: "Lucy" },
              { value: "Yiminghe", label: "yiminghe" },
              { value: "disabled", label: "Disabled", disabled: true },
            ]}
          />
        </div>
      </Modal> */}
    </div>
  );
};

export default MyShop;
