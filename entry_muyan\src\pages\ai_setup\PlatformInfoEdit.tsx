/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { PlatformInfoEditUploadImg } from './PlatformInfoEditUploadImg';
import { Table, Tooltip } from 'antd';
import cloneDeep from 'lodash-es/cloneDeep';
import { useState } from 'react';
import PlatformInfoEditGoodSpec from './PlatformInfoEditGoodSpec';
import type { TableProps } from 'antd';
type PlatformInfo = {
  aiGoodSku: string;
  aiGoodSkuid: string;
  pcode: string;
  img: string;
  aiGoodSpec: string;
  mall_name: string;
  item_id: number;
};
type Props = {
  platform_info: PlatformInfo[];
  onChange: (value: PlatformInfo[]) => void;
  attribute: any[];
};
const linkColorStyle: any = {
  color: '#2E74FF',
  cursor: 'pointer',
  maxWidth: '150px',
  textOverflow: 'ellipsis',
  overflow: 'hidden',
  textWrap: 'nowrap',
  display: 'inline-block'
};
const PlatformInfoEdit: React.FC<Props> = ({ platform_info, onChange, attribute }: Props) => {
  const [value, setValue] = useState<PlatformInfo[]>(cloneDeep(platform_info));
  const [editIndex, setEditIndex] = useState(0);
  const [editValue, setEditValue] = useState<string>('');
  const imgChange = (index, url): void => {
    setValue((prevValue) => {
      const newValue = [...prevValue];
      newValue[index] = { ...newValue[index], img: url };
      onChange(newValue);
      return newValue;
    });
  };

  const inputChange = (index, val): void => {
    setValue((prevValue) => {
      const newValue = [...prevValue];
      newValue[index] = { ...newValue[index], aiGoodSpec: val };
      onChange(newValue);
      return newValue;
    });
  };
  const [goodSpecOpen, setGoodSpecOpen] = useState(false);
  if (value.length === 0) {
    return <></>; // 处理空数组的情况
  }
  const columns: TableProps['columns'] = [
    {
      title: '店铺名称',
      dataIndex: 'mall_name',
      key: 'mall_name',

      render: (_, record) => <span style={{ minWidth: 150 }}>{record.mall_name}</span>
    },
    {
      title: '商品名称',
      dataIndex: 'aiGoodSku',
      key: 'aiGoodSku'
    },
    {
      title: '商品规格',
      dataIndex: 'aiGoodSpec',
      key: 'aiGoodSpec',
      render: (_, record, index) => (
        <Tooltip title={record.aiGoodSpec}>
          <span
            style={linkColorStyle}
            onClick={() => {
              setEditIndex(index);
              setEditValue(record.aiGoodSpec);
              setGoodSpecOpen(true);
            }}
          >
            {record.aiGoodSpec}
          </span>
        </Tooltip>
      )
    },

    {
      title: '商品图片',
      key: 'img',
      render: (_, record, index) => (
        <PlatformInfoEditUploadImg
          value={record.img ? [{ url: record.img, type: 'image/jpeg' }] : []}
          onChange={(value) => {
            imgChange(index, value?.[0]?.url ?? '');
          }}
        />
      )
    },
    {
      title: '商品SKU',
      dataIndex: 'aiGoodSkuid',
      key: 'aiGoodSkuid'
    },
    {
      title: '商品ID',
      dataIndex: 'item_id',
      key: 'item_id'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record, index) => (
        <span
          style={linkColorStyle}
          onClick={() => {
            setEditIndex(index);
            setEditValue(record.aiGoodSpec);
            setGoodSpecOpen(true);
          }}
        >
          编辑商品规格
        </span>
      )
    }
  ];

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'start',
        marginBottom: '20px'
      }}
    >
      <PlatformInfoEditGoodSpec
        open={goodSpecOpen}
        setOpen={setGoodSpecOpen}
        data={attribute}
        value={editValue}
        onChange={(value) => {
          inputChange(editIndex, value);
        }}
      />
      {/* <div
        style={{
          minWidth: 10,
          maxWidth: 500,
          width: "20%",
          textAlign: "right",
          marginRight: "8px",
        }}
      >
        平台信息:
      </div> */}
      {/* <div
        style={{
          flex: 1,
        }}
      >
        {value.map((item, index) => {
          return (
            <div key={item.aiGoodSkuid}>
              <div
                style={{
                  marginBottom: "20px",
                }}
              >
                店铺名称: &nbsp; {item.mall_name}
              </div>
              <div
                style={{
                  marginBottom: "20px",
                }}
              >
                商品名称: &nbsp; {item.aiGoodSku}
              </div>
              <div
                style={{
                  marginBottom: "20px",
                }}
              >
                商品SKU: &nbsp; {item.aiGoodSkuid}
              </div>
              <div
                style={{
                  marginBottom: "20px",
                }}
              >
                商品ID: &nbsp; {item.item_id}
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "20px",
                  width: "80%",
                }}
              >
                <div
                  style={{
                    width: "80px",
                  }}
                >
                  商品规格:
                </div>
                <span
                  style={{
                    color: "#1890ff",
                    cursor: "pointer",
                  }}
                  onClick={() => {
                    setEditIndex(index);
                    setEditValue(item.aiGoodSpec);
                    setGoodSpecOpen(true);
                  }}
                >
                  {item.aiGoodSpec || "点击添加"}
                </span>
              </div>
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  marginBottom: "20px",
                  width: "80%",
                }}
              >
                <div
                  style={{
                    width: "80px",
                  }}
                >
                  商品图片:
                </div>
                <PlatformInfoEditUploadImg
                  value={
                    item.img ? [{ url: item.img, type: "image/jpeg" }] : []
                  }
                  onChange={(value) => {
                    imgChange(index, value?.[0]?.url ?? "");
                  }}
                />
              </div>
            </div>
          );
        })}
      </div> */}
      <Table
        columns={columns}
        style={{ width: '100%' }}
        scroll={{ x: '600' }}
        dataSource={platform_info}
      />
    </div>
  );
};
export default PlatformInfoEdit;
