import TabsTime from './tabs_time/index';
import { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import CustomerServiceOverview from './CustomerServiceOverview';
// import { Select } from "antd";
import { Chat } from './chat';
import React from 'react';
import { Tabs } from 'antd';
import type { TabsProps } from 'antd';
import Table from './Table';
import { getAnalysis } from '@/api/myqaApi';
import Default from './Default';
import ai_fenxi from '@/assets/imgs/ai_fenxi.jpg';
const Data: React.FC = () => {
  const [time, setTime] = useState<any>([
    dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD')),
    dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD'))
  ]);
  const [sqllab, setSqllab] = useState<any>({});
  useEffect(() => {
    getAnalysis({
      start: time[0].format('YYYY-MM-DD'),
      end: time[1].format('YYYY-MM-DD')
    }).then((res: any) => {
      if (time[0].valueOf() - time[1].valueOf() === 0) {
        setSqllab(JSON.parse(res.hourlab));
      } else {
        setSqllab(JSON.parse(res.sqllab));
      }
    });
  }, [time]);

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: (
        <div
          style={{
            padding: '0px 10px'
          }}
        >
          折线图
        </div>
      ),
      children: (
        <>
          {(sqllab?.data ?? []).length > 0 ? (
            <Chat data={sqllab?.data ?? []} isDay={time[0].valueOf() - time[1].valueOf() === 0} />
          ) : (
            <Default />
          )}
        </>
      )
    },
    {
      key: '2',
      label: (
        <div
          style={{
            padding: '0px 10px'
          }}
        >
          表单
        </div>
      ),
      children: (
        <>
          {(sqllab?.data ?? []).length > 0 ? (
            <Table
              data={sqllab?.data ?? []}
              time={time}
              isDay={time[0].valueOf() - time[1].valueOf() === 0}
            />
          ) : (
            <Default />
          )}
        </>
      )
    }
  ];
  const sumValuesByKey = (data) => {
    const totals = {};
    data.forEach((record) => {
      for (const key in record) {
        if (typeof record[key] === 'number') {
          if (!totals[key]) {
            totals[key] = 0;
          }
          totals[key] += record[key];
        }
      }
    });
    return totals;
  };

  return (
    <div
      style={{
        padding: '10px 20px 0px',
        backgroundColor: '#ffffff',
        height: '100%'
      }}
    >
      <div
        style={{
          display: 'flex',
          alignContent: 'center'
          // justifyContent: "space-between",
        }}
      >
        <TabsTime value={time} onChange={setTime} />
        <img
          src={ai_fenxi}
          alt="ai_fenxi"
          style={{
            width: '59px',
            height: '26px',
            cursor: 'pointer',
            marginTop: '14px',
            marginLeft: '10px'
          }}
          onClick={() => {
            window.open(
              'https://sso.superset.dongchacat.cn/superset/dashboard/2/?standalone=true',
              '_blank',
              'noopener'
            );
          }}
        />
        {/* <Select
          style={{
            width: "240px",
            marginTop: "20px",
          }}
          defaultValue="pdd"
          options={[{ value: "pdd", label: "对话平台（拼多多）" }]}
        /> */}
      </div>
      <CustomerServiceOverview data={sumValuesByKey(sqllab?.data ?? [])} />
      <Tabs
        defaultActiveKey="1"
        items={items}
        indicator={{ size: (origin) => origin - 30, align: 'center' }}
      />
    </div>
  );
};

export default Data;
