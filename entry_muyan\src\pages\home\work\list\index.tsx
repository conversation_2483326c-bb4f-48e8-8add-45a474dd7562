import React from 'react';
import style from './index.module.css';
import Item from './Item';
import { Empty, Col, Row } from 'antd';
import default_content from '@/assets/imgs/default_content.png';

type Props = {
  data: any[];
  getData: (val?: string) => void;
  updateConfigState: (id: string, state) => void;
};

const List: React.FC<Props> = ({ data, getData, updateConfigState }) => {
  return (
    <div className={style.list}>
      <Row
        gutter={[16, 16]}
        style={{
          width: '101.2%'
        }}
      >
        {data.map((config: any) => (
          <Col key={config.id + config.name} span={6}>
            <Item config={config} getData={getData} updateConfigState={updateConfigState} />
          </Col>
        ))}
      </Row>
      {data.length === 0 && (
        <div
          style={{
            width: '160px',
            margin: '0 auto',
            paddingTop: '100px'
          }}
        >
          <Empty
            image={default_content}
            imageStyle={{
              height: 160
            }}
            description={
              <span
                style={{
                  fontSize: '12px',
                  color: '#666666',
                  lineHeight: '18px'
                }}
              >
                暂无内容
              </span>
            }
          />
        </div>
      )}
    </div>
  );
};

export default List;
