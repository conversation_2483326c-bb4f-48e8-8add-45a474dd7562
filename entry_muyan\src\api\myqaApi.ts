/* eslint-disable @typescript-eslint/no-explicit-any */
import service from './utils/service';
import userService from './utils/userService';
import { AxiosResponse } from 'axios';
const ADMIN_SK = 'sk-eovwFUmKyTbp0I7b5CbtSM6Ck8xO6H5xsxveKGndvsGkz1b9cSQxMpFozpP5T0nG';
// 获取thread列表
export const threadSearch = (data: { start_time; end_time }): Promise<AxiosResponse<any, any>> => {
  return service.post('/debug/thread/search', {
    source: ['agent'],
    thumb: [1, 2, -1],
    extract: undefined,
    reject: ['no_recall', 'infer_fail', 'other_reject', 'ai_sovle', 'ai_error'],
    assistant_id: localStorage.getItem('assistantId'),
    start_time: data.start_time ?? null,
    end_time: data.end_time ?? null
  });
};

// 获取静默开线列表
export const threadBesilentSearch = (data: {
  start_time;
  end_time;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/debug/thread/search', {
    source: ['manual'],
    thumb: [1, 2, -1],
    extract: undefined,
    reject: ['no_recall', 'infer_fail', 'other_reject', 'ai_sovle', 'ai_error'],
    assistant_id: localStorage.getItem('assistantId'),
    start_time: data.start_time ?? null,
    end_time: data.end_time ?? null
  });
};

export const getMessagesItem = (id: string) => {
  return service.get(`/debug/messages/${id}`);
};

// 获取消息列表
export const getMessages = (thread_id): Promise<AxiosResponse<any, any>> => {
  return service.get('/debug/thread/messages', {
    params: { thread_id }
  });
};

// 获取消息的metadata
export const getMessagesMetadata = (data: {
  thread_id;
  message_id;
}): Promise<AxiosResponse<any, any>> => {
  return service.get('/debug/thread/metadata', { params: data });
};

// 获取助手
export const getAssistants = (): Promise<AxiosResponse<any, any>> => {
  return service.get('/assistants');
};

// 获取id搜索
export const searchAll = (data: {
  search: string;
  assistant_id: string;
  run_id: string;
  thread_id: string;
  message_id: string;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/debug/search_all/messages', data);
};

export const createUser = (data: {
  username: string;
  password: string;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/users', data, {
    headers: {
      Authorization: `Bearer ${ADMIN_SK}`
    }
  });
};

export const deleteMyqaUser = (username: string): Promise<AxiosResponse<any, any>> => {
  return service.delete(`/users/${username}`, {
    headers: {
      Authorization: `Bearer ${ADMIN_SK}`
    }
  });
};

export const myqalogin = (data: { username; password }): Promise<AxiosResponse<any, any>> => {
  return service.post(`/users/${data.username}/login`, data);
};

// 创建助手
export const createAssistants = (data): Promise<AxiosResponse<any, any>> => {
  return service.post('/assistants', data);
};

/** 添加阿根廷版本 */
export const addAgentium = ({
  version,
  name,
  assistant_id,
  meta_data
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/workbench/agentium', {
    version,
    name,
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
    assistant_id,
    meta_data
  });
};
/** 获取阿根廷配置 */
export const getAgentium = ({
  page_num,
  page_size,
  assistant_id
}): Promise<AxiosResponse<any, any>> => {
  return service.get('/workbench/agentium', {
    params: {
      page_num,
      page_size,
      assistant_id
    }
  });
};

// 获取商品列表
export const getProduct = ({ page_num, page_size, title }): Promise<AxiosResponse<any, any>> => {
  return service.get('/myoracle/product', {
    params: {
      page_num,
      page_size,
      pid: '',
      title,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    },
    withCredentials: true
  });
};
// cookie.load("user")?.id ??

/** 知识库编辑商品 */
export const updateProduct = (
  id: string,
  data: {
    title: string;
    page_title: string;
    item_id: string;
    price: string;
    category: string;
    properties: string;
    code: string;
    shop_id: string;
    children: any;
    version_id: string;
  }
): Promise<AxiosResponse<any, any>> => {
  return service.put(`/myoracle/product?id=${id}`, {
    ...data,
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
  });
};

export const getAdminAssistantsOne = (): Promise<AxiosResponse<any, any>> => {
  return service.get('/assistants', {
    headers: {
      Authorization: `Bearer ${ADMIN_SK}`
    }
  });
};
export const getAdminAgentiumOne = (assistant_id): Promise<AxiosResponse<any, any>> => {
  return service.get('/workbench/agentium', {
    params: {
      page_num: 1,
      page_size: 1,
      assistant_id
    }
  });
};

export const copyAdminAssistants = (user_id, org_id): Promise<AxiosResponse<any, any>> => {
  return service.post('/haofubao/copy', {
    user_id,
    org_id,
    assistant_id: 'asst_roj2hdhfw2r22e4mhodjts7afhk76a5y'
  });
};

export const getOrganizations = (secret_key): Promise<AxiosResponse<any, any>> => {
  return service.get('/organizations', {
    headers: {
      Authorization: `Bearer ${secret_key}`
    }
  });
};

export const updatePawssword = (username, password): Promise<AxiosResponse<any, any>> => {
  return service.put(`/users/${username}/password`, { password });
};

// 获取platform_info信息
export const platformData = (): Promise<AxiosResponse<any, any>> => {
  return service.get('/myoracle/platform/data', {
    params: {
      platform: 'pdd',
      data_type: 'product',
      assistant_id: localStorage.getItem('assistantId')
    }
  });
};
export const getConfig = (): Promise<AxiosResponse<any, any>> => {
  return userService.post('/aiAgentConfig/selectAiAgentConfigList', {
    shopId: '6868840236607488'
  });
};

export const getConfigDetail = (configId): Promise<AxiosResponse<any, any>> => {
  return service.get('/message/api/aiCsConfig/configDetail', {
    baseURL: import.meta.env.gateway + '/',
    params: {
      configId
    }
  });
};

export const updateConfigDetail = (data): Promise<AxiosResponse<any, any>> => {
  return service.post('/message/api/aiCsConfig/updateConfig', data, {
    baseURL: import.meta.env.gateway + '/'
  });
};

type AddConfig = {
  assistant_id;
  platform: string;
  code?: string;
  mall_id?: string;
  mall_name?: string;
  data_type?: string;
  meta_data?: any;
};
export const addConfig = (data: AddConfig): Promise<AxiosResponse<any, any>> => {
  return service.post('/myoracle/config', {
    ...data,
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
  });
};

export const deleteConfig = (id: string): Promise<AxiosResponse<any, any>> => {
  return service.delete('/myoracle/config', {
    params: {
      id
    }
  });
};

export const updateConfig = (
  id: string,
  mall_name: string,
  meta_data: any
): Promise<AxiosResponse<any, any>> => {
  return service.put(`/myoracle/config?id=${id}`, {
    mall_name,
    meta_data
  });
};

export const applicationTransfer = (): Promise<AxiosResponse<any, any>> => {
  return service.get('/application/transfer', {
    params: {
      assistant_id: localStorage.getItem('assistantId')
      // user_id: JSON.parse(localStorage.getItem("user") ?? "{}")?.id ?? "",
    }
  });
};

type PlatformTask = {
  task_name: string;
  mall_id: string;
  mall_name: string;
  customer_id: string; // uid
  meta_data: {
    conversation_id: string; // thread id
  };
};

export const platformTask = (data: PlatformTask) => {
  return service.post('/application/platform_task', {
    assistant_id: localStorage.getItem('assistantId'),
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
    status: 'undo',
    task_belong: 'chat',
    task_type: 'send',
    ...data
  });
};

export const messagesCreate = ({ thread_id, assistant_name, content }) => {
  return service.post(`/threads/${thread_id}/messages`, {
    metadata: {
      assistant_id: localStorage.getItem('assistantId'),
      assistant_name
    },
    role: 'manual',
    file_ids: [],
    content
  });
};

export const snapshot = () => {
  return service.get('/myoracle/snapshot', {
    params: {
      type: 'auto',
      assistant_id: localStorage.getItem('assistantId'),
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
      page_num: 1,
      page_size: 1
    }
  });
};

export const getAnalysis = ({ start, end }) => {
  return service.get('/workbench/analysis', {
    params: {
      start: start,
      end: end,
      assistant_id: localStorage.getItem('assistantId')
    }
  });
};

export const platformConfig = () => {
  return service.get('/myoracle/platform_config', {
    params: {
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
      assistant_id: localStorage.getItem('assistantId')
    }
  });
};

export const addPlatformConfig = (platform_info) => {
  return service.post('/myoracle/platform_config', {
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
    assistant_id: localStorage.getItem('assistantId'),
    meta_data: {
      platform_info
    }
  });
};

export const updatePlatformConfig = (id, platform_info) => {
  return service.put(`/myoracle/platform_config?id=${id}`, {
    user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
    assistant_id: localStorage.getItem('assistantId'),
    meta_data: {
      platform_info
    }
  });
};

export const members = (username) => {
  return service.post(`/organizations/${localStorage.getItem('organization')}/members`, {
    username,
    role: 'owner'
  });
};

export const setUserRelation = (data: { oid; fid }) => {
  return service.post('/myoracle/temporary', {
    user_id: null,
    assistant_id: localStorage.getItem('assistantId'),
    data_type: 'relation',
    meta_data: {
      userId: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    },
    ...data
  });
};

export const authentication = (data) => {
  return service.post('/api/v3/flows/executor/default-authentication-flow/', {
    component: 'ak-stage-identification',
    ...data
  });
};

export const getUserRelation = () => {
  return service.get('/myoracle/temporary');
};

export const deleteUserRelation = (id) => {
  return service.delete(`/myoracle/temporary?id=${id}`);
};

export const getFileItem = (file_id) => {
  return service.get(`/files/${file_id}`);
};

/** 更新assistant */
export const updateAssistant = (json) => {
  return service.post(`/assistants/${json.id}`, json);
};

/** 编辑thread metadata 信息*/
export const updateThread = (thread_id, metadata) => {
  return service.post(`/threads/${thread_id}`, {
    metadata: metadata
  });
};

/** 获取编辑thread 信息**/
export const getThread = (thread_id) => {
  return service.get(`/threads/${thread_id}`);
};

export const loginOidc = () => {
  return fetch('/api/v1/login/oidc', {
    method: 'GET'
  });
};

export const authOidc = () => {
  return service.get('/auth/oidc');
};
// 用户管理相关接口
//新增用户
export const addUser = (data: any) => {
  return userService.post('/users', data);
};
//分页查询用户列表
export const getUser = (data: any) => {
  return userService.get('/users', {
    params: data
  });
};
//重置用户登录密码
export const setPassword = (data: any) => {
  return userService.put('/users/set_password', data);
};
//删除用户
export const deleteUser = (id) => {
  return userService.delete(`/users/${id}`);
};
//查询用户权限点列表
export const getPermissions = () => {
  return userService.get('/permissions');
};
//获取用户权限
export const getUserRole = () => {
  return userService.get('/roles/current_user');
};
//获取主账号信息
export const getMasterUser = (secret_key) => {
  return userService.get('/users/current_master_user', {
    headers: {
      Authorization: `Bearer ${secret_key}`
    }
  });
};
//用户登录
export const userLogin = (data: any) => {
  return userService.post('/login/client', data);
};
// 修改密码
export const editPassword = (data: { password: string }) => {
  return userService.put('/role/permission/password', data);
};
class KnowledgeAPi {
  getProductQuery({ assistant_id }) {
    return service.get(`/myoracle/product_query?assistant_id=${assistant_id}`);
  }
  deleteProductQuery(id, version_id) {
    return service.delete(
      `/myoracle/domain/intention/product_query_base?id=${id}&version_id=${version_id}`
    );
  }
  addProductQuery(data: {
    product_id;
    cate_id;
    product_qa_id;
    query;
    version_id;
    meta_data?: any;
  }) {
    return service.post('/myoracle/product_query', data);
  }
  /** 编辑知识库问法 */
  updateProductQuery({
    product_id,
    cate_id,
    product_qa_id,
    query,
    id,
    version_id,
    query_id,
    meta_data
  }) {
    return service.put(`/myoracle/product_query?id=${id}`, {
      product_id,
      cate_id,
      product_qa_id,
      query,
      version_id,
      query_id,
      meta_data
    });
  }

  /** 获取知识库意图 */
  getProductQa({ page_num, page_size, version_id }) {
    return service.get(
      `/myoracle/product_qa?version_id=${version_id}&page_num=${page_num}&page_size=${page_size}`
    );
  }
  /** 删除知识库意图 */
  deleteProductQa(id, version_id) {
    return service.delete(`/myoracle/product_qa?id=${id}&version_id=${version_id}`);
  }
  /** 添加知识库意图 */
  addProductQa(json: { cate_kb_id; cate_id; product_id; answer_rule; terms; version_id }) {
    return service.post('/myoracle/product_qa', json);
  }

  /** 编辑知识库意图 */
  updateProductQa({
    cate_kb_id,
    cate_id,
    product_id,
    kb_name,
    answer_rule,
    terms,
    id,
    version_id,
    intent_id
  }) {
    return service.put(`/myoracle/product_qa?id=${id}`, {
      cate_kb_id,
      cate_id,
      product_id,
      answer_rule,
      kb_name,
      terms,
      version_id,
      intent_id
    });
  }
  /** 获取知识库回答 */
  getResponse(data: { page_num; page_size; product_qa_id; version_id }) {
    return service.get(
      `/myoracle/response?version_id=${data.version_id}&page_num=${data.page_num}&page_size=${data.page_size}&product_qa_id=${data.product_qa_id}`
    );
  }

  /** 添加知识库回答 */
  addResponse(json: {
    response;
    product_qa_id;
    kb_category;
    item_id;
    shop_name;
    attr_info;
    version_id;
  }) {
    return service.post('/myoracle/response', json);
  }

  /** 删除知识库回答 */
  deleteResponse(id, version_id) {
    return service.delete(`/myoracle/domain/intention/response?id=${id}&version_id=${version_id}`);
  }

  /** 编辑知识库回答 */
  updateResponse(
    json: {
      response;
      product_qa_id;
      kb_category;
      item_id;
      shop_name;
      attr_info;
      version_id;
      response_id;
      meta_data?: any;
    },
    id
  ) {
    return service.put(`/myoracle/response?id=${id}`, json);
  }

  /** 获取知识库全量意图信息 */
  getDomain(
    kb_name,
    version_id,
    search?: string,
    data?: {
      shop: string;
      pid: string;
      attr_info: string;
      kb_category: string;
    }
  ) {
    return service.post('/myoracle/domain/intention', {
      kb_name,
      search,
      version_id,
      ...data
    });
  }

  /** 获取知识库知识列表 */
  getKbname() {
    return service.get('/myoracle/product_qa/get_kb_name');
  }

  /** 导入知识库文件 */
  domainUploadFile(file, kb_name, version_id, assistant_id) {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('kb_name', kb_name);
    formData.append('version_id', version_id);
    return service.post(
      `/myoracle/domain/intention/upload_docs?assistant_id=${assistant_id}`,
      formData
    );
  }

  /** 删除知识库知识列表 */
  deleteKb(kb_name, version_id) {
    return service.delete(`/myoracle/domain/intention?kb_name=${kb_name}&version_id=${version_id}`);
  }
  /** 导出知识库文件 */
  downDomainFile(kb_name: string, version_id) {
    return service.post(
      `/myoracle/domain/intention/download_docs?kb_name=${kb_name}&version_id=${version_id}&preview=0`
    );
  }
  additionalDown(kb_name) {
    return service.post(
      `/myoracle/additional/additional_down?kb_name=${kb_name}&user_id=${JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''}`
    );
  }
  additionalImportdb(kb_name, file) {
    const formData = new FormData();
    formData.append('files', file);
    formData.append('user_id', JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '');
    formData.append('kb_name', kb_name);
    return service.post('/myoracle/additional/import_db', formData);
  }

  /** 获取知识库文件path */
  getDocPath(kb_name, file_name, version_id) {
    return service.post(
      `/myoracle/domain/intention/get_doc_path?kb_name=${kb_name}&version_id=${version_id}&file_name=${file_name}`
    );
  }

  /** 导入助手知识文件 */
  migrateDocs({ file_id, kb_name, override, assistant_id }) {
    return service.post(
      `/myoracle/domain/intention/migrate_docs?file_id=${file_id}&override=${override}&kb_name=${kb_name}&assistant_id=${assistant_id}`
    );
  }

  /** 知识库文件同步到助手 */
  knowledge({ file_name, embeddings, metadata, version_id }) {
    const formData = new FormData();
    formData.append('file_name', file_name);
    formData.append('purpose', 'assistants');
    formData.append('embeddings', embeddings);
    formData.append('version_id', version_id);
    if (metadata) {
      for (const k of Object.keys(metadata)) {
        if (k !== 'version_id') formData.append(k, metadata[k]);
      }
    }
    return service.post(`/knowledge?file_name=${file_name}`, formData);
  }

  /** 知识库问法建议 */
  querySim(id, version_id) {
    return service.post(`/myoracle/domain/detection/query_sim?id=${id}&version_id=${version_id}`);
  }

  /** 知识库获取问法推荐 */
  getRecommend(prefix, data, version_id, file_id) {
    let metadata = {};
    try {
      metadata = JSON.parse(data.metadata);
    } catch (e) {
      metadata = JSON.parse('{}');
    }
    return service.post('/knowledge/get_recommend', {
      metadata,
      prefix,
      file_id,
      version_id,
      assistant_id: data.assistantId,
      q_or_a: true
    });
  }

  /** 知识库获取回复推荐 */
  getResponseRecommend(prefix, data, version_id, file_id) {
    let metadata = {};
    try {
      metadata = JSON.parse(data.metadata);
    } catch (e) {
      metadata = JSON.parse('{}');
    }
    return service.post('/knowledge/get_recommend', {
      metadata,
      prefix,
      version_id,
      file_id,
      assistant_id: data.assistantId,
      q_or_a: false
    });
  }

  /** 知识库删除建议接口 */
  deleteSuggest(kb_name, data, version_id) {
    let metadata = {};
    try {
      metadata = JSON.parse(data.metadata);
    } catch (e) {
      metadata = JSON.parse('{}');
    }
    return service.post('/knowledge/delete_suggest', {
      kb_name,
      version_id,
      assistant_id: data.assistantId,
      metadata,
      q_or_a: true
    });
  }

  /** 知识检查依据文件生成 */
  genHistory(assistant_id, metadata, version_id, kb_name) {
    const formData = new FormData();
    formData.append('assistant_id', assistant_id);
    formData.append('purpose', 'assistants');
    formData.append('loader', 'chat_query');
    formData.append('version_id', version_id);
    if (metadata) {
      for (const k of Object.keys(metadata)) {
        if (k !== 'loader') formData.append(k, metadata[k]);
      }
    }
    return service.post(
      `/knowledge/gen_history?kb_name=${kb_name}&version_id=${version_id}&assistant_id=${assistant_id}$q_or_a=${true}`,
      formData
    );
  }

  /** 知识库添加属性 */
  addProductAttr(attr_name: string, version_id) {
    return service.post('/myoracle/product_attr', {
      attr_name,
      value: [],
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
      version_id
    });
  }

  /** 知识库删除属性 */
  deleteProductAttr(id: string, version_id) {
    return service.delete(`/myoracle/product_attr?id=${id}&version_id=${version_id}`);
  }

  /** 知识库获取属性 */
  getProductAttr(page_num, page_size, version_id) {
    return service.get(
      `/myoracle/product_attr?version_id=${version_id}&page_num=${page_num}&page_size=${page_size}&user_id=${JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''}`
    );
  }

  /** 知识库添加类别 */
  addProductCate(category: string, version_id, children) {
    return service.post('/myoracle/product_cate', {
      category,
      version_id,
      children,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }

  /** 知识库删除类别 */
  deleteProductCate(id: string, version_id) {
    return service.delete(`/myoracle/product_cate?version_id=${version_id}&id=${id}`);
  }

  /** 知识库编辑属性 */
  updateProductCate(id: string, category: string, version_id, children) {
    return service.put(`/myoracle/product_cate?id=${id}`, {
      category,
      version_id,
      children,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }

  /** 知识库获取类别 */
  getProductCate(page_num, page_size, version_id) {
    return service.get(
      `/myoracle/product_cate?user_id=${JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''}&page_num=${page_num}&page_size=${page_size}&version_id=${version_id}`
    );
  }
  /** 知识库添加商品 */
  addProduct(data: {
    title: string;
    page_title: string;
    item_id: string;
    price: string;
    category: string;
    properties: string;
    code: string;
    shop_id: string;
    children: any;
    version_id: string;
  }) {
    return service.post('/myoracle/product', {
      ...data,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }
  /** 知识库删除商品 */
  deleteProduct(id: string, version_id) {
    return service.delete(`/myoracle/product$version_id=${version_id}&id=${id}`);
  }

  /** 知识库编辑商品 */
  updateProduct(
    id: string,
    data: {
      title: string;
      page_title: string;
      item_id: string;
      price: string;
      category: string;
      properties: string;
      code: string;
      shop_id: string;
      children: any;
      version_id: string;
    }
  ) {
    return service.put(`/myoracle/product?id=${id}`, {
      ...data,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }

  /** 知识库获取商品 */
  getProduct(page_num, page_size, version_id) {
    return service.get(
      `/myoracle/product?user_id=${JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''}&pid=""&version_id=${version_id}&page_size=${page_size}&page_num=${page_num}`
    );
  }

  /** 知识库添加店铺 */
  addShop(shop_name: string, version_id, children) {
    return service.post('/myoracle/shop', {
      shop_name,
      version_id,
      children,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }

  /** 知识库删除店铺 */
  deleteShop(id: string, version_id) {
    return service.delete(`/myoracle/shop?id=${id}&version_id=${version_id}`);
  }

  /** 知识库编辑店铺 */
  updateShop(id: string, shop_name: string, version_id, children) {
    return service.put(`/myoracle/shop?id=${id}`, {
      shop_name,
      version_id,
      children,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''
    });
  }

  /** 知识库获取店铺 */
  getShop(page_num, page_size, version_id) {
    return service.get(
      `/myoracle/shop?user_id=${JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? ''}&page_num=${page_num}&page_size=${page_size}&version_id=${version_id}`
    );
  }

  /** 回复消息推荐生成文件 */
  genResponse(assistant_id, metadata, version_id, kb_name) {
    const formData = new FormData();
    formData.append('assistant_id', assistant_id);
    formData.append('purpose', 'assistants');
    formData.append('loader', 'chat_query');
    formData.append('version_id', version_id);
    if (metadata) {
      for (const k of Object.keys(metadata)) {
        if (k !== 'loader') formData.append(k, metadata[k]);
      }
    }

    return service.post(
      `/knowledge/gen_history?assistant_id=${assistant_id}&version_id=${version_id}&kb_name=${kb_name}&q_or_a=${false}`,
      formData
    );
  }

  /** 获取默认意图id  */
  getDefaultIntentIds(thread_ids) {
    return service.post('/debug/thread/intent_ids', {
      thread_ids
    });
  }

  /** 保存意图id */
  saveIntentIds({ thread_id, intent_ids }) {
    return service.post(`/debug/thread/intent_ids?thread_id=${thread_id}`, {
      intent_ids
    });
  }

  /** 全量意图问法生成 */
  createHistoryQuery({ version_id, kb_name, assistant_id, metadata }) {
    return service.post('/knowledge/create_history_query', {
      metadata,
      kb_name,
      version_id,
      assistant_id,
      q_or_a: true
    });
  }

  /** 全量意图回复生成 */
  createHistoryResponse({ version_id, kb_name, assistant_id, metadata }) {
    return service.post('/knowledge/create_history_query', {
      q_or_a: false,
      metadata,
      kb_name,
      version_id,
      assistant_id
    });
  }

  myoracleTaskStatus(assistant_id: string) {
    return service.get(`/myoracle/task?assistant_id=${assistant_id}`);
  }

  // 聚类意图
  kmeansIntention(data: { version_id: string; kb_name: string; assistant_id: string }) {
    return service.post(
      `/knowledge/kmeans?version_id=${data.version_id}&kb_name=${data.kb_name}&assistant_id=${data.assistant_id}}`
    );
  }

  /** 知识库编辑属性 */
  updateProductAttr(id: string, attr_key: string, version_id, value) {
    return service.put(`/myoracle/product_attr?id=${id}`, {
      attr_name: attr_key,
      value,
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
      version_id
    });
  }
  // 新增意图问法推荐
  myoracleLlmknowledge(task_id: string) {
    return service.get(`/myoracle/llm_knowledge?task_id=${task_id}`);
  }

  // 标记推荐
  mackLlmknowledge(data: { intent_id: string; is_done: boolean; know_id: string; id: string }) {
    const { is_done, intent_id, know_id, id } = data;
    return service.put(`/myoracle/llm_knowledge?id=${id}`, {
      is_done,
      intent_id,
      know_id
    });
  }

  platformDiff() {
    return service.get('/myoracle/platform/diff?platform=pdd&data_type=product');
  }

  platformTask(assistant_id: string) {
    return service.post('/application/platform_task', {
      task_name: 'product',
      user_id: JSON.parse(localStorage.getItem('user') ?? '{}')?.id ?? '',
      assistant_id,
      task_type: 'get',
      task_belong: 'product',
      status: 'undo',
      metadata: {}
    });
  }

  platformAuto(assistant_id) {
    return service.get(
      `/myoracle/platform/auto?platform=pdd&data_type=product&assistant_id=${assistant_id}`
    );
  }
}

export const knowledgeAPi = new KnowledgeAPi();

class FileApi {
  /** 删除知识库文件 */
  deleteFile(id) {
    return service.delete(`/ses/file?id=${id}`);
  }

  /** 上传知识库文件 */
  uploadFile(file, metadata, embeddings) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('purpose', 'assistants');
    formData.append('embeddings', embeddings);
    if (metadata) {
      metadata = JSON.parse(metadata);
      for (const k of Object.keys(metadata)) {
        formData.append(k, metadata[k]);
      }
    }
    return service.post('/files', formData);
  }

  /** 获取知识库文件 */
  getFile(limit) {
    return service.get(`/files?limit=${limit}`);
  }

  getFileItem(file_id) {
    return service.get(`/files/${file_id}`);
  }
}

export const fileApi = new FileApi();
