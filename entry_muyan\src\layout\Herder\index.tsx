import icon_logo from '@/assets/icons/muyan_logo.svg';
import style from './index.module.css';
import Menu from './Menu';
import { useLocation } from 'react-router-dom';
import { useNavigate } from 'react-router-dom';
import useIsDev from '@/hooks/useIsDev.ts';
const HerderView = (): React.ReactElement => {
  const location = useLocation();
  const navigate = useNavigate();
  const isDev = useIsDev();
  return (
    <div className={style.herderView}>
      <div
        className={style.title}
        onClick={() => {
          navigate('/home');
        }}
      >
        <img src={icon_logo} alt="icon_logo" />
        {isDev && <span className={style.dev}>DEV</span>}
      </div>
      {location.pathname !== '/login' && <Menu />}
    </div>
  );
};

export default HerderView;
