/* eslint-disable @typescript-eslint/no-unused-vars */
import React from 'react';
import style from './index.module.css';
import { useState, useEffect } from 'react';
import { message } from 'antd';
import { useConfigStates } from '../../utils/configItemStates';
import { deleteConfig } from '@/api/myqaApi';
import { Tooltip, Popconfirm } from 'antd';
import { cardDataMap } from '../../utils/cardData';
import { openCardPage } from '../../utils/cardData';
// import { useLocation } from 'react-router-dom';
import { Editor } from './Editor';
import { useIsDev } from '@/contexts/IsDevContext';

type Props = {
  config: any;
  getData: (val?: string) => void;
  updateConfigState: (id: string, state) => void;
};
// 接待模式
enum ReceptionModeType {
  SILENCE = '静默',
  ASSISTANT = 'ai辅助',
  OFFICIAL = '正式接待'
}

const List: React.FC<Props> = ({ config, getData, updateConfigState }) => {
  // const { name, id, type = 'pdd', state, status } = config;
  const {
    name,
    id,
    state,
    shopName,
    platformId = 'pdd',
    receptionMode,
    platformName,
    configContent
  } = config;
  const imgMap = cardDataMap();
  const [messageApi, contextHolder] = message.useMessage();
  const { updateListState: setStateList } = useConfigStates();
  const [_logs, setLogs] = useState<Array<{ key: string; value: string }>>([]);
  const [openEditor, setOpenEditor] = useState<boolean>(false);
  const isDev = useIsDev();
  const handleArgenting = async (): Promise<void> => {
    try {
      const isSuccess = openCardPage(platformId, id);
      if (!isSuccess) {
        messageApi.open({
          type: 'warning',
          content: '启动失败'
        });
        return;
      }
      updateConfigState(id, 'ai');
      setStateList('update', { id, state: 'ai' });
    } catch (error) {
      console.log('handleArgenting', error);
    }
  };
  const buttonProps = {
    style: {
      height: '32px',
      width: '60px',
      marginTop: '28px'
    }
  };

  useEffect(() => {
    const handleLog = (_event: any, { id: appId, msg }: any) => {
      if (appId !== id) return;
      const reg = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z/;
      const match = msg.match(reg);
      const value = msg.replace(reg, '');
      setLogs((logs) => {
        return [
          ...logs,
          {
            key: logs.length + '',
            value,
            date: match?.[0]
          }
        ];
      });
    };
    const handleExit = (_event: any, data: any) => {
      if (data.id !== id) return;
      console.log(data.id === id, name);
      updateConfigState(id, null);
      setStateList('update', { id: data.id, state: null });
    };
    const removeLog = window.xBrowser?.on('log', handleLog);
    const removeExit = window.xBrowser?.on('exit', handleExit);
    return () => {
      removeLog?.();
      removeExit?.();
    };
  }, []);
  const handleDel = (): void => {
    deleteConfig(id).then(() => {
      getData();
    });
  };

  return (
    <>
      {contextHolder}
      <div className={`${style.item_case} card-hover`}>
        <div className={style.item_case_header}>
          <div
            className={style.item_case_label_avatar}
            style={{
              backgroundImage: `url(${imgMap.get(config.platformId)})`
            }}
          />
          <div style={{ width: '80%' }}>
            <div className={style.name}>
              <Tooltip
                title={
                  <>
                    {platformName}-{shopName}({ReceptionModeType[receptionMode]})-{name}
                  </>
                }
              >
                {platformName}-{shopName}({ReceptionModeType[receptionMode]})-{name}
              </Tooltip>
            </div>
            <div
              className={`${style.state} ${state === 'ai' ? style.state_success : ''} ${state === null ? style.state_out : ''}`}
            >
              {state === null && '未启用'}
              {state === 'ai' && '启用中'}
            </div>
          </div>
        </div>
        <div className={style.item_case_bottom}>
          <div className={style.item_case_bottom_button}>
            {state === null && (
              <span
                style={{
                  cursor: 'pointer'
                }}
                className="text-hover--primary"
                onClick={() => {
                  handleArgenting();
                }}
              >
                启用
              </span>
            )}
            {state !== null && (
              <Popconfirm
                placement="bottomRight"
                title="确认停用该账号？"
                description="停用后账号将停止同步客服数据"
                okText="确认"
                cancelText="取消"
                cancelButtonProps={buttonProps}
                okButtonProps={buttonProps}
                onConfirm={() => {
                  window.xBrowser?.send('TABS_DEl_ONENTRY', {
                    id
                  });
                }}
              >
                <span
                  style={{
                    cursor: 'pointer'
                  }}
                  className="text-hover--primary"
                >
                  停用
                </span>
              </Popconfirm>
            )}
          </div>
          {isDev && (
            <>
              <div
                style={{
                  width: '1px',
                  height: '20px',
                  borderLeft: '1px solid rgba(18, 18, 18, 0.1)'
                }}
              />
              <div className={style.item_case_bottom_button}>
                <div
                  onClick={() => {
                    setOpenEditor(true);
                  }}
                  style={{
                    height: '40px',
                    cursor: 'pointer',
                    display: 'inline-block',
                    lineHeight: '40px'
                  }}
                  className="text-hover--primary"
                >
                  编辑
                </div>
              </div>
            </>
          )}
        </div>
      </div>
      <Editor
        configContent={configContent}
        id={id}
        name={name}
        open={openEditor}
        close={() => setOpenEditor(false)}
      />
    </>
  );
};

export default List;
