<!doctype html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>Electron</title>
  <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:" />
</head>

<body>
  <div id="root"></div>
  <script type="module" src="/default-view/src/main.tsx"></script>
  <script>
    window.onmessage = (event) => {
      // event.source === window means the message is coming from the preload
      // script, as opposed to from an <iframe> or other source.
      if (event.source === window && event.data === 'port') {
        const [port] = event.ports
        // Once we have the port, we can communicate directly with the main
        // process.
        port.onmessage = (event) => {
          console.log('from main process:', event.data)
          port.postMessage({
            source: "default-view",
            data: "我是default-view,收到了" + event.data.source + "的数据:" + JSON.stringify(event.data.data)
          })
        }
        port.postMessage({
          source: "default-view",
          handshake: true,
        })
      }
    }
    const beforeUnloadHandler = (event) => {
      // Recommended
      event.preventDefault();

      // Included for legacy support, e.g. Chrome/Edge < 119
      event.returnValue = true;
    };
    // window.addEventListener("beforeunload", beforeUnloadHandler);

  </script>
</body>

</html>
