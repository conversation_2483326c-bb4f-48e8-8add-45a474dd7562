import { crashReporter } from 'electron';
import { name, version } from '../../package.json';

const mode = import.meta.env.MODE;

crashReporter.start({
  submitURL: `https://agent_client_v2.bugsplat.com/post/electron/v2/crash.php`,
  // ignoreSystemCrashHandler: true,
  uploadToServer: true,
  rateLimit: false,
  globalExtra: {
    product: mode,
    version: version
    // "key": "en-US",
    // "email": "<EMAIL>",
    // "comments": "BugSplat rocks!",
  }
});
