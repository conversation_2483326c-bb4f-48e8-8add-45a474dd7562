/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import _ from 'lodash';
import Help from './help';
import './toast.css';
import { Button } from 'antd';
import running from './img/running.svg';
import waitReply from './img/wait-reply.svg';
import disconnect from './img/disconnect.svg';
import Block from './components/Block';
import empty from './img/empty.svg';
// import PerfectScrollbar from 'react-perfect-scrollbar';
// import 'react-perfect-scrollbar/dist/css/styles.css';
import SimpleBar from 'simplebar-react';
import 'simplebar-react/dist/simplebar.min.css';

// import button from '../../../../entry_muyan/src/components/Button';

// window.onbeforeunload = (event) => {
//   // Recommended
//   event.preventDefault();
//
//   // Included for legacy support, e.g. Chrome/Edge < 119
//   event.returnValue = true;
// };

type ToastMessage = {
  sender?: number;
  to?: number;
  topic: string;
  payload: {
    uid: string;
    cs: string;
    shop: string;
    nickname: string;
    time: number;
    message: string;
  };
};

// function usePrevious(value) {
//   const ref = useRef();
//   useEffect(() => {
//     ref.current = value;
//   });
//   return ref.current;
// }

const waitLimit = 3 * 60 * 1000;
const renderTime = (time, now, close) => {
  const estimate = dayjs(time).add(waitLimit);
  const t = dayjs(time);
  const n = dayjs(now);
  if (estimate.isAfter(n)) {
    return <span className="countdown red">{estimate.diff(n, 's')}秒后超时</span>;
  } else {
    return (
      <span className="countdown orange">
        {`已等待${n.diff(t, 'm')}分钟`}
        <span className="close" onClick={close}>
          x
        </span>
      </span>
    );
  }
};

// const handleItemClick = (message) => {
//   return (_e) => {
//     window.electronAPI.callbackToastMessage({
//       to: message.sender,
//       topic: 'response_to_user',
//       payload: message.payload
//     });
//   };
// };

const App: React.FC = () => {
  const [unDealMessage, setUnDealMessage] = useState<any>({});
  const [currentTime, setCurrentTime] = useState(dayjs().valueOf());
  const [clientStatus, setClientStatus] = useState<string>('启动中');
  const [exeStatus, setExeStatus] = useState<string>('未启动');
  const [replyMsg, setReplyMsg] = useState<string>('');
  const [runningStatus, setRunningStatus] = useState<'connect' | 'wait-reply' | 'disconnect'>(
    'connect'
  );
  const port = useRef<any>();
  const [messages, setMessages] = useState<any[]>([]);
  const [user, setUser] = useState<string>('未知联系人');
  const [selected, setSelected] = useState<string | undefined>(undefined);

  useEffect(() => {
    window.xBrowser.on('user-reply-list', (_, data) => {
      setMessages(
        data.map((d) => {
          const q = JSON.parse(d.question);
          return {
            key: d.uuid,
            answer: d.answer,
            userMsgs: q.map((q) => {
              return {
                question: q.question,
                time: q.time
              };
            })
          };
        })
      );
    });
    window.xBrowser.on('user-reply-selected', (_, data) => {
      setSelected(data);
    });
    window.xBrowser.on('user-reply-add', (_, data) => {
      setMessages((old) => {
        const q = JSON.parse(data.question);
        return [
          {
            key: data.uuid,
            answer: data.answer,
            userMsgs: q.map((q) => {
              return {
                question: q.question,
                time: q.time
              };
            })
          },
          ...old
        ];
      });
    });
    window.xBrowser.on('current-user', (_, data) => {
      console.log('select user', data);
      let user = data.user;
      if (!user) {
        user = '未知联系人';
        setMessages([]);
      }
      setUser(user);
    });
    // window.addEventListener('message', (e) => {
    //   const { source, data } = e;

    //   if (source === window) {
    //     return;
    //   }

    //   port.current = source;
    //   console.log('received', e);

    //   if (typeof data === 'object') {
    //     if (data.type === 'status') {
    //       setRunningStatus(data.status);
    //     } else if (data.type === 'reply-list') {
    //       // setUser(data.data[0].nickname);
    //       setMessages(
    //         data.data.map((d) => {
    //           const q = JSON.parse(d.question);
    //           return {
    //             key: d.uuid,
    //             answer: d.answer,
    //             userMsgs: q.map((q) => {
    //               return {
    //                 question: q.question,
    //                 time: q.time
    //               };
    //             })
    //           };
    //         })
    //       );
    //       // console.log('reply-list', data.data);
    //       // setReplyList(data.data);
    //     } else if (data.type === 'select-message') {
    //       setSelected(data.data);
    //     } else if (data.type === 'add-reply') {
    //       setMessages((old) => {
    //         const q = JSON.parse(data.data.question);
    //         return [
    //           {
    //             key: data.data.uuid,
    //             answer: data.data.answer,
    //             userMsgs: q.map((q) => {
    //               return {
    //                 question: q.question,
    //                 time: q.time
    //               };
    //             })
    //           },
    //           ...old
    //         ];
    //       });
    //     } else if (data.type === 'current-user') {
    //       console.log('select user', data.data);
    //       let user = data.data.user;
    //       if (!user) {
    //         user = '未知联系人';
    //         setMessages([]);
    //       }
    //       setUser(user);
    //     }
    //   }
    // });
    // setMessages([
    //   {
    //     key: '1234.PNM',
    //     answer:
    //       'image|https://muyu-public.oss-cn-beijing.aliyuncs.com/rpa/images/20241206180738.jpg',
    //     userMsgs: [
    //       {
    //         question: '问题1',
    //         time: '1318781876406' // 时间戳
    //       },
    //       {
    //         question: '问题2',
    //         time: '1735800201000'
    //       }
    //     ]
    //   },
    //   {
    //     key: '1232324.PNM',
    //     answer: '亲亲 这边为您转接到高级客服来处理哦～请您稍等',
    //     userMsgs: [
    //       {
    //         question: '问题1',
    //         time: '1318781876406' // 时间戳
    //       },
    //       {
    //         question: '问题2',
    //         time: '1735800201000'
    //       }
    //     ]
    //   },
    //   {
    //     key: '1232322324.PNM',
    //     answer: '亲亲 这边为您转接到高级客服来处理哦～请您稍等',
    //     userMsgs: [
    //       {
    //         question:
    //           '您好，我对这款鞋子非常感兴趣，鞋子的材质是怎么样的？它的透气性如何，适不适合夏季穿着？我比较担心夏天穿着会不会感觉闷热或者不透气。',
    //         time: '1318781876406' // 时间戳
    //       },
    //       {
    //         question: '问题2',
    //         time: '1735800201000'
    //       }
    //     ]
    //   },
    //   {
    //     key: '123232222324.PNM',
    //     answer: '亲亲 这边为您转接到高级客服来处理哦～请您稍等',
    //     userMsgs: [
    //       {
    //         question:
    //           '您好，我对这款鞋子非常感兴趣，鞋子的材质是怎么样的？它的透气性如何，适不适合夏季穿着？我比较担心夏天穿着会不会感觉闷热或者不透气。',
    //         time: '1318781876406' // 时间戳
    //       },
    //       {
    //         question: '问题2',
    //         time: '1735800201000'
    //       }
    //     ]
    //   }
    // ]);
  }, []);

  return (
    <div>
      <div className="title">
        <h1>
          AI回复详情<span className="user">{`【买家：${user}】`}</span>
        </h1>
        {/*<span className="collapse">收起</span>*/}
      </div>
      <div className="list">
        <SimpleBar style={{ padding: '0 12px', height: '100%' }}>
          {messages.length ? (
            messages.map((msg) => {
              return <Block selected={selected === msg.key} msg={msg} key={msg.key} />;
            })
          ) : (
            <div className="empty">
              <img src={empty} alt="empty" />
              <p className="empty-text">暂无数据</p>
            </div>
          )}
        </SimpleBar>
      </div>
    </div>
  );
};

export default App;
