class Feishu {
  constructor(
    private token: string,
    private log
  ) {}

  async send(title: string, content: string) {
    const url = `https://open.feishu.cn/open-apis/bot/v2/hook/${this.token}`;
    const data = {
      msg_type: 'interactive',
      card: {
        elements: [
          {
            tag: 'markdown',
            content: content
            // text: {
            //   content: content,
            //   tag: 'lark_md'
            // }
          } /*, {
          "actions": [{
            "tag": "button",
            "text": {
              "content": "更多景点介绍 :玫瑰:",
              "tag": "lark_md"
            },
            "url": "https://www.example.com",
            "type": "default",
            "value": {}
          }],
          "tag": "action"
        }*/
        ],
        header: {
          title: {
            content: title,
            tag: 'plain_text'
          }
        }
      }
    };

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    });

    if (!response.ok) {
      this.log.error('Failed to send notice', response);
      throw new Error('Failed to send notice');
    }

    return response.json();
  }
}

export default Feishu;
