{"name": "entry_muyan", "private": true, "version": "1.5.2", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"ali-oss": "^6.21.0", "antd": "^5.21.5", "jsoneditor-react": "^3.1.2", "react": "^18.3.1", "react-diff-viewer": "^3.1.1", "react-dom": "^18.3.1", "react-json-view": "^1.21.3"}, "devDependencies": {"@antv/x6": "^2.18.1", "@antv/x6-plugin-selection": "^2.2.2", "@antv/x6-react-shape": "^2.2.3", "@aws-sdk/client-s3": "^3.679.0", "@eslint/js": "^9.13.0", "@formily/antd-v5": "^1.2.3", "@formily/core": "^2.3.2", "@formily/react": "^2.3.2", "@sentry/react": "^8.35.0", "@sentry/vite-plugin": "^2.22.6", "@types/react": "^18.3.11", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.3", "echarts-for-react": "3.0.2", "eslint": "^9.13.0", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.13", "file-saver": "^2.0.5", "github-markdown-css": "^5.7.0", "globals": "^15.11.0", "insert-css": "^2.0.0", "less": "^4.2.0", "less-loader": "^12.2.0", "mobx": "^6.13.5", "mobx-devtools-mst": "^0.9.33", "mobx-react-lite": "^4.0.7", "mobx-state-tree": "^6.0.1", "react-router-dom": "^6.27.0", "react-sortablejs": "^6.1.4", "typescript": "~5.6.2", "typescript-eslint": "^8.10.0", "vite": "^5.4.9", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-svgr": "^4.3.0", "xlsx": "^0.18.5"}}