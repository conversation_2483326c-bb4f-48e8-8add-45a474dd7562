import { createPointer, DataType, define, open, restorePointer, unwrapPointer } from 'ffi-rs';

if (process.platform === 'win32') {
  open({
    library: 'user32',
    path: 'user32'
  });
}

// 这里填写需要吸附的窗口名字
const SNAP_FORM_TITLE_ARR = ['千牛工作台', '接待中心'];

// 通过窗体标题判断窗体要不要吸附
function isSnapForm(title: string) {
  return SNAP_FORM_TITLE_ARR.some((it) => title?.includes(it));
}

const user32 = define({
  GetWindowRect: {
    library: 'user32',
    retType: DataType.Boolean,
    paramsType: [DataType.I32, DataType.External]
  },
  GetWindowTextW: {
    library: 'user32',
    retType: DataType.I32,
    paramsType: [DataType.I32, DataType.U8Array, DataType.I32]
  },
  GetForegroundWindow: {
    library: 'user32',
    retType: DataType.I32,
    paramsType: []
  }
});

// 窗体矩形类型
const rectType = {
  left: DataType.I32,
  top: DataType.I32,
  right: DataType.I32,
  bottom: DataType.I32
};

const rect = {
  left: 0,
  top: 0,
  right: 0,
  bottom: 0
};
const rectPointer = createPointer({
  paramsType: [rectType],
  paramsValue: [rect]
});

let interval: NodeJS.Timeout | null;

// 吸附和取消吸附
export function monitorWindow(windowTitle: string, cb: (res: typeof rect | null) => void) {
  const interval = setInterval(() => {
    if (process.platform !== 'win32') {
      return cb(null);
    }
    const hwnd = user32.GetForegroundWindow([]);
    const titleBuffer = Buffer.alloc(200);
    user32.GetWindowTextW([hwnd, titleBuffer, titleBuffer.length]);
    const title = titleBuffer.toString('utf16le');
    if (title.includes(windowTitle)) {
      user32.GetWindowRect([hwnd, unwrapPointer(rectPointer)[0]]);
      const restoreData = restorePointer({
        paramsValue: rectPointer,
        retType: [rectType]
      });
      cb(restoreData[0] as any as typeof rect);
    }
  }, 16);
  // 16ms相对丝滑一点
  return () => {
    clearInterval(interval);
  };
}
