import { store } from '../store';
import { is } from '@electron-toolkit/utils';
import { app, BaseWindow } from 'electron';
import path from 'node:path';
import fs from 'fs';
import download from 'download';
import { log } from '../log';

store.set('window.title', '牧言 智能客服');
store.set('window.hideAddressBar', true);
store.set('window.hideFavicon', true);
store.set('window.toolbarHeight', 68);
store.set('window.onbeforeunload', {
  buttons: ['退出', '取消'],
  title: '确认退出',
  message: '您确定要退出吗？'
});

if (store.get('homepage') === undefined) {
  store.set('homepage', {
    url: 'https://client.dongchacat.cn/',
    options: {
      integration: {
        id: 'home',
        name: 'home'
      },
      advances: {
        dangerousDisableWebSecurity: true,
        disableClose: true,
        overrideTitle: `<?xml version="1.0" encoding="UTF-8"?>
<svg width="16px" height="16px" viewBox="0 0 16 16" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>首页面</title>
    <g id="首页面" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path d="M6.89059961,2.03774984 C7.56239843,1.58988395 8.43760157,1.58988395 9.10940039,2.03774984 L13.6094004,5.03774984 C14.1657977,5.40868139 14.5,6.03314405 14.5,6.70185043 L14.5,12.2981496 C14.5,13.4027191 13.6045695,14.2981496 12.5,14.2981496 L10.2,14.2978744 C10.0895524,14.2978372 10.0000239,14.208298 10,14.0978504 L10,10.2981496 L10,10.2981496 C10,9.78531374 9.61395981,9.36264241 9.11662113,9.30487731 L9,9.29814957 L7,9.29814957 C6.44771525,9.29814957 6,9.74586483 6,10.2981496 L6,14.0978504 C5.99997607,14.208298 5.9104476,14.2978372 5.8,14.2978744 L3.5,14.2981496 L3.5,14.2981496 C2.4456382,14.2981496 1.58183488,13.4822718 1.50548574,12.4474119 L1.5,12.2981496 L1.5,6.70185043 C1.5,6.03314405 1.83420227,5.40868139 2.39059961,5.03774984 L6.89059961,2.03774984 Z" id="路径"></path>
    </g>
</svg>`
      }
    }
  });
}

if (is.dev) {
  store.set('homepage.url', 'http://localhost:8172/');
}

const gotTheLock = app.requestSingleInstanceLock();

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory, additionalData) => {
    // Print out data received from the second instance.
    // console.log(additionalData)

    // Someone tried to run a second instance, we should focus our window.
    BaseWindow.getAllWindows().forEach((lastWindow) => {
      if (lastWindow) {
        if (lastWindow.isMinimized()) lastWindow.restore();
        lastWindow.focus();
      }
    });
  });
}

async function checkAndDownloadQianniu() {
  const appPath = app.getPath('appData');
  const exePath = path.join(appPath, 'AliWork');

  if (!fs.existsSync(path.join(exePath, 'AliWorkbench'))) {
    log.info('downloading qianniu...');
    await download(
      'https://muyan-package-1312011744.cos.ap-shanghai.myqcloud.com/download/AliWorkbench.zip',
      exePath,
      {
        extract: true
      }
    );
    log.info('download qianniu success');
  } else {
    log.info('qianniu is already installed');
  }
}

if (process.platform === 'win32') {
  checkAndDownloadQianniu().catch((e) => {
    log.error('download qianniu failed', e);
  });
}
