/* eslint-disable react/prop-types */
import style from './index.module.css';
import icon_shop from '@/assets/icons/icon_shop.svg';

type Props = {
  handleOpen: (id: string) => void;
  title: string;
  total: number;
};
const Item: React.FC<Props> = ({ handleOpen, title, total }) => {
  return (
    <div className={style.item} style={{ cursor: 'pointer' }} onClick={() => handleOpen(title)}>
      <div className={style.header}>
        <img src={icon_shop} alt="icon_shop" className={style.icon} />
        <span className={style.title}>{title}</span>
      </div>
      <div className={style.content}>
        <div className={style.tip}>商品数:{total}</div>
      </div>
    </div>
  );
};

export default Item;
