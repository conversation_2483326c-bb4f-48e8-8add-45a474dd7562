import { types } from 'mobx-state-tree';
import makeInspectable from 'mobx-devtools-mst';
import { UserInfo } from './userInfo';
import { Knowledge } from './knowledge';
import { ExtractSession } from './extractSession';
import { File } from './file';
import { Message } from './message';
const RootStore = types.model('RootStore', {
  userInfo: UserInfo,
  knowledge: Knowledge,
  extractSession: ExtractSession,
  file: File,
  message: Message
});

export const store = RootStore.create({
  userInfo: {
    aikfPackageAsset: {},
    permission: 'workspace'
  },
  knowledge: {
    list: [],
    kbdata: [],
    currentKdName: '',
    spinList: false,
    modifier: 0,
    openCheck: false,
    openRM: false,
    search: '',
    openQM: false,
    versionId: '',
    queryProposal: {},
    responseProposal: {},
    multifileData: [],
    taskStatus: {},
    searchParams: {
      shop_name: '',
      item_id: '',
      attr_info: '',
      kb_category: ''
    },
    productList: [],
    searchType: 'content',
    fetchStats: 0,
    fetchStatsAll: 0,
    llmknowledge: [],
    productAttrs: [],
    responseAttrs: [],
    attribute: []
  },
  file: {
    fileList: []
  },
  extractSession: {
    editList: [],
    currentIntentionId: '',
    search: '',
    newIntention: [],
    intentionList: [],
    currenQuerytList: []
  },
  message: {
    queryIur: []
  }
});

makeInspectable(store);
