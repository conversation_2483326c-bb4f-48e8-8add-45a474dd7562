/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable @typescript-eslint/explicit-function-return-type */
/* eslint-disable no-prototype-builtins */
/* eslint-disable no-case-declarations */
import { useMemo, forwardRef, useImperativeHandle, useRef, useEffect } from 'react';
import { FormProvider, createSchemaField } from '@formily/react';
import { createForm } from '@formily/core';
import {
  FormItem,
  Input,
  Switch,
  Space,
  FormLayout,
  ArrayItems,
  ArrayCollapse,
  FormTab,
  FormButtonGroup,
  Submit,
  NumberPicker,
  Editable,
  Select,
  Radio,
  DatePicker
} from '@formily/antd-v5';
import metadata_dictionary from './metadata-dictionary.json';
// import { schema } from "./schema";
type Props = {
  json: any;
  onFormSubmit: (val: any) => void;
  onChange: (form: any) => void;
};
// eslint-disable-next-line react/display-name
export const MetadataEditForm = forwardRef<any, Props>(({ json, onFormSubmit, onChange }, ref) => {
  const rexArr = [
    /recall_vs_configs\.([A-Za-z0-9-_]+)\.topk/,
    /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.method/,
    /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.prompt/
  ];
  const stringMap = new Map()
    .set(/recall_vs_configs\.([A-Za-z0-9-_]+)\.topk/.toString(), 'recall_vs_configs.xxx.topk')
    .set(
      /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.method/.toString(),
      'preset_configs.preset_label_resp.xxx.method'
    )
    .set(
      /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.prompt/.toString(),
      'preset_configs.preset_label_resp.xxx.prompt'
    );
  function getTitle(title: string) {
    let key = title;
    rexArr.forEach((item) => {
      if (item.test(title)) key = title.replace(item, stringMap.get(item.toString()));
    });
    switch (key) {
      case 'recall_vs_configs.xxx.topk':
        const oneExecArr = /recall_vs_configs\.([A-Za-z0-9-_]+)\.topk/.exec(title);
        return {
          title: metadata_dictionary[key]
            ? oneExecArr
              ? oneExecArr[1] + metadata_dictionary[key]
              : metadata_dictionary[key]
            : title,
          tip: metadata_dictionary[key + '_tip'] ?? ''
        };
      case 'preset_configs.preset_label_resp.xxx.method':
        const twoExecArr =
          /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.method/.exec(title);
        return {
          title: metadata_dictionary[key]
            ? twoExecArr
              ? twoExecArr[1] + metadata_dictionary[key]
              : metadata_dictionary[key]
            : title,
          tip: metadata_dictionary[key + '_tip'] ?? ''
        };
      case 'preset_configs.preset_label_resp.xxx.prompt':
        const threeExecArr =
          /preset_configs\.preset_label_resp\.([A-Za-z0-9-_\u4e00-\u9fff]+)\.prompt/.exec(title);
        return {
          title: metadata_dictionary[key]
            ? threeExecArr
              ? threeExecArr[1] + metadata_dictionary[key]
              : metadata_dictionary[key]
            : title,
          tip: metadata_dictionary[key + '_tip'] ?? ''
        };
      default:
        return {
          title: metadata_dictionary[key] ?? title,
          tip: metadata_dictionary[key + '_tip'] ?? ''
        };
    }
  }
  function jsonToSchema(jsonData, title = '') {
    const schema: any = {
      title: title,
      properties: {},
      type: 'object'
    };
    const labelData = getTitle(title);
    schema.required = true;
    schema.title = labelData.title;
    const tip = labelData.tip;
    if (tip) schema['x-decorator-props'] = { tooltip: <div>{tip}</div> };
    if (typeof jsonData === 'object' && !Array.isArray(jsonData)) {
      schema.type = 'object';
      for (const key in jsonData) {
        if (jsonData.hasOwnProperty(key) && !['goog:chromeOptions'].includes(key)) {
          const subTitle = title ? `${title}.${key}` : `${key}`;
          schema.properties[key] = jsonToSchema(jsonData[key], subTitle);
        }
      }
    } else if (Array.isArray(jsonData)) {
      schema.type = 'array';
      if (jsonData.length > 0) {
        schema['x-decorator'] = 'FormItem';
        schema['x-component'] = 'ArrayItems';
        if (typeof jsonData[0] === 'string' || !jsonData[0]) {
          schema['x-decorator'] = 'FormItem';
          (schema.items = {
            type: 'void',
            'x-component': 'Space',
            'x-component-props': {
              className: 'array-space',
              style: {
                // minWidth: 10,
                // maxWidth: 500,
                width: '100%'
              }
            },
            properties: {
              sort: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.SortHandle'
              },
              input: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input.TextArea',
                'x-component-props': {
                  style: {
                    // minWidth: 500,
                    maxWidth: 800
                    // width: 800,
                  }
                }
              },
              remove: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.Remove'
              }
            }
          }),
            (schema.properties = {
              add: {
                type: 'void',
                title: '添加条目',
                'x-component': 'ArrayItems.Addition'
              }
            });
        }
        if (typeof jsonData[0] === 'number' || !jsonData[0]) {
          schema['x-decorator'] = 'FormItem';
          (schema.items = {
            type: 'void',
            'x-component': 'Space',
            properties: {
              sort: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.SortHandle'
              },
              input: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'NumberPicker',
                'x-component-props': {}
              },
              remove: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.Remove'
              }
            }
          }),
            (schema.properties = {
              add: {
                type: 'void',
                title: '添加条目',
                'x-component': 'ArrayItems.Addition'
              }
            });
        }
        if (typeof jsonData[0] === 'object') {
          schema['x-component'] = 'ArrayItems';
          const schemaData = {};
          for (const key in jsonData[0]) {
            const titleName = title + '.' + key;
            const tipItem = metadata_dictionary[titleName + '_tip'];
            if (typeof jsonData[0][key] === 'string') {
              schemaData[key] = {
                type: 'string',
                title: metadata_dictionary[titleName] ?? titleName,
                required: titleName !== 'keyword_replacement.substituteWord' ? true : false,
                'x-decorator': 'FormItem',
                'x-component': 'Input'
              };
              if (tipItem)
                schemaData[key]['x-decorator-props'] = {
                  tooltip: <div>{tipItem}</div>
                };
            }
            if (typeof jsonData[0][key] === 'number') {
              schemaData[key] = {
                type: 'number',
                title: metadata_dictionary[titleName] ?? titleName,
                required: titleName !== 'keyword_replacement.substituteWord' ? true : false,
                'x-decorator': 'FormItem',
                'x-component': 'NumberPicker',
                'x-component-props': {}
              };
              if (tipItem)
                schemaData[key]['x-decorator-props'] = {
                  tooltip: <div>{tipItem}</div>
                };
            }
            if (typeof jsonData[0][key] === 'object') {
              schemaData[key] = jsonToSchema(jsonData[0][key], title);
            }
          }
          schema.items = {
            type: 'object',
            properties: {
              space: {
                type: 'void',
                'x-component': 'Space',
                'x-component-props': {
                  align: 'start'
                },
                properties: {
                  sort: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-component': 'ArrayItems.SortHandle'
                  },
                  ...schemaData,
                  remove: {
                    type: 'void',
                    'x-decorator': 'FormItem',
                    'x-component': 'ArrayItems.Remove'
                  }
                }
              }
            }
          };
          schema.properties = {
            add: {
              type: 'void',
              title: '添加条目',
              'x-component': 'ArrayItems.Addition'
            }
          };
        }
      }
    } else if (typeof jsonData === 'boolean') {
      schema.type = 'boolean';
      schema['x-decorator'] = 'FormItem';
      schema['x-component'] = 'Switch';
    } else if (typeof jsonData === 'string') {
      schema.type = 'string';
      schema['x-decorator'] = 'FormItem';
      schema['x-component'] = 'Input.TextArea';
      schema['x-component-props'] = {
        style: {
          width: 500
        }
      };
    } else if (typeof jsonData === 'number') {
      schema.type = 'number';
      schema['x-decorator'] = 'FormItem';
      schema['x-component'] = 'NumberPicker';
      schema['x-component-props'] = {
        style: {
          width: 500
        }
      };
    } else {
      schema.type = typeof jsonData;
    }
    return schema;
  }

  // 转换 JSON 到 JSON Schema
  const jsonSchema = jsonToSchema(json);
  const form = useMemo(
    () =>
      createForm({
        values: json
      }),
    []
  );
  useEffect(() => {
    const handleChange = (values) => {
      if (values.type === 'onFormInputChange') {
        onChange(values);
      }
    };
    const subscribe = form.subscribe(handleChange);

    // 清理函数，用于在组件卸载时移除事件监听器
    return () => {
      form.unsubscribe(subscribe);
    };
  }, []);

  const SchemaField = useMemo(
    () =>
      createSchemaField({
        components: {
          FormItem,
          Input,
          Switch,
          Space,
          ArrayItems,
          ArrayCollapse,
          FormTab,
          NumberPicker,
          Editable,
          DatePicker,
          Radio,
          Select
        }
      }),
    []
  );
  const handleSubmit = async (values: any) => {
    onFormSubmit(values);
  };
  useImperativeHandle(ref, () => {
    return {
      submit
    };
  });
  const submitRef = useRef<HTMLDivElement | null>(null);
  // eslint-disable-next-line @typescript-eslint/explicit-function-return-type
  function submit() {
    if (submitRef.current) {
      submitRef.current.click();
    }
  }

  return (
    <FormProvider form={form}>
      <FormLayout layout="vertical">
        <SchemaField schema={jsonSchema} />
      </FormLayout>
      <FormButtonGroup>
        <Submit style={{ display: 'none' }} onSubmit={handleSubmit}>
          <div ref={submitRef}>保存</div>
        </Submit>
      </FormButtonGroup>
    </FormProvider>
  );
});
