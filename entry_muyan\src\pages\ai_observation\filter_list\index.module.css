.filter_list {
  width: 383px;
  background-color: #ffffff;
  padding: 24px 16px;
  height: 100%;
  border-right: 1px solid #e5e5e5;
}

.title {
  font-family: PingFang SC;
  font-size: 18px;
  font-weight: 500;
  line-height: 26px;
  letter-spacing: 0px;
  color: #121212;
  margin-top: 24px;
  margin-bottom: 16px;
}

.icon {
  width: 14px;
  height: 14px;
}

.select {
  width: 100%;
  margin: 10px 0px;
}

.total {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  color: #7c7c7c;
}
.content {
  height: calc(100vh - 400px);
  padding-bottom: 20px;
  /* background-color: aqua; */
  overflow-y: auto;
}

.contenttwo {
  height: calc(100vh - 240px);
}

.header {
  width: 352px;
  height: 76px;
  border-radius: 8px;
  padding: 16px;
  background: rgba(18, 18, 18, 0.04);
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.header_title {
  display: flex;
  align-items: center;
}
.header_title .name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 20px;
  letter-spacing: 0px;
  color: #121212;
}
.header_btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 80px;
  height: 24px;
  padding: 0 8px;
  background: #f2f3f5;
  box-sizing: border-box;
  border: 1px solid #dadada;
  border-radius: 8px;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  letter-spacing: 0px;
  color: #595b60;
  margin-left: 8px;
}

.header_tip {
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  color: #7c7c7c;
  margin-top: 3px;
  display: inline-block;
  width: calc(100% - 20px);
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.tabs {
  display: flex;
  align-items: center;
  padding: 10px 0px;
}

.tabs_item {
  height: 20px;
  font-weight: 400;
  font-size: 14px;
  color: #333333;
  margin-right: 20px;
  cursor: pointer;
}

.tabs_active {
  color: #086eff;
  font-weight: bold;
}
