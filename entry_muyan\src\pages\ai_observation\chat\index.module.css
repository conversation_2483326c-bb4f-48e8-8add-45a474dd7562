.chat {
  padding: 48px 24px 24px 24px;
  height: 100%;
  flex: 1;
  overflow-y: auto;
}

.transfer {
  height: calc(100% - 74px);
}

.item {
  display: flex;
  align-items: start;
  margin-bottom: 58px;
  width: 100%;
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  position: absolute;
  top: -25px;
}
.headerRight {
  display: flex;
  align-items: right;
  position: absolute;
  top: -25px;
  right: 0;
}
.name {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0px;
  color: #121212;
  margin-right: 8px;
}

.time {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 20px;
  letter-spacing: 0px;
  color: rgba(0, 0, 0, 0.49);
}

.message {
  background: rgba(18, 18, 18, 0.06);
  border-radius: 8px;
  padding: 12px 16px;
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  color: #121212;
  position: relative;
  cursor: pointer;
  max-width: 100%;
  display: inline-block;
}
.bottom_icon {
  position: absolute;
  bottom: -28px;
  left: 0;
  opacity: 0;
  cursor: pointer;
}
.message:hover .bottom_icon {
  opacity: 1;
}
.bottom_icon:hover .bottom_icon {
  opacity: 1;
}
.ai_message {
  background: rgba(0, 122, 255, 0.1);
}
.manual_message {
  background: rgba(46, 116, 255, 0.1);
}

.icon_button {
  width: 20px;
  height: 20px;
  padding: 2px;
  margin-right: 12px;
  border-radius: 4px;
  cursor: pointer;
}
.icon_button:hover {
  background-color: rgba(18, 18, 18, 0.04);
}
.icon_button:active {
  background-color: rgba(18, 18, 18, 0.08);
}
