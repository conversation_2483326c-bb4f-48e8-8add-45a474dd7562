# x-browser

An Electron application with React and TypeScript

## Recommended IDE Setup

- [VSCode](https://code.visualstudio.com/) + [ESLint](https://marketplace.visualstudio.com/items?itemName=dbaeumer.vscode-eslint) + [Prettier](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode)

## Project Setup

### Install

```bash
$ pnpm install
```

### Development

```bash
$ pnpm dev
```

### All Development

```bash
$ pnpm dev:all
```


### Build

```bash
# For windows
$ pnpm build:win

# For macOS
$ pnpm build:mac

# For Linux
$ pnpm build:linux
```

### git commit
```shell
npm run commit
或
pnpm commit

或者全局安装 commitizen
npm install -g commitizen
或
pnpm add -g commitizen
然后使用 git cz 或者 cz
```
