import { ipc<PERSON>ain, WebContentsView } from 'electron';
import _ from 'lodash';
import { Integration } from '../Integration';
import { IntegrationConfig } from '../index';
import Tab from '../../tab';
import { is } from '@electron-toolkit/utils';
import path from 'node:path';
import { metrics } from '@opentelemetry/api';
import packageJson from '../../../../package.json';
import ms from 'ms';

const heartbeat = metrics.getMeter(packageJson.name).createGauge('muyan-home-heartbeat');

class Home extends Integration {
  #ctx: {
    emit?: any;
    log?: any;
  } = {};
  #homeView: WebContentsView;
  #loggedUser: string;
  #heartbeatInterval: NodeJS.Timeout;

  constructor(tab: Tab, config: IntegrationConfig) {
    super(tab, config);
    this.#ctx.log = this.getLogger();
    this.#ctx.emit = this.emit.bind(this);
    this.#homeView = tab.view;
  }

  async start() {
    this.#ctx.log.debug('home-start');
    this.#heartbeatInterval = setInterval(() => {
      heartbeat.record(Date.now(), {
        mode: import.meta.env.MODE,
        version: packageJson.version,
        instance: this.clientId,
        loggedUser: this.#loggedUser
        // env: is.dev ? 'development' : 'production'
      });
    }, ms('30s'));
    this.on('APP_CHANGED_EVENT', (data) => {
      this.#ctx.log.debug('APP_CHANGED_EVENT', data);
      this.#homeView.webContents.send('APP_CHANGED_EVENT', data);
    });
    this.on('exit', (data) => {
      this.#ctx.log.debug('card exit', data);
      this.#homeView.webContents?.send('exit', data);
    });
    this.#homeView.webContents.ipc.on('new-tab', (_event, url, options) => {
      this.#ctx.log.debug('new-tab', url, options);
      if (url === 'qianniu') {
        if (is.dev) {
          url = process.env['ELECTRON_RENDERER_URL'] + '/add-ons/qianniu/';
        } else {
          url = path.join(__dirname, '../renderer/add-ons/qianniu/index.html');
        }
      }
      this.tab.newTab(url, options);
    });
    this.#homeView.webContents.ipc.on('user-login', (_event, user) => {
      this.#loggedUser = user;
    });
    this.#homeView.webContents.ipc.on('TABS_DEl_ONENTRY', (_event, data) => {
      this.tab.currentWindow.closeTabs((tab) => {
        // @ts-ignore options is private...
        return tab.options?.integration?.id === data.id;
      });
    });
    this.#homeView.webContents.ipc.on('TABS_DEl_All', (_event, data) => {
      // @ts-ignore options is private...
      this.tab.currentWindow.closeTabs((tab) => tab?.options?.integration?.id !== 'home');
    });
  }

  async stop() {
    this.#ctx.log.debug('home-stop');
    clearInterval(this.#heartbeatInterval);
  }
}

export default Home;
