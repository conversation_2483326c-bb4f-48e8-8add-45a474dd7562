export const getCurrentUesrMessage = async (page, isSilence) => {
  await page.evaluate((silence) => {
    setInterval(() => {
      if ((window as any)?.agentStatue === 'running' || silence) {
        const pigeonChatScrollBox = document.querySelector('.pigeonChatScrollBox');
        if (pigeonChatScrollBox) {
          const childElements = pigeonChatScrollBox.children;
          if (childElements.length > 0) {
            const messageList = document.querySelector('.messageList');
            if (messageList) {
              const childElements = messageList.children[1].children;
              const childEnd = childElements[childElements.length - 1];
              const key = Object.keys(childEnd).find(
                (item) => item.indexOf('__reactInternalInstance') !== -1
              );
              if (key) {
                const data = childEnd[key].memoizedProps.children[0].props.data;
                const endItem = JSON.parse(JSON.stringify(data.at(-1)));
                const isSubmit =
                  ((endItem?.ext?.sender_role === '3' && endItem?.ext?.auto_welcome_tag === '1') ||
                    endItem?.ext?.sender_role === '1') &&
                  endItem.content !== '';

                if (isSubmit) {
                  const newData = JSON.parse(JSON.stringify(data));
                  let newItem: any = null;
                  for (let i = newData.length - 1; i > 0; i--) {
                    // sender_role 1 用户消息 2客服消息 3系统消息
                    if (newData[i]?.ext?.sender_role === '1') {
                      newItem = newData[i];
                      break;
                    }
                  }
                  if (newItem) {
                    (window as any).userMsgCreate({
                      uid: newItem.sender,
                      msg_body_list: [
                        {
                          serverId: newItem.serverId
                        }
                      ]
                    });
                  }
                }
              }
            }
          }
        }
      }
    }, 1500);
  }, isSilence);
};

export const getCurrentUserMessageApi = (request, isSilence, addSendMsgCount) => {
  if (request.url?.includes('/backstage/robot/assistant/answerRecommend')) {
    const postData = JSON.parse(request.postData || '{}');
    if (Object.keys(postData).length === 0) return null;
    // 获取用户消息
    if (postData?.type?.includes(1) && postData?.type?.includes(2)) {
      return postData;
    }

    const isUserSend = postData?.type?.length === 1 && postData?.type?.includes(2);
    if (isUserSend && !isSilence) {
      // 累计发消息数
      addSendMsgCount();
    }
    // 在静默模式下获取客服消息
    if (isSilence) {
      if (isUserSend) {
        addSendMsgCount();
        return postData;
      }
    }
    return null;
  }
};
