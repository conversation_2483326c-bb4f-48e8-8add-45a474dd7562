.app {
  font-family:
    PingFang SC,
    PingFang SC;
}
/* 整个滚动条 */
::-webkit-scrollbar {
  /* 对应纵向滚动条的宽度 */
  width: 5px !important;
  /* 对应横向滚动条的宽度 */
  height: 5px !important;
}

/* 滚动条上的滚动滑块 */
::-webkit-scrollbar-thumb {
  background: #ababab !important;
  border-radius: 5px;
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
  background-color: transparent !important;
  border-radius: 5px;
}
.unselectable {
  -webkit-user-select: none;
  /* Chrome, Safari, Opera */
  -moz-user-select: none;
  /* Firefox */
  -ms-user-select: none;
  /* Internet Explorer/Edge */
  user-select: none;
  /* 标准浏览器 */
}
/* 拖拽区域最大高度 */
.sortableModal-form {
  max-height: 350px;
  overflow-y: scroll;
}
/* icon鼠标公共样式 */
.icon-area-hover {
  border-radius: 4px;
  padding: 2px;
  cursor: pointer;
}
.icon-area-hover:hover {
  background: rgba(18, 18, 18, 0.04);
}
/* icon鼠标active显示样式 */
.icon-area-hover:active {
  background: rgba(18, 18, 18, 0.08);
}
.icon-area-delect-hover {
  color: #4e5969;
}
.icon-area-hover:hover .icon-area-delect-hover {
  color: #f23c3c;
}

.filter .ant-select-selector {
  border-radius: 2px !important;
}

.filter .ant-picker .ant-picker-input > input {
  font-size: 12px !important;
}

.ant-table-wrapper .ant-table-row-expand-icon {
  border: 1px solid #f0f0f0;
  background-color: #1d91fb;
  color: #ffffff;
  border-radius: 2px;
}

.ant-table-wrapper .ant-table-row-expand-icon:focus {
  color: #ffffff;
}

.ant-carousel .slick-prev,
.ant-carousel .slick-next {
  color: #333333 !important;
}

.slick-prev:before,
.slick-next:before {
  color: #000000 !important;
}

.ant-checkbox,
.ant-checkbox .ant-checkbox-inner {
  border-radius: 2px !important;
}

.ant-table-wrapper .ant-table-thead > tr > th {
  color: #333333 !important;
}

.text-hover--primary:hover,
.text-hover--primary:active,
.text-hover--primary:focus {
  color: #2e74ff;
}

.card-hover:hover {
  box-shadow:
    0px 4px 8px 0px rgba(0, 0, 0, 0.04),
    0px 8px 20px 0px rgba(0, 0, 0, 0.08);
}


* {
  padding: 0;
  margin: 0;
}
