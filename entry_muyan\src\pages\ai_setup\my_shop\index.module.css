.item {
  width: 230px;
  height: 78px;
  background: linear-gradient(180deg, #f7f7fc 0%, #ebeffe 100%);
  border-radius: 8px;
  padding: 14px 12px;
  margin-right: 20px;
  margin-bottom: 20px;
}

.header {
  display: flex;
  align-items: center;
}

.icon {
  width: 20px;
  height: 20px;
}

.title {
  font-weight: bold;
  font-size: 14px;
  color: #333333;
  margin-left: 6px;
}

.content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 10px;
}

.tip {
  font-size: 14px;
  color: #666666;
}

.label {
  font-size: 14px;
  color: #333333;
  line-height: 18px;
  margin-right: 6px;
}

.list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
}

.titleCss {
  width: 108px;
  height: 25px;
  font-weight: bold;
  font-size: 18px;
  color: #333333;
}

.text_button {
  font-size: 14px;
  color: #086eff;
  line-height: 18px;
  cursor: pointer;
  margin-right: 8px;
}

.text_button_active {
  color: #90beff;
}

.input {
  width: 240px;
  border-radius: 2px;
  margin-right: 16px;
}

.select {
  width: 240px;
  margin: 20px 0px;
  margin-right: 16px;
}

.state {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #808080;
}

.state_success,
.state_warning {
  display: block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 4px;
}

.state_success {
  background-color: #44d961;
}

.state_warning {
  background-color: #f2b100;
}

.edit_label {
  display: inline-block;
  width: 120px;
  text-align: right;
}
