export const msgCreate = async (
  self: {
    myqaSentMessage;
    page;
    waitingToBeSent;
    ctx;
    agentConfig;
  },
  postData,
  isSilence,
  setMyqaSentMessage,
  addTotalReceiveMsgCount
) => {
  if (!self.myqaSentMessage.includes(postData.msg_body_list[0].serverId)) {
    setMyqaSentMessage(postData.msg_body_list.map((item) => item.serverId));
    addTotalReceiveMsgCount();
    await self.page!.evaluate(() => {
      (window as any).toggleLock(false);
    });
    const resultData: any[] = await self.page!.evaluate(
      ({ id, isSilence, postData, agentConfig }) => {
        const messageList = document.querySelector('.messageList');
        const result: any[] = [];
        if (messageList) {
          const childElements = messageList.children[1].children;
          const childEnd = childElements[childElements.length - 1];
          const key = Object.keys(childEnd).find(
            (item) => item.indexOf('__reactInternalInstance') !== -1
          );
          if (key) {
            const data = childEnd[key].memoizedProps.children[0].props.data;
            const index = data.findIndex((item) => item.serverId === id);
            // 判断时候是新用户发送欢迎语
            if (data.length < 5) {
              (window as any).setTextAreaAndSend(
                postData.uid,
                agentConfig.greeting,
                agentConfig,
                'txt'
              );
            }
            const sender_role = JSON.parse(JSON.stringify(data[index] ?? {}))?.ext?.sender_role;

            if (sender_role === '1') {
              for (let i = index; i > 0; i--) {
                // sender_role 1 用户消息 2客服消息 3系统消息
                if (data[i].ext.sender_role === '1') {
                  result.push(data[i]);
                } else {
                  break;
                }
              }
            }

            if (sender_role === '2' && isSilence) {
              for (let i = index; i > 0; i--) {
                // sender_role 1 用户消息 2客服消息 3系统消息
                if (data[i].ext.sender_role === '2') {
                  result.push(data[i]);
                } else {
                  break;
                }
              }
            }
          }
        }
        return JSON.parse(JSON.stringify(result.reverse()));
      },
      {
        id: postData.msg_body_list[0].serverId,
        isSilence,
        postData,
        agentConfig: self.agentConfig
      }
    );

    if (self.waitingToBeSent) {
      clearTimeout(self.waitingToBeSent);
      self.waitingToBeSent = null;
    }
    self.waitingToBeSent = setTimeout(async () => {
      const resultDataFilter = resultData.filter((item) => {
        return !self.myqaSentMessage.includes(item.serverId);
      });
      if (self.waitingToBeSent) {
        clearTimeout(self.waitingToBeSent);
        self.waitingToBeSent = null;
      }
      await self.page!.evaluate(() => {
        (window as any).toggleLock(true);
      });
      await self.ctx.userManager!.get(postData.uid).addMsgAndRun(
        resultDataFilter.map((item) => {
          const typeMap = new Map()
            .set('[图片]', 'image')
            .set('[商品]', 'card')
            .set('[订单]', 'card')
            .set('[待收款]收到用户打款，请及时收款', 'card');
          const static_data = JSON.parse(item.ext.static_data ?? '{}');
          // 订单消息中的 order_id  普通消息中的shark_order_id
          return {
            msg: item.content,
            id: item.serverId,
            type: typeMap.get(item.content) || 'txt',
            user: item.sender,
            time: item.createTime,
            metadata: {
              shop: item.ext.shop_id || '',
              goods: {
                id: item?.ext?.shark_product_id || item?.ext?.goods_id || '',
                name: static_data?.goods_name ?? '',
                skuName: static_data?.goods_spec_desc ?? '',
                skuId: static_data?.sku_id ?? ''
              },
              ...(item.content === '[图片]'
                ? {
                    image: {
                      url: item?.ext?.imageUrl,
                      txt: item.content
                    }
                  }
                : {})
            }
          };
        }) as any[]
      );
    }, 500);
  }
};
