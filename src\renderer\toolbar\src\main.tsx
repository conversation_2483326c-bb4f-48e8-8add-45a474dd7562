import './assets/toolbar.less';

import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { ConfigProvider } from 'antd';

ReactDOM.createRoot(document.getElementById('root') as HTMLElement).render(
  <React.StrictMode>
    <ConfigProvider
      theme={{
        token: {
          /* here is your global tokens */
          borderRadius: 0
        },
        components: {
          Tabs: {
            /* here is your component tokens */
            cardGutter: -1,
            itemColor: '#595B60',
            itemHoverColor: '#595B60',
            itemSelectedColor: '#121212',
            cardBg: '#F7F8FA'
          }
        }
      }}
    >
      <App />
    </ConfigProvider>
  </React.StrictMode>
);
