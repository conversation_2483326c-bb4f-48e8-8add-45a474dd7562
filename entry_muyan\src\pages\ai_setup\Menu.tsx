/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import { observer } from 'mobx-react-lite';
import arrowUp from '@/assets/icons/arrow_up.svg';
type Props = {
  onChange: (value: string) => void;
};
const Menu: React.FC<Props> = observer(({ onChange }) => {
  const [show, setShow] = useState(true);
  const totalStyle: any = {
    width: '104px',
    // height: "36px",
    fontSize: '14px',
    lineHeight: '36px',
    color: '#1D2129',
    marginBottom: '4px',
    padding: '0 12px',
    display: 'flex',
    alginItems: 'center',
    cursor: 'pointer'
    // justifyContent: "space-between",
  };
  const itemStyle: any = {
    width: '104px',
    height: '36px',
    borderRadius: '8px',
    fontSize: '12px',
    lineHeight: '36px',
    color: '#595B60',
    marginBottom: '4px',
    textAlign: 'center',
    cursor: 'pointer'
  };
  const activeStyle: any = {
    width: '104px',
    height: '36px',
    borderRadius: '8px',
    fontSize: '12px',
    lineHeight: '36px',
    backgroundColor: 'rgba(46, 116, 255, 0.08)',
    marginBottom: '4px',
    color: '#2E74FF',
    textAlign: 'center',
    cursor: 'pointer'
  };

  const [activeKey, setActiveKey] = React.useState('1');

  const change = (val: string): void => {
    setActiveKey(val);
    onChange(val);
  };
  return (
    <div
      style={{
        width: '120px',
        height: '100%',
        backgroundColor: '#fff',
        padding: '8px',
        borderRight: ' 1px solid #DCE0E8',
        boxSizing: 'border-box'
      }}
    >
      <div
        style={totalStyle}
        onClick={() => {
          setShow(!show);
        }}
      >
        <span>话术管理</span>
        <img
          src={arrowUp}
          style={{
            marginLeft: '8px',
            transform: `rotate(${show ? 0 : 180}deg)`
          }}
        />
      </div>
      {show && (
        <div
          style={activeKey === '1' ? activeStyle : itemStyle}
          className="unselectable"
          onClick={() => change('1')}
        >
          话术配置
        </div>
      )}
      {/* <div
        style={activeKey === "2" ? activeStyle : itemStyle}
        className="unselectable"
        onClick={() => change("2")}
      >
        我的店铺
      </div> */}
      <div
        style={activeKey === '2' ? activeStyle : itemStyle}
        className="unselectable"
        onClick={() => change('2')}
      >
        <span style={{ fontSize: 14, marginLeft: '-20px' }}>商品管理</span>
      </div>

      {/* {userInfo.permission.split(",").includes("documentary") && (
        <div
          style={activeKey === "4" ? activeStyle : itemStyle}
          className="unselectable"
          onClick={() => change("4")}
        >
          智能跟单
        </div>
      )} */}
    </div>
  );
});

export default Menu;
