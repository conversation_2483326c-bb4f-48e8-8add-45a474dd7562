/* antd公共样式修改 */
.ant-modal-content {
  padding: 0 !important;
}

.ant-modal-header {
  padding: 16px 30px !important;
  border: 1px solid #dadada !important;
  margin-bottom: 0 !important;
}
.ant-drawer-extra {
  height: 24px;
  margin-top: -10px;
}

.ant-modal-body {
  width: 100%;
}
.ant-upload-select {
  background-color: #fff !important;
  border-color: #dadada;
}
.ant-upload-list-item {
  padding: 0 !important;
  border-radius: 10px;
}

.ant-upload-list-item::before {
  width: 100% !important;
  height: 100% !important;
  left: 0;
  top: 0;
  border-radius: 10px;
  overflow: hidden;
}

.ant-upload-list-item-thumbnail {
  border-radius: 10px;
}

.ant-upload-list-item-action.ant-btn {
  position: absolute;
  bottom: -40px;
  border-radius: 0 0 10px 10px;
  left: 0;
  background: rgba(0, 0, 0, 0.45);
  width: 100%;
}

.ant-upload-list-item-action.ant-btn:hover {
  background: rgba(0, 0, 0, 0.45);
}
.ant-btn-text:not(:disabled):not(.ant-btn-disabled):hover {
  background: rgba(0, 0, 0, 0.45);
}
.ant-popconfirm .ant-popover-content .ant-popover-inner {
  padding: 20px 24px !important;
  min-width: 424px !important;
  height: 160px !important;
  box-sizing: border-box;
}

.ant-popconfirm
  .ant-popover-content
  .ant-popover-inner
  .ant-popconfirm-message-text
  .ant-popconfirm-title {
  font-size: 16px !important;
  font-weight: 500 !important;
  line-height: 24px !important;
  letter-spacing: 0px;
  color: #121212 !important;
}

.ant-popconfirm
  .ant-popover-content
  .ant-popover-inner
  .ant-popconfirm-message-text
  .ant-popconfirm-description {
  font-size: 14px !important;
  font-weight: normal;
  line-height: 22px !important;
  letter-spacing: 0px;
  color: #595b60 !important;
  margin-top: 8px !important;
}

.ant-popconfirm .ant-popover-content .ant-popconfirm-message-icon {
  margin-right: 8px;
}

.ant-popconfirm .ant-popover-content .ant-popconfirm-message-icon svg {
  width: 24px;
  height: 24px;
}

.ant-tabs-nav::before {
  border-bottom: none !important;
}
.ant-tabs-tab {
  padding: 5px 0 !important;
}
.ant-tabs-ink-bar {
  height: 3px !important;
  border-radius: 2px !important;
}
.ant-form-item-control-input-content {
  display: flex;
  align-items: center;
}
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item:hover,
.ant-dropdown-menu-submenu .ant-dropdown-menu .ant-dropdown-menu-item:hover,
.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-submenu-title:hover {
  background-color: rgba(46, 116, 255, 0.08) !important;
  font-weight: 500;
}
.ant-form-item-control-input-content {
  display: flex;
  align-items: center;
}
.ant-table-cell::before {
  display: none;
}
.ant-form-item-required::before {
  display: inline-block;
  margin-inline-end: var(--ant-margin-xxs);
  color: var(--ant-form-label-required-mark-color);
  font-size: var(--ant-font-size);
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}

.ant-dropdown-menu-submenu-expand-icon.ant-dropdown-menu-submenu-arrow {
  display: none !important;
}
.ant-table-cell .ant-upload-wrapper {
  width: 64px;
  height: 64px;
}
.ant-table-cell .ant-upload-select {
  height: 64px !important;
  line-height: 64px !important;
}
.ant-table-cell .ant-upload-list-item-container {
  height: 64px !important;
  line-height: 64px !important;
}
.ant-table-cell .ant-upload-list-item-action.ant-btn {
  bottom: 0px;
}
.ant-form-item .ant-form-item-label > label::after {
  content: "";
}
.ant-form-item .ant-form-item-label {
  margin-right: 8px;
}
.ant-input,
.ant-input-affix-wrapper {
  border-radius: 8px !important;
}
.ant-table-cell {
  color: #595b60;
}
.ant-table-wrapper .ant-table-thead > tr > th {
  border-color: #e5e5e5 !important;
}
.ant-table-wrapper .ant-table-tbody > tr > td {
  border-color: #f0f0f0 !important;
}
.ant-form-css-var {
  margin-bottom: 16px;
}
