<!doctype html>
<html>

<head>
  <meta charset="UTF-8" />
  <title>千牛</title>
  <!-- https://developer.mozilla.org/en-US/docs/Web/HTTP/CSP -->
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:" />
</head>

<body>
  <div id="root">
    助手运行状态：<span id="status">未启动</span>
    千牛运行状态：<span id="status2">未启动</span>
    <input id="name" type="text" />
  </div>
  <a href="https://www.baidu.com" target="_self">11111</a>
  <script>
    window.changeClientStatus = function (status) {
      document.getElementById('status').innerText = status;
    }
    window.changeExeStatus = function (status) {
      document.getElementById('status2').innerText = status;
    }
  </script>
  <script>
    window.onmessage = (event) => {
      // event.source === window means the message is coming from the preload
      // script, as opposed to from an <iframe> or other source.
      if (event.source === window && event.data === 'port') {
        const [port] = event.ports
        // Once we have the port, we can communicate directly with the main
        // process.
        port.onmessage = (event) => {
          console.log('from main process:', event.data)
        }
        port.postMessage({
          source: "qianniu",
          handshake: true,
        })
      }
    }
    const beforeUnloadHandler = (event) => {
      // Recommended
      event.preventDefault();

      // Included for legacy support, e.g. Chrome/Edge < 119
      event.returnValue = true;
    };

    const nameInput = document.querySelector("#name");

    nameInput.addEventListener("input", (event) => {
      if (event.target.value !== "") {
        window.addEventListener("beforeunload", beforeUnloadHandler);
      } else {
        window.removeEventListener("beforeunload", beforeUnloadHandler);
      }
    });
  </script>
  <!--<script type="module" src="/add-ons/qianniu/src/main.tsx"></script>-->
</body>

</html>
