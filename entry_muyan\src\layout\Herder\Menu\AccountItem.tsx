type Props = {
  data: any;
};
const AccountItem = ({ data }: Props) => {
  const user: any = localStorage.getItem('user');
  const currentUsername: any = JSON.parse(user)?.username;
  return (
    <div
      style={{
        width: '170px',
        height: '44px',
        display: 'flex',
        alignItems: 'center',
        position: 'relative',
        margin: '-5px -12px',
        borderRadius: '4px',
        padding: '4px 12px',
        backgroundColor:
          currentUsername === data.username ? 'rgba(46, 116, 255, 0.08)' : 'transparent',
        ...(currentUsername === data.username ? { fontWeight: '500' } : {})
      }}
    >
      <div>
        <div
          style={{
            fontSize: '14px',
            color: '#121212',
            lineHeight: '13px',
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <span>{data?.username ?? '-'}</span>
        </div>
        <div
          style={{
            fontSize: '11px',
            color: '#808080',
            lineHeight: '13px',
            marginTop: '4px'
          }}
        >
          公司的名称
        </div>
      </div>
    </div>
  );
};

export default AccountItem;
