import style from './index.module.css';
import Tabs from './tabs';
import List from './list';
import AddButton from './add_button';
import { Button } from 'antd';
import { useEffect, useState } from 'react';
import IconRefresh from '@/assets/icons/icon_refresh.svg?react';
type Props = {
  data: any[];
  getData: (val?: string) => void;
  updateConfigState: (id: string, state) => void;
};
const Work = ({ data, getData, updateConfigState }: Props): React.ReactElement => {
  const [tabsValue, setTabsValue] = useState('all');
  const [dataClass, setDataClass] = useState({});
  const [accKeys, setAccKeys] = useState<string[]>([]);
  useEffect(() => {
    setDataClass(
      data.reduce((acc, item) => {
        const key = item.platformId;
        if (!acc[key]) {
          acc[key] = [];
        }
        acc[key].push(item);
        return acc;
      }, {})
    );
    const keys: string[] = [];
    data.forEach((item) => {
      if (!keys.includes(item.platformId)) {
        keys.push(item.platformId);
      }
    });
    if (!keys.includes(tabsValue)) {
      setTabsValue('all');
    }
    setAccKeys(keys);
  }, [data]);
  return (
    <div className={style.work}>
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between'
        }}
      >
        <Tabs
          value={tabsValue}
          accKeys={accKeys}
          onChange={(e) => {
            console.log(e);
            setTabsValue(e);
          }}
        />
        {/* <AddButton data={data} getData={getData} /> */}
        <Button
          className={style.refresh_button}
          icon={<IconRefresh />}
          onClick={() => {
            getData();
          }}
        ></Button>
      </div>
      <List
        data={tabsValue === 'all' ? data : (dataClass[tabsValue] ?? [])}
        getData={getData}
        updateConfigState={updateConfigState}
      />
    </div>
  );
};

export default Work;
