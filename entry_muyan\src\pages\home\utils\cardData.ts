import douyin from '@/assets/imgs/douyin.png';
import qianniu from '@/assets/imgs/qianniu.png';
export const cardData = [
  {
    title: '千牛-天猫',
    src: qianniu,
    id: '6868833650239488',
    type: 'qianniu',
    name: '天猫',
    isOpen: true
  },
  {
    title: '千牛-淘宝',
    src: qianniu,
    id: '6868833650239489',
    type: 'qianniu',
    name: '淘宝',
    isOpen: false
  },
  {
    title: '抖店',
    src: douyin,
    id: '6868833650239489',
    type: 'doudian',
    name: '抖音',
    isOpen: false
  }
];

export const cardDataMap = () => {
  const cardMap = new Map<string, any>();

  cardData.forEach((item) => {
    cardMap.set(item.id, item.src);
  });

  return cardMap;
};

export const openCardPage = (key: string, id: string | number): boolean => {
  const caseKey = cardData.find((item) => item.id === key)?.type;
  console.log(caseKey);
  switch (caseKey) {
    case 'qianniu':
      window.xBrowser?.send('new-tab', 'qianniu', {
        integration: {
          id,
          name: 'qianniu',
          options: {
            endpoint:
              (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
              '/muyan/api/aiAgentConfig/getAiAgentConfigDetail?aiAgentConfigId=' +
              id,
            aiAgentConfigId: id,
            token: localStorage.getItem('accessToken'),
            username: JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? ''
          }
        }
      });
      return true;
    case 'qianniu-silence':
      window.xBrowser?.send('new-tab', 'qianniu', {
        integration: {
          id,
          name: 'qianniu',
          options: {
            endpoint:
              (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
              '/muyan/api/aiAgentConfig/getAiAgentConfigDetail?aiAgentConfigId=' +
              id,
            aiAgentConfigId: id,
            token: localStorage.getItem('accessToken'),
            username: JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? '',
            isSilence: true
          }
        },
        advances: {
          overrideTitle: '千牛(静默)'
        }
      });
      return true;
    case 'qianniu-assistant':
      window.xBrowser?.send('new-tab', 'qianniu', {
        integration: {
          id,
          name: 'qianniu',
          options: {
            endpoint:
              (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
              '/muyan/api/aiAgentConfig/getAiAgentConfigDetail?aiAgentConfigId=' +
              id,
            aiAgentConfigId: id,
            token: localStorage.getItem('accessToken'),
            username: JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? '',
            isAssistant: true
          }
        },
        advances: {
          overrideTitle: '千牛(辅助接待)'
        }
      });
      return true;

    case 'doudian':
      window.xBrowser?.send('new-tab', 'https://fxg.jinritemai.com/login/common', {
        integration: {
          id,
          name: 'doudian_v2',
          // version: '2.0',
          options: {
            endpoint:
              (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
              '/muyan/api/aiAgentConfig/getAiAgentConfigDetail?aiAgentConfigId=' +
              id,
            token: localStorage.getItem('accessToken'),
            username: JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? ''
            // isAssistant: true
          }
        }
      });
      return true;

    case 'doudian-silence':
      window.xBrowser?.send('new-tab', 'https://fxg.jinritemai.com/login/common', {
        integration: {
          id,
          name: 'doudian_v2',
          // version: '2.0',
          options: {
            endpoint:
              (import.meta.env.VITE_DEV_ENDPOINT ?? import.meta.env.gateway) +
              '/muyan/api/aiAgentConfig/getAiAgentConfigDetail?aiAgentConfigId=' +
              id,
            token: localStorage.getItem('accessToken'),
            username: JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? '',
            isSilence: true
          }
        }
      });
      return true;

    default:
      return false;
  }
};
