import got from 'got';
import ms from 'ms';

// 创建自定义实例并添加钩子
const customGot = got.extend({
  hooks: {
    beforeError: [
      (error) => {
        const { options, response } = error;
        error.name = `${options.method} ${options.url} ${error.name}`;
        if (response && response.body) {
          // error.name = `${options.method} ${response.url}`;
          error.message = `${error.message} ${response.body}`;
        }

        return error;
      }
    ]
  },
  timeout: ms('30s')
});

export default customGot;
