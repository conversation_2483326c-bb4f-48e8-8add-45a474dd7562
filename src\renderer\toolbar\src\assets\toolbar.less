//@property --favicon-url {
//  syntax: "<url>";
//  inherits: true;
//  initial-value: url("data:image/png;base64,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");
//}
html {
  body {
    margin: 0;
    background-color: #F7F8FA;
  }
}
::-webkit-scrollbar {
  display: none;
}
.ant-tabs {
  .ant-tabs-nav {
    margin-bottom: 0;
    .ant-tabs-tab {
      height: 36px;
      font-size: 12px;
      &.ant-tabs-tab-active {
        .ant-tabs-tab-btn {
          .title {
            svg {
              g {
                fill: #121212;
              }
            }
          }
        }
      }
      .ant-tabs-tab-btn {
        max-width: 158px;
        overflow: hidden;
        .title {
          display: flex;
          align-items: center;
          position: relative;
          img {
            width: 16px;
            height: 16px;
            margin-left: 1px;
            margin-right: 8px;
            flex: none;
            visibility: hidden;
          }
          .anticon {
            position: absolute;
          }
          svg {
            g {
              fill: #595B60;
            }
          }
        }
      }
    }
    .ant-tabs-nav-add {
      min-height: 36px;
    }
  }
  .ant-tabs-tab-remove {
    color: #777;
  }
}
.address-bar {
  display: flex;
}
