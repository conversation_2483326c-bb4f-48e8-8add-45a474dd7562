/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import Analyze from './Analyze';
import { AIModelDAG } from './LogFlowChart';
import { Drawer, Tabs } from 'antd';
import type { TabsProps } from 'antd';
type Props = {
  showLog: boolean;
  onClose: (val: boolean) => void;
  metadata: any;
  logViewKey: string;
  setLogViewKey: (val: string) => void;
  currentAssistant: any;
};
const items: TabsProps['items'] = [
  {
    key: '1',
    label: '分析'
  },
  {
    key: '2',
    label: '过程'
  }
];
const Log: React.FC<Props> = ({
  showLog,
  onClose,
  metadata,
  logViewKey,
  setLogViewKey,
  currentAssistant
}) => {
  const onTabChange = (key: string) => {
    setLogViewKey(key);
  };
  return (
    <Drawer
      title=""
      destroyOnClose
      onClose={() => {
        onClose(false);
      }}
      open={showLog}
      extra={<Tabs defaultActiveKey="1" items={items} onChange={onTabChange} />}
    >
      {logViewKey === '1' && <Analyze metadata={metadata} currentAssistant={currentAssistant} />}
      {logViewKey === '2' && <AIModelDAG metadata={metadata} />}
    </Drawer>
  );
};

export default Log;
