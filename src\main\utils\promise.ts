export function createPromiseCapability<T>() {
  let resolve,
    reject,
    isSettled = false;
  const promise = new Promise<T>((res, rej) => {
    resolve = (val: T) => {
      isSettled = true;
      res(val);
    };
    reject = (reason) => {
      isSettled = true;
      rej(reason);
    };
  });
  return {
    promise,
    resolve,
    reject,
    get isSettled() {
      return isSettled;
    }
  };
}
