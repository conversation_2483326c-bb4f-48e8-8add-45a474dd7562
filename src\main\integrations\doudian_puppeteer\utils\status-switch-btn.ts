export const statusSwitchBtn = async (page) => {
  await page!.evaluate(() => {
    (window as any).agentStatue = 'pause';
    const btn = document.createElement('button');
    btn.id = 'pause-agent-btn';

    btn.style.position = 'fixed';
    btn.style.top = '75px';
    btn.style.right = '600px';
    btn.style.zIndex = '1000000';
    btn.style.padding = '10px 20px';
    btn.style.fontSize = '16px';
    btn.style.color = '#ffffff';
    btn.style.border = 'none';
    btn.style.borderRadius = '5px';
    btn.style.cursor = 'pointer';
    document.body.append(btn);

    (window as any).updateAgentStatue('pending');

    btn.addEventListener('click', () => {
      let state = (window as any).agentStatue;
      if (state === 'pending') return;
      if (state === 'running') {
        state = 'pause';
      } else {
        state = 'running';
      }
      (window as any).updateAgentStatue(state);
    });
  });
};
