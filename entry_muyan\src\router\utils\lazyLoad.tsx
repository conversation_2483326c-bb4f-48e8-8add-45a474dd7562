import React, { Suspense } from 'react';
import { Spin } from 'antd';
const lazyLoad = (
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  Comp: React.LazyExoticComponent<any>
): React.ReactNode => {
  return (
    <Suspense
      fallback={
        <Spin
          size="large"
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100vh'
          }}
        />
      }
    >
      <Comp />
    </Suspense>
  );
};

export default lazyLoad;
