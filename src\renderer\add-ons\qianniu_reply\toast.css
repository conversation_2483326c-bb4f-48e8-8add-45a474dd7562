body {
  margin: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei';
  overflow: hidden;
}

.status-wrap {
  padding: 20px;
}
.status-wrap  .top {
  display: flex;
  position: relative;
  align-items: center;
}

.timer {
  width: 52px;
  height: 52px;
  border-radius: 52px;
  background-color: #F23C3C;
  color: #fff;
  margin: 2px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  font-weight: 550;

  /*position: absolute;*/
  /*right: 0;*/
  /*top: 0;*/
  /*font-size: 12px;*/
  /*font-weight: 500;*/
  /*line-height: 22px;*/
  /*border: 1px solid #FF9999;*/
  /*padding: 0 12px;*/
  /*border-radius: 13px;*/
  /*color: #F23C3C;*/
}

.right {
  margin-left: 12px;
}

.right h3 {
  font-size: 16px;
  line-height: 24px;
  margin: 0;
  font-weight: 551;
}
.wait-reply {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.confirm {
  width: 240px;
}
.right .content {
  font-size: 12px;
  line-height: 20px;
  color: #4E5969;
}
.footer {
  display: flex;
  justify-content: flex-end;
  /*margin-top: 8px;*/
}
.status-wrap .ant-btn {
  /*float: right;*/
  /*background: #2E74FF;*/
  border-radius: 22px;
  font-size: 14px;
  height: 40px;
  margin-left: 12px;
  /*margin-top: 8px;*/
}

.ant-btn.ant-btn-variant-outlined {
  color: #2E74FF;
  border-color: #2E74FF;
}
.ant-btn.ant-btn-primary {
  background: #2E74FF;
}

.ant-popover {
  width: 346px;
  height: 80px;
  /*height: 72px;*/
}

.ant-popover .ant-popover-inner {
  padding: 20px;
}

.ant-popover .ant-popconfirm-inner-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.ant-popover .ant-popconfirm-message {
  margin-bottom: 0;
  align-items: center;
}

.ant-popover .ant-popconfirm-message .ant-popconfirm-message-icon {
  margin: 5px 8px 0 0;
}

.ant-popconfirm .ant-popconfirm-message .ant-popconfirm-title:only-child {
  font-weight: 550;
  font-size: 16px;
  line-height: 20px;
}

.ant-popover .ant-btn {
  border-radius: 22px;
  font-size: 14px;
  height: 32px;
  padding: 0 14px;
}

.ant-popconfirm .ant-popconfirm-buttons button {
  margin-inline-start: 12px;
}
