/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { getConfig } from '@/api/myqaApi';
import { message } from 'antd/lib';
export const Synchronization: React.FC<{
  title: string;
  open: boolean;
  shopData: string[];
  setOpen: (bool: boolean) => void;
}> = ({ title, open, /*shopData,*/ setOpen }) => {
  const configStates = JSON.parse(localStorage.getItem('configStates') ?? '[]');
  const [configs, setConfigs] = useState<any[]>([]);
  useEffect(() => {
    if (open) {
      getConfig().then((res) => {
        const data = res.data.data.reverse().map((item) => {
          return {
            name: item.mall_name,
            id: item.id
          };
        });
        setConfigs(data);
      });
    }
  }, [open]);

  const onOk = () => {
    // window.ipcRenderer.send('SYNC_GOODS_EVENT', { mallNames: shopData });
    setOpen(false);
    message.success('已开始同步商品,预计需要几分钟时间,请勿再次点击同步!');
  };
  return (
    <Modal
      open={open}
      footer={configStates.filter((item) => item.state === 'ai').length > 0}
      setOpen={setOpen}
      width={500}
      onOk={onOk}
      title={title}
    >
      <div
        style={{
          padding: '50px 20px 30px'
        }}
      >
        <div>
          {configStates.filter((item) => item.state === 'ai').length > 0 ? (
            <>
              <span>本次会同步</span>
              {configStates
                .filter((item) => item.state === 'ai')
                .map((item, index) => {
                  const congfigItem = configs.find((config) => config.id === item.id);
                  if (congfigItem) {
                    return (
                      <span
                        key={index}
                        style={{
                          padding: '0px 3px'
                        }}
                      >
                        {congfigItem.name}
                      </span>
                    );
                  } else {
                    return <span key={index}></span>;
                  }
                })}
              <span>
                账户对应的店铺中的商品, 剩余未启动账户对应店铺中的商品将不会被同步,是否执行同步
              </span>
            </>
          ) : (
            '没有智能客服在启动中,本次不会同步商品'
          )}
        </div>
      </div>
    </Modal>
  );
};
