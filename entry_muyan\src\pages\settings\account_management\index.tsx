import React, { useEffect, useState } from 'react';
import { Table, message, But<PERSON>, Popconfirm, Pagination } from 'antd';
import type { TableColumnsType } from 'antd';
import style from './index.module.css';
import AddAccount from './AddAccount';
import UpdateAccount from './UpdateAccountPassword';
import UpdateAccountRole from './UpdateAccountRole';
import { getUser, deleteUser, getPermissions, getUserRole } from '@/api/myqaApi';
import Modal from '@/components/Modal';
import dayjs from 'dayjs';

const AccountManagement: React.FC = () => {
  const [data, setData] = useState<any>([]);
  const [isAdd, setIsAdd] = useState<any>(false);
  const [isDelete, setIsDelete] = useState<any>(false);
  const [isUpdate, setIsUpdate] = useState<any>(false);
  const [roleList, setRoleList] = useState<any>([]);
  const [open, setOpen] = useState(false);
  const [openPassword, setOpenPassword] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [openRole, setOpenRole] = useState(false);
  const [editUserId, setEditUserId] = useState('');
  const [editUserName, setEditUserName] = useState('');
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [loading, setLoading] = useState(true);
  const [total, setTotal] = useState(0);
  const buttonProps = {
    style: {
      height: '32px',
      width: '60px',
      marginTop: '28px'
    }
  };
  const roleDict = {
    SUPER_ADMIN: '超级管理员',
    COMPANY_SUPER_ADMIN: '企业超级管理员',
    COMPANY_ADMIN: '企业管理员',
    AI_TRAINER: 'AI训练师',
    MEMBER: '普通成员'
  };

  const columns: TableColumnsType<any> = [
    // /  { title: "用户名", dataIndex: "accountName" },
    {
      title: '账号',
      dataIndex: 'user_name',
      render: (val) => <span style={{ color: '#121212' }}>{val}</span>
    },
    { title: '角色', dataIndex: 'role_code', render: (val) => roleDict[val] },
    { title: '企业', dataIndex: 'company_name' },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      render: (val) => (val ? dayjs(val).format('YYYY/MM/DD HH:mm:ss') : '')
    },
    { title: '创建人', dataIndex: 'creator_name' },
    {
      title: '操作',
      dataIndex: '',
      fixed: 'right',
      render: (r) => (
        <>
          {JSON.parse(localStorage.getItem('user') ?? '{}')?.username !== r.username && (
            <>
              {isUpdate && (
                <span
                  className={`${style.text_button} unselectable`}
                  onClick={() => {
                    setEditUserId(r.user_id);
                    setEditUserName(r.user_name);
                    setOpenPassword(true);
                  }}
                >
                  修改密码
                </span>
              )}
              {/* <span
                  className={`${style.text_button} unselectable`}
                  onClick={() => {
                    setEditUserId(r.userId);
                    setOpenRole(true);
                  }}
                >
                  修改角色
                </span> */}
              {isDelete && (
                <Popconfirm
                  placement="bottomRight"
                  title="确认删除该账号？"
                  description="删除后将无法找回，需要重新添加账号"
                  okText="确认"
                  cancelText="取消"
                  cancelButtonProps={buttonProps}
                  okButtonProps={buttonProps}
                  onConfirm={() => {
                    deleteUser(r.user_id).then(() => {
                      getData();
                      setDeleteOpen(false);
                      message.success('删除成功');
                    });
                  }}
                >
                  <span className={`${style.text_button_delete}`}>删除</span>
                </Popconfirm>
              )}
            </>
          )}
        </>
      )
    }
  ];

  useEffect(() => {
    setWidth(getWindowWidth());
    window.addEventListener('resize', handleResize);
    getData();
    getPermissionsList();
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  const [width, setWidth] = useState<number>(0);
  const getWindowWidth = (): number => {
    return (
      (window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) - 288
    );
  };
  const handleResize = (): void => {
    setWidth(getWindowWidth());
  };

  function getData(page = 1, limit = 10) {
    setLoading(true);
    setPage(page);
    setPageSize(limit);
    getUser({
      page_num: page,
      page_size: limit
      // name,
      // roleId,
    })
      .then((res: any) => {
        setData(res.data);
        setTotal(res.total_size);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  }

  function getPermissionsList() {
    getPermissions().then((res) => {
      if (res.data?.length > 0) {
        res.data.map((item: any) => {
          if (item.permission_code === 'add_user') {
            setIsAdd(true);
          }
          if (item.permission_code === 'set_user_password') {
            setIsUpdate(true);
          }
          if (item.permission_code === 'delete_user') {
            setIsDelete(true);
          }
        });
        getRole();
      }
    });
  }
  function getRole() {
    getUserRole().then((res) => {
      if (res.data.role_code === 'SUPER_ADMIN') {
        setRoleList([
          {
            name: '超级管理员',
            value: 'SUPER_ADMIN'
          },
          {
            name: '企业管理员',
            value: 'COMPANY_ADMIN'
          },
          {
            name: '普通成员',
            value: 'MEMBER'
          }
        ]);
      }
      if (res.data.role_code === 'COMPANY_ADMIN') {
        setRoleList([
          {
            name: '企业管理员',
            value: 'COMPANY_ADMIN'
          },
          {
            name: '普通成员',
            value: 'MEMBER'
          }
        ]);
      }
      if (res.data.role_code === 'COMPANY_SUPER_ADMIN') {
        setRoleList([
          {
            name: '企业超级管理员',
            value: 'COMPANY_SUPER_ADMIN'
          },
          {
            name: 'AI训练师',
            value: 'AI_TRAINER'
          }
        ]);
      }
    });
  }

  return (
    <div
      style={{
        height: '100%',
        padding: '24px',
        width: '100%'
      }}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px'
        }}
      >
        <div className="filter">
          <span className={style.settingTitle}>设置</span>
        </div>
        {isAdd && (
          <Button
            style={{
              width: '96px',
              height: '36px',
              lineHeight: '36px'
            }}
            type="primary"
            onClick={() => {
              setOpen(true);
            }}
          >
            添加账户
          </Button>
        )}
      </div>

      <div
        style={{
          width: '100%',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          boxSizing: 'border-box'
        }}
      >
        <Table
          loading={loading}
          columns={columns}
          dataSource={data ?? []}
          scroll={{
            x: width,
            scrollToFirstRowOnChange: true,
            y: total > 10 ? 'calc(100vh - 292px)' : undefined
          }}
          pagination={false}
        />
      </div>
      {total > 10 && (
        <div
          style={{
            width: ' calc(100% - 136px)',
            height: '56px',
            padding: '0 24px ',
            background: '#fff',
            borderRadius: '2px 2px 0px 0px;',
            boxSizing: 'border-box',
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            boxShadow:
              '0px 6px 16px 0px rgba(0, 0, 0, 0.08),0px -9px 28px 8px rgba(0, 0, 0, 0.05),0px 3px 6px -4px rgba(0, 0, 0, 0.12)',
            position: 'fixed',
            bottom: '0',
            zIndex: 999
          }}
        >
          <Pagination
            total={total}
            pageSize={pageSize}
            current={page}
            showSizeChanger
            showQuickJumper
            onChange={(page, pageSize) => {
              getData(page, pageSize);
            }}
          />
        </div>
      )}
      <AddAccount
        open={open}
        setOpen={setOpen}
        onSubmit={() => {
          getData();
        }}
        roleList={roleList}
      />
      <UpdateAccount
        open={openPassword}
        userId={editUserId}
        userName={editUserName}
        setOpen={setOpenPassword}
        onSubmit={() => {
          getData();
        }}
      />
      <UpdateAccountRole
        open={openRole}
        userId={editUserId}
        setOpen={setOpenRole}
        onSubmit={() => {
          getData();
        }}
      />
      <Modal
        title="提示"
        open={deleteOpen}
        width={400}
        onOk={() => {
          deleteUser(editUserId).then(() => {
            getData();
            setDeleteOpen(false);
            message.success('删除成功');
          });
        }}
        setOpen={setDeleteOpen}
      >
        <div
          style={{
            textAlign: 'center',
            padding: '20px'
          }}
        >
          是否删除该账户？
        </div>
      </Modal>
    </div>
  );
};

export default AccountManagement;
