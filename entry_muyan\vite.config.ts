import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import svgr from 'vite-plugin-svgr';
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons';
// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd(), '');
  // console.log('env', env);
  return {
    base: './',
    resolve: {
      alias: {
        '@': resolve(__dirname, './src/')
      }
    },
    plugins: [
      react(),
      process.env.NODE_ENV === 'production'
        ? sentryVitePlugin({
            authToken: process.env.SENTRY_AUTH_TOKEN,
            org: 'sentry',
            project: 'electron',
            url: 'https://sentry.dongchacat.cn'
          })
        : null,
      svgr()
      // createSvgIconsPlugin({
      //   // 指定需要缓存的图标文件夹
      //   iconDirs: [resolve(process.cwd(), 'src/assets/icons')],
      //   // 指定symbolId格式
      //   symbolId: 'icon-[dir]-[name]'
      // })
    ],
    esbuild: {
      pure: ['console.log', 'debugger']
    },
    build: {
      sourcemap: true
      // outDir: '/out/entry_muyan'
    },
    server: {
      host: '0.0.0.0',
      port: 8172,
      proxy: {
        // '/api': {
        //   target: 'https://test.endpoint.dongchacat.cn/',
        //   changeOrigin: true
        // },
        // '/myuser': {
        //   target: 'https://test.endpoint.dongchacat.cn/',
        //   changeOrigin: true
        // },
        // 所有接口都只用 message / muyan
        '/message': {
          //
          target: 'https://test.endpoint.dongchacat.cn/',
          changeOrigin: true
        },
        '/muyan': {
          target: 'https://test.endpoint.dongchacat.cn/',
          changeOrigin: true
        }
      }
    },
    define: Object.entries(env).reduce((prev: { [key: string]: string }, [key, value]) => {
      prev[`import.meta.env.${key}`] = JSON.stringify(value);
      return prev;
    }, {})
  };
});
