/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect } from 'react';
import pdd from './pdd.json';
import { UploadImg } from './UploadImg';
import { getJsonData } from './utils/getJsonData';
import { useState, useRef } from 'react';
// import { MetadataEditForm } from "./MetadataEditFrom";
import { EditForm } from './EditForm';
import { message, Button } from 'antd';
import {
  getAgentium,
  addAgentium,
  platformData,
  platformConfig,
  addPlatformConfig,
  updatePlatformConfig
} from '@/api/myqaApi';
import { Spin } from 'antd';
import PlatformInfoEdit from './PlatformInfoEdit';
import cloneDeep from 'lodash-es/cloneDeep';
type PlatformInfo = {
  aiGoodSku: string;
  aiGoodSkuid: string;
  pcode: string;
  img: string;
  aiGoodSpec: string;
  mall_name: string;
  item_id: number;
};
const titleStyle = {
  fontWeight: '500',
  fontSize: '20px',
  color: '#121212'
};
const Transferlabor: React.FC = () => {
  const handleCancel = (): void => {
    getMetadata();
    setIsEdit(false);
  };
  const handleOk = (): void => {
    if (editFormRef?.current) {
      editFormRef.current.submit();
      setIsEdit(false);
    }
  };
  const [pressImgs, setPressImgs] = useState<string[]>([]);
  const editFormRef = useRef<any>(null);
  const [value, setValue] = useState<string>('{}');
  const [agentium, setAgentium] = useState<any>({});
  const [jsonData, setJsonData] = useState<any>(null);
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [platform_info, setPlatformInfo] = useState<PlatformInfo[]>([]);
  const [attribute, setAttribute] = useState<any[]>([]);
  const [platformId, setPlatformId] = useState<string>('');
  const onChange = (value: string): void => {
    addAgentium({
      version: agentium.version,
      name: agentium.name,
      assistant_id: localStorage.getItem('assistantId'),
      meta_data: JSON.parse(value)
    }).then(() => {
      updatePlatformConfig(platformId, platform_info).then(() => {
        getMetadata();
        message.success('配置已修改');
      });
    });
  };
  useEffect(() => {
    getMetadata();
  }, []);

  const getMetadata = (): void => {
    setJsonData(null);
    Promise.all([
      getAgentium({
        page_num: 1,
        page_size: 1,
        assistant_id: localStorage.getItem('assistantId')
      }),
      platformData(),
      platformConfig()
    ]).then((res) => {
      setAgentium(res[0].data.data[0]);
      setAttribute(res[0].data?.data?.[0]?.meta_data?.attribute ?? []);
      setValue(JSON.stringify(res[0].data?.data?.[0]?.meta_data ?? pdd ?? {}));
      setJsonData(getJsonData(JSON.stringify(res[0].data?.data?.[0]?.meta_data ?? pdd ?? {})));
      setPressImgs(res[0].data?.data?.[0]?.meta_data?.pressImgs ?? []);
      if (res[2].data.length === 0) {
        addPlatformConfig([]).then((addRes) => {
          setPlatformId(addRes.data.id);
          setPlatformInfo(
            diffPlatformInfo(res[1].data, JSON.stringify(addRes?.data?.meta_data ?? {}))
          );
        });
      } else {
        setPlatformId(res[2].data[0].id);
        setPlatformInfo(
          diffPlatformInfo(res[1].data, JSON.stringify(res[2].data[0].meta_data ?? {}))
        );
      }
    });
  };

  const diffPlatformInfo = (platform_info, value) => {
    const data = JSON.parse(value)?.platform_info ?? [];
    const newArr = cloneDeep(data);
    platform_info.forEach((item) => {
      if (!data.find((plitem) => plitem.aiGoodSkuid === item.aiGoodSkuid)) {
        newArr.push(item);
      }
    });
    return newArr;
  };

  return (
    <div style={{ width: '100%', backgroundColor: '#F2F3F5' }}>
      <div
        style={{
          width: '100%',
          padding: '24px 4px 24px 24px',
          height: isEdit ? 'calc(100% - 66px)' : '100%'
        }}
      >
        <div style={titleStyle}>话术配置</div>
        <div
          style={{
            marginTop: '20px',
            height: 'calc(100% - 30px)',
            overflow: 'auto'
          }}
        >
          <div
            style={{
              marginRight: '20px',
              background: '#fff',
              borderRadius: '8px',
              padding: '24px'
            }}
          >
            {jsonData ? (
              <>
                {JSON.parse(value)?.pressImgs && (
                  <div
                    style={{
                      marginBottom: '20px'
                    }}
                  >
                    <div
                      style={{
                        minWidth: 10,
                        maxWidth: 500,
                        marginBottom: '8px',
                        color: '#595B60'
                      }}
                    >
                      催付图片:
                    </div>
                    <UploadImg
                      value={pressImgs}
                      onChange={(value) => {
                        setPressImgs(value);
                        setIsEdit(true);
                      }}
                    />
                  </div>
                )}

                <EditForm
                  json={jsonData}
                  ref={editFormRef}
                  onChange={() => {
                    setIsEdit(true);
                  }}
                  onFormSubmit={(val) => {
                    const data = JSON.parse(value);
                    if (val?.keyword_replacement) {
                      val.keyword_replacement = val.keyword_replacement.reduce(function (obj, key) {
                        obj[key.forbiddenWord] = key.substituteWord;
                        return obj;
                      }, {});
                    }
                    if (data?.executablePath) {
                      onChange(
                        JSON.stringify({
                          ...data,
                          ...val,
                          pressImgs: pressImgs
                        })
                      );
                    } else {
                      onChange(JSON.stringify(val));
                    }
                  }}
                />

                {/* <MetadataEditForm
                ref={editFormRef}
                onChange={() => {
                  setIsEdit(true);
                }}
                onFormSubmit={(val) => {
                  const data = JSON.parse(value);
                  if (val?.keyword_replacement) {
                    val.keyword_replacement = val.keyword_replacement.reduce(
                      function (obj, key) {
                        obj[key.forbiddenWord] = key.substituteWord;
                        return obj;
                      },
                      {},
                    );
                  }
                  if (data?.executablePath) {
                    onChange(
                      JSON.stringify({
                        ...data,
                        ...val,
                        pressImgs: pressImgs,
                      }),
                    );
                  } else {
                    onChange(JSON.stringify(val));
                  }
                }}
                json={jsonData}
              /> */}
                {platform_info.length !== 0 && (
                  <PlatformInfoEdit
                    attribute={attribute}
                    platform_info={platform_info}
                    onChange={(val) => {
                      setIsEdit(true);
                      setPlatformInfo(val);
                    }}
                  />
                )}
              </>
            ) : (
              <Spin>
                <div
                  style={{
                    width: '100%',
                    height: '500px'
                  }}
                />
              </Spin>
            )}
          </div>
        </div>
      </div>

      {isEdit && (
        <div
          style={{
            width: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            padding: '14px 0px',
            borderTop: ' 1px solid #ebebeb'
          }}
        >
          <Button style={{ marginRight: '36px' }} onClick={handleCancel}>
            取消
          </Button>
          <Button type="primary" onClick={handleOk}>
            确定
          </Button>
        </div>
      )}
    </div>
  );
};

export default Transferlabor;
