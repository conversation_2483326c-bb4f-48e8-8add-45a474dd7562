import got from '../../../utils/got';
import os from 'node:os';

const endpoint = 'https://msg.in.dongchacat.cn/action';
// const endpoint = 'http://127.0.0.1:3000/action';

export async function report(
  data: { id: string; action: 'add' | 'run' | 'completed'; params?: any },
  ctx
) {
  const { id, action, params } = data;
  ctx.log.info('action上报', id, action, params);

  if (action === 'add') {
    try {
      await got.post(endpoint, {
        json: {
          ...data,
          account: ctx.env.config.options.username,
          machine: os.userInfo().username + '@' + os.hostname(),
          add_time: new Date()
        },
        headers: {
          authorization: `iamasimpleauth`
        }
      });
      // ctx.log.info('action上报成功', id, action, params.no);
    } catch (e: any) {
      ctx.log.error('action上报失败', e);
    }
  } else {
    try {
      await got.put(endpoint + '/' + id, {
        json: {
          ...data,
          ...(action === 'run' ? { run_time: new Date() } : {}),
          ...(action === 'completed' ? { completed_time: new Date() } : {})
        },
        headers: {
          authorization: `iamasimpleauth`
        }
      });
      // ctx.log.info('action上报成功2', id, action);
    } catch (e: any) {
      ctx.log.error('action上报失败2', e);
    }
  }
}
