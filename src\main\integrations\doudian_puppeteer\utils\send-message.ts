export const sendMessage = async (page, agentConfig, uid, msg, type) => {
  await page!.evaluate(
    async ({ u, m, ac, tp }) => {
      setTimeout(async () => {
        const pigeonChatScrollBox = document.querySelector(
          '.ReactVirtualized__Grid__innerScrollContainer'
        );
        if (pigeonChatScrollBox) {
          const key = Object.keys(pigeonChatScrollBox).find(
            (item) => item.indexOf('__reactInternalInstance') !== -1
          );

          const chatList = pigeonChatScrollBox?.children;
          let chatButton;
          for (let index = 0; index < chatList.length; index++) {
            const key = Object.keys(chatList[index]).find(
              (item) => item.indexOf('__reactInternalInstance') !== -1
            );
            if (key) {
              if (chatList[index][key]?.return?.memoizedProps?.node?.id === '55191870957') {
                chatButton = chatList[index];
              }
            }
          }

          if (chatButton) {
            const clickChatItem = () => {
              return new Promise((resolve) => {
                if (chatButton) {
                  chatButton.click();
                  setTimeout(resolve, 100);
                } else {
                  setTimeout(resolve, 100);
                }
              });
            };

            await clickChatItem();
            const descriptor: any = Object.getOwnPropertyDescriptor(
              window.HTMLTextAreaElement.prototype,
              'value'
            );
            console.log(descriptor);

            if (descriptor && typeof descriptor.set === 'function') {
              const nativeInputValueSetter = descriptor.set;
              const textarea = document.querySelector(
                "textarea[data-qa-id='qa-send-message-textarea']"
              );
              const transfer = (window as any).needTransfer(u, m);
              console.log(tp);

              if (tp === 'txt') {
                nativeInputValueSetter.call(
                  textarea,
                  transfer
                    ? (ac?.transer_chat ?? '为您转接更专业的人工处理')
                    : replaceText(m, ac.keyword_replacement)
                );
                // 触发input事件
                const event = new Event('input', { bubbles: true });
                textarea?.dispatchEvent(event);

                // 点击发送按钮
                const sendButton: HTMLButtonElement | null = document.querySelector(
                  "div[data-qa-id='qa-send-message-button']"
                );
                if (sendButton) {
                  sendButton?.click();
                  console.log('setTextAreaAndSend', u, m);
                  if (transfer) {
                    (window as any).transferUser(u);
                  }
                }
              }
              if (tp === 'img') {
                console.log('sendImage', u, m);
                (window as any).sendImage(m);
              }
            }
          }
        }
        function escapeRegExp(str) {
          return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // 转义正则表达式中的特殊字符
        }
        function replaceText(str, replacements) {
          // 转义 replacements 中的键，并构造正则表达式
          const escapedKeys = Object.keys(replacements).map(escapeRegExp);
          const regex = new RegExp(escapedKeys.join('|'), 'g');
          // 使用替换函数进行替换
          return str.replace(regex, function (matched) {
            if (matched) {
              (window as any).addSenceCount();
            }
            return replacements[matched];
          });
        }
      }, 1000);
    },
    { u: uid, m: msg, ac: agentConfig, tp: type }
  );
};
