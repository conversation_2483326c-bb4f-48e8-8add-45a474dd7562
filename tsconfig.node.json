{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": ["electron.vite.config.*", "src/main/**/*", "src/preload/**/*"],
  "compilerOptions": {
    "composite": true,
    "types": ["electron-vite/node"],
    "noUnusedLocals": false,  // 禁用未使用局部变量的检查
    "noUnusedParameters": false,  // 禁用未使用参数的检查
    "strictPropertyInitialization": false, // 禁用未初始化的属性检查
    "noImplicitReturns": false,
    "isolatedModules": false
  }
}
