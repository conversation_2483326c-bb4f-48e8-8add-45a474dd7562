.button {
  display: inline-block;
  border-radius: 4px 4px 4px 4px;
  font-size: 14px;
  line-height: 40px;
  cursor: pointer;
  text-align: center;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.primary {
  color: #ffffff;
  background: linear-gradient(90deg, #64b7f8 0%, #6e62f0 100%);
}

.default {
  border: 1px solid #90beff;
  color: #086eff;
}
