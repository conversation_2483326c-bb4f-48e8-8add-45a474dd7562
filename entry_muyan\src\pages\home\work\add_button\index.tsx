/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';
import PlatformSelection from '../../PlatformSelection';
import UpperLimitTip from './UpperLimitTip';
import { Button } from 'antd';
import { useIsDev } from '@/contexts/IsDevContext';
import styles from './index.module.css';
import IconRefresh from '@/assets/icons/icon_refresh.svg?react';

type Props = {
  data: any[];
  getData: () => void;
};
const AddButton: React.FC<Props> = ({ getData, data }) => {
  const [open, setOpen] = useState(false);
  const [tipOpen, setTipOpen] = useState(false);
  const isDev = useIsDev();
  return (
    <>
      <div>
        <Button
          className={styles.refresh_button}
          icon={<IconRefresh />}
          onClick={() => {
            getData();
          }}
        ></Button>
        {isDev && (
          <Button
            type="primary"
            onClick={() => {
              setOpen(true);
            }}
            style={{
              marginLeft: '8px'
            }}
          >
            添加账号
          </Button>
        )}
      </div>
      <PlatformSelection data={data} open={open} getData={getData} setOpen={setOpen} />
      <UpperLimitTip open={tipOpen} setOpen={setTipOpen} />
    </>
  );
};

export default AddButton;
