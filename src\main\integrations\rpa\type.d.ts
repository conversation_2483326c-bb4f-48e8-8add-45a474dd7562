export type NewData = {
  id: string;
  user: string;
  to: string;
  time: string;
  msg: string;
  type: string;
  metadata: {
    shop: string;
    goods?: {
      id: string;
      name: string;
      skuName: string;
    };
    image?: {
      url: string;
      txt: string;
    };
    quoteMsg?: {
      sender: string;
      msg: string;
      type?: string;
    };
    audio?: {
      url: string;
    };
    video?: {
      url: string;
    };
    isCS: boolean;
    cs: string;
    firstBatchNeedReply?: boolean;
  };
};

export type UserGoods = {
  id?: string;
  url?: string;
  name?: string;
  skuName?: string;
  skuId?: string;
};

export interface Config {
  id: string;
  updated_at: string;
  version: string;
  assistant_id: string;
  user_id: string;
  meta_data: typeof global.pdd_config;
}

export interface ReportMessage {
  created_at: string;
  user_id: string;
  thread_id: string;
  assistant_id: string;
  role: 'user' | 'assistant';
  content: string;
  content_type: string;
  meta_data: object;
}

export type Order = {
  orderId: string;
  orderStatus: string;
  orderTime: string;
  goods: UserGoods[];
};
