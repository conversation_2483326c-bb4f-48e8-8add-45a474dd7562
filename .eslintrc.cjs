module.exports = {
  plugins: ['@typescript-eslint', 'prettier'],
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:react/jsx-runtime',
    '@electron-toolkit/eslint-config-ts/recommended',
    '@electron-toolkit/eslint-config-prettier'
  ],
  rules: {
    '@typescript-eslint/no-explicit-any': 'off',
    'no-inner-declarations': 'off',
    '@typescript-eslint/no-unused-vars': 'off',
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-duplicate-enum-values': 'off',
    '@typescript-eslint/no-require-imports': 'off',
    'no-inline-styles': 'off',
    '@typescript-eslint/no-unused-expressions': 'off',
    '@typescript-eslint/no-empty-object-type': 'off',
    '@typescript-eslint/no-empty-interface': 'off',
    'no-case-declarations': 'off'
  },
  settings: {
    react: {
      version: 'detect' // 自动检测 React 版本
    }
  },
  overrides: [
    {
      files: ['entry_muyan/**/*.{js,jsx,ts,tsx}'],
      rules: {
        // 可以为子目录单独设置规则
        'no-inline-styles': 'off'
      }
    }
  ]
};
