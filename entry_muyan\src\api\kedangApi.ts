/* eslint-disable @typescript-eslint/no-explicit-any */
import service from './utils/kdService';
import { AxiosResponse } from 'axios';

// 发送验证码（登陆）
export const sendPhoneVoucherByLogin = (phone: string): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/sendPhoneVoucherByLogin', {
    phone
  });
};

// 登录自动注册
export const kedanglogin = (data: {
  type: '1' | '2'; // 登陆方式 1验证码 2密码
  voucher: string;
  userName: string;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/login', data);
};

// 发送验证码（适用于登陆后的个人设置类接口）
export const sendVoucherMsg = (phone: string): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/sendVoucherMsg', {
    phone
  });
};

// 发送验证码（适用于登陆后的个人设置类接口）
export const updateKedangPassWord = (
  password: string,
  voucher: string
): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/updatePassWord', {
    password,
    voucher
  });
};

// 获取套餐列表
export const getaAiPackage = (data: {
  type: '1' | '2'; // type	套餐类型 1套餐 2算力
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aiPackage/getList', data);
};

export const aiCommonPay = (data: {
  productId: number; //套餐ID
  type: number; // 1支付宝 2微信
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/AiCommonPay/order', data);
};

// 获取用户套餐列表
export const getAikfPackageAsset = (): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikfAsset/getAikfPackageAsset');
};

// export const sendVoucherMsgFe = (phone: string): Promise<Response> => {
//   return fetch(
//     "http://*************:2000//kedang/saas-api/aikf/user/sendVoucherMsg",
//     {
//       method: "POST",
//       headers: {
//         "Content-Type": "application/json",
//         tokenUf: localStorage.getItem("kedang_toekn") ?? "",
//       },
//       credentials: "include",
//       body: JSON.stringify({ phone }),
//     },
//   );
// };

// 获取账号列表
export const getUserPage = (data: {
  page;
  limit;
  name;
  roleId;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/userPage', data);
};

// 获取可选角色列表
export const getUserRoleList = (data: { name?: string }): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/roleList', data);
};

// 创建子账号
export const userSonAdd = (data: {
  roleId;
  password;
  username;
  name;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/userAdd', data);
};

// 修改子账号密码
export const updateUserSonPassword = (data: {
  userId;
  password;
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/updateUserPassword', data);
};

// 删除子账号
export const deleteUser = (data: { userId }): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/delUser', data);
};

// 修改子账号角色
export const updateUserSonRole = (data: { userId; roleId }): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikf/user/updateUserRole', data);
};

// 订单列表
export const orderPage = (data: {
  page;
  limit;
  invoiceFlag; // 是否被开过票 1开过 0没开过 不传：全部订单
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikfAsset/orderPage', data);
};

// 开票 （需要先通过订单列表接口选订单）
export const invoiceCreate = (data: {
  orderNumbers; // 需要开票的订单号，多个订单之间英文逗号分隔
  type; // 是	发票类型 1电子
  headerType; // 发票抬头类型 1个人 2企业
  headerName; // 抬头 个人名称、公司名称
  dutyParagraph; // 税号
  contentType; // 发票内容类型（1:商品明细、2:商品大类）
}): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikfAsset/invoiceCreate', data);
};

// 发票列表
export const invoicePage = (data: { page; limit }): Promise<AxiosResponse<any, any>> => {
  return service.post('/saas-api/aikfAsset/invoicePage', data);
};
