.send_input {
  width: calc(100% - 48px);
  position: absolute;
  bottom: 24px;
  left: 24px;

  .ant-input {
    height: 50px !important;
    border-top-right-radius: 0 !important;
    border-bottom-right-radius: 0 !important;

  }

  .ant-input-outlined {
    border-right: none !important;
  }

  .ant-input-group-addon {
    border-left: none !important;
    background-color: #fff;
  }

  .ant-input:focus+.ant-input-group-addon {
    border-color: #2E74FF;
  }

  .ant-input-outlined:focus {
    box-shadow: none;
  }

  .ant-input-wrapper:hover .ant-input-group-addon {
    border-color: #2E74FF;

  }

  .ant-input-wrapper:hover .ant-input {
    border-color: #2E74FF;
  }

  .sendIcon {
    color: #ABABAB;
    cursor: pointer;

    &:hover {
      color: #2E74FF;
    }
  }

  .ant-input-disabled {
    color: #ABABAB !important;
    border-color: #dadada !important;
  }

  .ant-input-disabled+.ant-input-group-addon {
    color: #ABABAB !important;
    border-color: #dadada !important;

    .sendIcon {
      color: #ABABAB;
      cursor: not-allowed;

      &:hover {
        color: #ABABAB;
      }
    }
  }
}