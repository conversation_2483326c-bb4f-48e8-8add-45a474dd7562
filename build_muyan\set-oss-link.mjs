import OSS from 'ali-oss';

const client = new OSS({
  // yourregion填写Bucket所在地域。以华东1（杭州）为例，Region填写为oss-cn-hangzhou。
  region: 'oss-cn-beijing',
  // 从环境变量中获取访问凭证。运行本代码示例之前，请确保已设置环境变量OSS_ACCESS_KEY_ID和OSS_ACCESS_KEY_SECRET。
  accessKeyId: 'LTAI5tLe2Ae9gxVJVzjpLDEd',
  accessKeySecret: '******************************',
  // yourbucketname填写存储空间名称。
  bucket: 'muyu-public'
});

console.log('version:', process.env.npm_package_version);

if (!process.env.npm_package_version) {
  console.log('error: npm_package_version is undefined, exit');
  process.exit(1);
}

const isAlphaOrBeta = /alpha|beta/.test(process.env.npm_package_version);

if (isAlphaOrBeta) {
  console.log('version is alpha or beta, exit');
  process.exit(0);
}

const winLink = 'rpa/muyan/win/muyan-setup.exe';
const macLink = 'rpa/muyan/mac/muyan.dmg';

const macPath = `rpa/muyan/mac/牧言 智能客服-${process.env.npm_package_version}-universal.dmg`;
const winPath = `rpa/muyan/win/muyan-${process.env.npm_package_version}-setup.exe`;

(async () => {
  await client.putSymlink(winLink, winPath);
  console.log('set win link success');
  await client.putSymlink(macLink, macPath);
  console.log('set mac link success');
})();
