import { forwardRef, useState, useEffect, useImperativeHandle } from 'react';
import { PlusOutlined } from '@ant-design/icons';
import { Button, Form, Input, Tooltip } from 'antd';
import dragIcon from '@/assets/icons/icon_drag.svg';
import { ReactSortable } from 'react-sortablejs';
type Props = {
  json: any;
  onChange: () => void;
  onFormSubmit: (form: any) => void;
};
// eslint-disable-next-line react/display-name
export const EditForm = forwardRef<any, Props>(({ json, onChange, onFormSubmit }, ref) => {
  const [form] = Form.useForm();
  const [formData, setFormData] = useState<any>({});
  useImperativeHandle(ref, () => {
    return {
      submit
    };
  });
  const delectIconStyle = {
    width: '24px',
    height: '24px',
    marginLeft: '10px',
    cursor: 'pointer'
  };
  const formItemStyle = {
    marginBottom: '5px',
    marginTop: '24px'
  };

  const submit = () => {
    form.validateFields().then((values: any) => {
      // const values = form.getFieldsValue();
      const jsonData = {
        ...values,
        transfer_blacklist: values.transfer_blacklist.flatMap((obj) => obj.value),
        press_interval: values.press_interval.flatMap((obj) => obj.value),
        press_payment_list: values.press_payment_list.flatMap((obj) => obj.value)
      };
      onFormSubmit(jsonData);
    });
  };
  const sortArrayByAnotherArray = (name, evt) => {
    const list = form.getFieldsValue([name])[name];
    // // console.log(list)
    // const result = list.sort(function (a: any, b: any) {
    //   return newArr.map((s) => s.key).indexOf(a.index) <
    //     newArr.map((s) => s.key).indexOf(b.index)
    //     ? -1
    //     : 1;
    // });
    const result = list.splice(evt.oldIndex || 0, 1);
    list.splice(evt.newIndex || 0, 0, result[0]);
    return result;
  };
  useEffect(() => {
    if (json) {
      const jsonData = {
        ...json,
        transfer_blacklist: json?.transfer_blacklist?.map((item, index) => {
          return { value: item, index };
        }),
        press_interval: json?.press_interval?.map((item, index) => {
          return { value: item, index };
        }),
        press_payment_list: json?.press_payment_list?.map((item, index) => {
          return { value: item, index };
        }),
        keyword_replacement: json?.keyword_replacement?.map((item, index) => {
          return { ...item, index };
        })
      };
      setFormData({
        ...jsonData
      });
      form.setFieldsValue({
        ...jsonData
      });
    }
  }, [json]);
  return (
    <Form
      form={form}
      name="dynamic_form_complex"
      onValuesChange={() => {
        onChange();
      }}
      validateTrigger="onBlur"
      style={{ maxWidth: '100%' }}
      autoComplete="off"
      layout="vertical"
    >
      <Form.Item
        label="当前未回复数小于该值才转接:"
        name="transfer_rate"
        required
        rules={[
          {
            required: true,
            message: '请输入'
          }
        ]}
      >
        <Input placeholder="" style={{ width: '50%' }} />
      </Form.Item>
      <div className="ant-form-item-required" style={formItemStyle}>
        转接黑名单
      </div>
      <Form.List name="transfer_blacklist">
        {(fields: any, { add, remove }, { errors }) => (
          <>
            {formData?.transfer_blacklist && (
              <ReactSortable
                list={fields}
                handle=".filed-drop-icon"
                sort={true}
                className="sortableModal-form"
                animation={150}
                onEnd={(evt: any) => {
                  const list = sortArrayByAnotherArray('transfer_blacklist', evt);
                  setFormData((data) => {
                    return {
                      ...data,
                      press_payment_list: list
                    };
                  });
                }}
                setList={() => {}}
              >
                {fields.map(({ key, name, ...restField }) => (
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: key === fields?.length - 1 ? 0 : '12px'
                    }}
                    key={key}
                  >
                    <Tooltip title="拖拽">
                      <img
                        className="filed-drop-icon icon-area-hover"
                        style={{ marginRight: '8px' }}
                        src={dragIcon}
                      />
                    </Tooltip>
                    <Form.Item
                      rules={[
                        {
                          required: true,
                          message: '请输入'
                        }
                      ]}
                      noStyle
                      {...restField}
                      name={[name, 'value']}
                    >
                      <Input.TextArea placeholder="" style={{ width: 'calc(100% - 24px)' }} />
                    </Form.Item>
                    {fields.length > 0 ? (
                      <Tooltip title="删除">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          version="1.1"
                          viewBox="0 0 16 16"
                          className="icon-area-hover icon-area-delect-hover"
                          style={delectIconStyle}
                          onClick={() => remove(name)}
                        >
                          <g>
                            <g>
                              <path
                                className="icon-area-delect-hover"
                                d="M13.018895151901246,3.8133281469345093L10.834365151901245,3.8133281469345093C10.597365151901245,2.480938146934509,9.429915151901245,1.4695781469345093,8.026115151901244,1.4695781469345093C6.622315151901245,1.4695781469345093,5.454865151901245,2.480938146934509,5.2178551519012455,3.8133281469345093L2.026095151901245,3.8133281469345093L1.936219151901245,3.8213881469345092C1.702970151901245,3.8637181469345094,1.5260951519012451,4.06786814693451,1.5260951519012451,4.313328146934509C1.5260951519012451,4.589468146934509,1.749953151901245,4.813328146934509,2.026095151901245,4.813328146934509L3.0274251519012454,4.813328146934509L3.4103551519012454,13.039278146934508C3.4476151519012452,13.83977814693451,4.107425151901245,14.46957814693451,4.908735151901245,14.46957814693451L11.091005151901244,14.46957814693451C11.892295151901244,14.46957814693451,12.552095151901245,13.83977814693451,12.589395151901245,13.039278146934508L12.972295151901244,4.813328146934509L14.026095151901245,4.813328146934509L14.115995151901245,4.805268146934509C14.349195151901245,4.762938146934509,14.526095151901245,4.558788146934509,14.526095151901245,4.313328146934509C14.526095151901245,4.0371881469345094,14.302195151901245,3.8133281469345093,14.026095151901245,3.8133281469345093L13.018895151901246,3.8133281469345093ZM10.878305151901245,4.813328146934509L11.971195151901245,4.813328146934509L11.590495151901244,12.99217814693451C11.577995151901245,13.25897814693451,11.358095151901246,13.468978146934509,11.090995151901245,13.468978146934509L4.908725151901245,13.468978146934509L4.821655151901245,13.461378146934509C4.595315151901245,13.42157814693451,4.420305151901245,13.22937814693451,4.409265151901245,12.99217814693451L4.0273051519012455,4.813328146934509L10.878305151901245,4.813328146934509ZM9.792465151901245,3.756968146934509C9.569425151901244,3.0546881469345095,8.935385151901245,2.533958146934509,8.170905151901245,2.475128146934509L8.026105151901245,2.4695781469345093C7.197695151901245,2.4695781469345093,6.496725151901245,3.0107981469345093,6.259745151901245,3.756968146934509L6.243505151901245,3.8129981469345093L9.807505151901244,3.8129981469345093L9.792465151901245,3.756968146934509ZM6.527685151901245,6.9226981469345095C6.773145151901245,6.9226981469345095,6.977295151901245,7.099578146934509,7.019625151901245,7.332828146934509L7.027685151901245,7.4226981469345095L7.027685151901245,10.90640814693451C7.027685151901245,11.18255814693451,6.803825151901245,11.40640814693451,6.527685151901245,11.40640814693451C6.282225151901245,11.40640814693451,6.0780751519012455,11.229538146934509,6.035735151901245,10.996288146934509L6.027685151901245,10.90640814693451L6.027685151901245,7.4226981469345095C6.027685151901245,7.14655814693451,6.251535151901245,6.9226981469345095,6.527685151901245,6.9226981469345095ZM9.524505151901245,6.9226981469345095C9.769965151901244,6.9226981469345095,9.974115151901245,7.099578146934509,10.016455151901246,7.332828146934509L10.024505151901245,7.4226981469345095L10.024505151901245,10.90640814693451C10.024505151901245,11.18255814693451,9.800655151901244,11.40640814693451,9.524505151901245,11.40640814693451C9.279045151901245,11.40640814693451,9.074895151901245,11.229538146934509,9.032565151901245,10.996288146934509L9.024505151901245,10.90640814693451L9.024505151901245,7.4226981469345095C9.024505151901245,7.14655814693451,9.248365151901245,6.9226981469345095,9.524505151901245,6.9226981469345095Z"
                                fillRule="evenodd"
                                fill="currentColor"
                                fillOpacity="1"
                              />
                            </g>
                          </g>
                        </svg>
                      </Tooltip>
                    ) : null}
                  </div>
                ))}
              </ReactSortable>
            )}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => {
                  add({
                    value: '',
                    index: fields.length
                  });
                  // addItem("transfer_blacklist");
                }}
                style={{ width: '100%', marginTop: '12px' }}
                icon={<PlusOutlined />}
              >
                添加条目
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
      <Form.Item
        label="售前流程的欢迎语"
        name="greeting"
        style={{ marginTop: '24px' }}
        rules={[
          {
            required: true,
            message: '请输入'
          }
        ]}
      >
        <Input.TextArea placeholder="" />
      </Form.Item>
      <Form.Item
        label="转接客服时的提示文案"
        name="transer_chat"
        style={{ marginTop: '24px' }}
        rules={[
          {
            required: true,
            message: '请输入'
          }
        ]}
      >
        <Input.TextArea placeholder="" />
      </Form.Item>
      <div className="ant-form-item-required" style={formItemStyle}>
        催促等待时间
      </div>
      <Form.List name="press_interval">
        {(fields: any, { add, remove }, { errors }) => (
          <>
            {formData?.press_interval && (
              <ReactSortable
                list={fields}
                handle=".filed-drop-icon"
                sort={true}
                className="sortableModal-form"
                onEnd={(evt: any) => {
                  const list = sortArrayByAnotherArray('press_interval', evt);
                  setFormData((data) => {
                    return {
                      ...data,
                      press_payment_list: list
                    };
                  });
                }}
                setList={() => {}}
              >
                {fields.map(({ key, name, ...restField }) => (
                  <div
                    style={{
                      width: '50%',
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: key === fields?.length - 1 ? 0 : '12px'
                    }}
                    key={key}
                  >
                    <Tooltip title="拖拽">
                      <img
                        className="filed-drop-icon icon-area-hover"
                        style={{ marginRight: '8px' }}
                        src={dragIcon}
                      />
                    </Tooltip>
                    <Form.Item
                      rules={[
                        {
                          required: true,
                          message: '请输入'
                        }
                      ]}
                      noStyle
                      {...restField}
                      name={[name, 'value']}
                    >
                      <Input placeholder="" style={{ width: 'calc(100% - 24px)' }} />
                    </Form.Item>
                    {fields.length > 0 ? (
                      <Tooltip title="删除">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          version="1.1"
                          viewBox="0 0 16 16"
                          className="icon-area-hover icon-area-delect-hover"
                          style={delectIconStyle}
                          onClick={() => remove(name)}
                        >
                          <g>
                            <g>
                              <path
                                className="icon-area-delect-hover"
                                d="M13.018895151901246,3.8133281469345093L10.834365151901245,3.8133281469345093C10.597365151901245,2.480938146934509,9.429915151901245,1.4695781469345093,8.026115151901244,1.4695781469345093C6.622315151901245,1.4695781469345093,5.454865151901245,2.480938146934509,5.2178551519012455,3.8133281469345093L2.026095151901245,3.8133281469345093L1.936219151901245,3.8213881469345092C1.702970151901245,3.8637181469345094,1.5260951519012451,4.06786814693451,1.5260951519012451,4.313328146934509C1.5260951519012451,4.589468146934509,1.749953151901245,4.813328146934509,2.026095151901245,4.813328146934509L3.0274251519012454,4.813328146934509L3.4103551519012454,13.039278146934508C3.4476151519012452,13.83977814693451,4.107425151901245,14.46957814693451,4.908735151901245,14.46957814693451L11.091005151901244,14.46957814693451C11.892295151901244,14.46957814693451,12.552095151901245,13.83977814693451,12.589395151901245,13.039278146934508L12.972295151901244,4.813328146934509L14.026095151901245,4.813328146934509L14.115995151901245,4.805268146934509C14.349195151901245,4.762938146934509,14.526095151901245,4.558788146934509,14.526095151901245,4.313328146934509C14.526095151901245,4.0371881469345094,14.302195151901245,3.8133281469345093,14.026095151901245,3.8133281469345093L13.018895151901246,3.8133281469345093ZM10.878305151901245,4.813328146934509L11.971195151901245,4.813328146934509L11.590495151901244,12.99217814693451C11.577995151901245,13.25897814693451,11.358095151901246,13.468978146934509,11.090995151901245,13.468978146934509L4.908725151901245,13.468978146934509L4.821655151901245,13.461378146934509C4.595315151901245,13.42157814693451,4.420305151901245,13.22937814693451,4.409265151901245,12.99217814693451L4.0273051519012455,4.813328146934509L10.878305151901245,4.813328146934509ZM9.792465151901245,3.756968146934509C9.569425151901244,3.0546881469345095,8.935385151901245,2.533958146934509,8.170905151901245,2.475128146934509L8.026105151901245,2.4695781469345093C7.197695151901245,2.4695781469345093,6.496725151901245,3.0107981469345093,6.259745151901245,3.756968146934509L6.243505151901245,3.8129981469345093L9.807505151901244,3.8129981469345093L9.792465151901245,3.756968146934509ZM6.527685151901245,6.9226981469345095C6.773145151901245,6.9226981469345095,6.977295151901245,7.099578146934509,7.019625151901245,7.332828146934509L7.027685151901245,7.4226981469345095L7.027685151901245,10.90640814693451C7.027685151901245,11.18255814693451,6.803825151901245,11.40640814693451,6.527685151901245,11.40640814693451C6.282225151901245,11.40640814693451,6.0780751519012455,11.229538146934509,6.035735151901245,10.996288146934509L6.027685151901245,10.90640814693451L6.027685151901245,7.4226981469345095C6.027685151901245,7.14655814693451,6.251535151901245,6.9226981469345095,6.527685151901245,6.9226981469345095ZM9.524505151901245,6.9226981469345095C9.769965151901244,6.9226981469345095,9.974115151901245,7.099578146934509,10.016455151901246,7.332828146934509L10.024505151901245,7.4226981469345095L10.024505151901245,10.90640814693451C10.024505151901245,11.18255814693451,9.800655151901244,11.40640814693451,9.524505151901245,11.40640814693451C9.279045151901245,11.40640814693451,9.074895151901245,11.229538146934509,9.032565151901245,10.996288146934509L9.024505151901245,10.90640814693451L9.024505151901245,7.4226981469345095C9.024505151901245,7.14655814693451,9.248365151901245,6.9226981469345095,9.524505151901245,6.9226981469345095Z"
                                fillRule="evenodd"
                                fill="currentColor"
                                fillOpacity="1"
                              />
                            </g>
                          </g>
                        </svg>
                      </Tooltip>
                    ) : null}
                  </div>
                ))}
              </ReactSortable>
            )}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => {
                  add({
                    value: '',
                    index: fields.length
                  });
                }}
                style={{ width: '50%', marginTop: '12px' }}
                icon={<PlusOutlined />}
              >
                添加条目
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
      <div className="ant-form-item-required" style={formItemStyle}>
        催促话术列表
      </div>
      <Form.List name="press_payment_list">
        {(fields: any, { add, remove }, { errors }) => (
          <>
            {formData?.press_payment_list && (
              <ReactSortable
                list={fields}
                handle=".filed-drop-icon"
                sort={true}
                delay={100}
                // animation={150}
                className="sortableModal-form"
                onEnd={(evt: any) => {
                  const list = sortArrayByAnotherArray('press_payment_list', evt);
                  setFormData((data) => {
                    return {
                      ...data,
                      press_payment_list: list
                    };
                  });
                }}
                setList={() => {}}
              >
                {fields.map(({ key, name, ...restField }) => (
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: key === fields?.length - 1 ? 0 : '12px'
                    }}
                    key={key}
                  >
                    <Tooltip title="拖拽">
                      <img
                        className="filed-drop-icon icon-area-hover"
                        style={{ marginRight: '8px' }}
                        src={dragIcon}
                      />
                    </Tooltip>
                    <Form.Item
                      rules={[
                        {
                          required: true,
                          message: '请输入'
                        }
                      ]}
                      noStyle
                      {...restField}
                      name={[name, 'value']}
                    >
                      <Input.TextArea placeholder="" style={{ width: 'calc(100% - 24px)' }} />
                    </Form.Item>
                    {fields.length > 0 ? (
                      <Tooltip title="删除">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          version="1.1"
                          viewBox="0 0 16 16"
                          className="icon-area-hover icon-area-delect-hover"
                          style={delectIconStyle}
                          onClick={() => remove(name)}
                        >
                          <g>
                            <g>
                              <path
                                className="icon-area-delect-hover"
                                d="M13.018895151901246,3.8133281469345093L10.834365151901245,3.8133281469345093C10.597365151901245,2.480938146934509,9.429915151901245,1.4695781469345093,8.026115151901244,1.4695781469345093C6.622315151901245,1.4695781469345093,5.454865151901245,2.480938146934509,5.2178551519012455,3.8133281469345093L2.026095151901245,3.8133281469345093L1.936219151901245,3.8213881469345092C1.702970151901245,3.8637181469345094,1.5260951519012451,4.06786814693451,1.5260951519012451,4.313328146934509C1.5260951519012451,4.589468146934509,1.749953151901245,4.813328146934509,2.026095151901245,4.813328146934509L3.0274251519012454,4.813328146934509L3.4103551519012454,13.039278146934508C3.4476151519012452,13.83977814693451,4.107425151901245,14.46957814693451,4.908735151901245,14.46957814693451L11.091005151901244,14.46957814693451C11.892295151901244,14.46957814693451,12.552095151901245,13.83977814693451,12.589395151901245,13.039278146934508L12.972295151901244,4.813328146934509L14.026095151901245,4.813328146934509L14.115995151901245,4.805268146934509C14.349195151901245,4.762938146934509,14.526095151901245,4.558788146934509,14.526095151901245,4.313328146934509C14.526095151901245,4.0371881469345094,14.302195151901245,3.8133281469345093,14.026095151901245,3.8133281469345093L13.018895151901246,3.8133281469345093ZM10.878305151901245,4.813328146934509L11.971195151901245,4.813328146934509L11.590495151901244,12.99217814693451C11.577995151901245,13.25897814693451,11.358095151901246,13.468978146934509,11.090995151901245,13.468978146934509L4.908725151901245,13.468978146934509L4.821655151901245,13.461378146934509C4.595315151901245,13.42157814693451,4.420305151901245,13.22937814693451,4.409265151901245,12.99217814693451L4.0273051519012455,4.813328146934509L10.878305151901245,4.813328146934509ZM9.792465151901245,3.756968146934509C9.569425151901244,3.0546881469345095,8.935385151901245,2.533958146934509,8.170905151901245,2.475128146934509L8.026105151901245,2.4695781469345093C7.197695151901245,2.4695781469345093,6.496725151901245,3.0107981469345093,6.259745151901245,3.756968146934509L6.243505151901245,3.8129981469345093L9.807505151901244,3.8129981469345093L9.792465151901245,3.756968146934509ZM6.527685151901245,6.9226981469345095C6.773145151901245,6.9226981469345095,6.977295151901245,7.099578146934509,7.019625151901245,7.332828146934509L7.027685151901245,7.4226981469345095L7.027685151901245,10.90640814693451C7.027685151901245,11.18255814693451,6.803825151901245,11.40640814693451,6.527685151901245,11.40640814693451C6.282225151901245,11.40640814693451,6.0780751519012455,11.229538146934509,6.035735151901245,10.996288146934509L6.027685151901245,10.90640814693451L6.027685151901245,7.4226981469345095C6.027685151901245,7.14655814693451,6.251535151901245,6.9226981469345095,6.527685151901245,6.9226981469345095ZM9.524505151901245,6.9226981469345095C9.769965151901244,6.9226981469345095,9.974115151901245,7.099578146934509,10.016455151901246,7.332828146934509L10.024505151901245,7.4226981469345095L10.024505151901245,10.90640814693451C10.024505151901245,11.18255814693451,9.800655151901244,11.40640814693451,9.524505151901245,11.40640814693451C9.279045151901245,11.40640814693451,9.074895151901245,11.229538146934509,9.032565151901245,10.996288146934509L9.024505151901245,10.90640814693451L9.024505151901245,7.4226981469345095C9.024505151901245,7.14655814693451,9.248365151901245,6.9226981469345095,9.524505151901245,6.9226981469345095Z"
                                fillRule="evenodd"
                                fill="currentColor"
                                fillOpacity="1"
                              />
                            </g>
                          </g>
                        </svg>
                      </Tooltip>
                    ) : null}
                  </div>
                ))}
              </ReactSortable>
            )}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => {
                  add({
                    value: '',
                    index: fields.length
                  });
                }}
                style={{ width: '100%', marginTop: '12px' }}
                icon={<PlusOutlined />}
              >
                添加条目
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
      <div className="ant-form-item-required" style={formItemStyle}>
        敏感词替换
      </div>
      <Form.List name="keyword_replacement">
        {(fields: any, { add, remove }, { errors }) => (
          <>
            {formData?.keyword_replacement && (
              <ReactSortable
                list={fields}
                className="sortableModal-form"
                handle=".filed-drop-icon"
                sort={true}
                onEnd={(evt: any) => {
                  const list = sortArrayByAnotherArray('keyword_replacement', evt);
                  setFormData((data) => {
                    return {
                      ...data,
                      press_payment_list: list
                    };
                  });
                }}
                setList={() => {}}
              >
                {fields.map(({ key, name, ...restField }) => (
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      alignItems: 'center',
                      marginBottom: key === fields?.length - 1 ? 0 : '12px'
                    }}
                    key={key}
                  >
                    <Tooltip title="拖拽">
                      <img
                        className="filed-drop-icon icon-area-hover"
                        style={{ marginRight: '8px' }}
                        src={dragIcon}
                      />
                    </Tooltip>
                    <Form.Item
                      style={{
                        width: '50%',
                        marginRight: '20px',
                        marginBottom: '0'
                      }}
                      layout="horizontal"
                      {...restField}
                      name={[name, 'forbiddenWord']}
                      rules={[
                        {
                          required: true,
                          message: '请输入'
                        }
                      ]}
                      label="违禁词"
                    >
                      <Input placeholder="" />
                    </Form.Item>
                    <Form.Item
                      style={{ width: '50%', marginBottom: '0' }}
                      layout="horizontal"
                      {...restField}
                      name={[name, 'substituteWord']}
                      label="替换词"
                    >
                      <Input placeholder="" />
                    </Form.Item>
                    {fields.length > 0 ? (
                      <Tooltip title="删除">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          fill="currentColor"
                          version="1.1"
                          viewBox="0 0 16 16"
                          className="icon-area-hover icon-area-delect-hover"
                          style={delectIconStyle}
                          onClick={() => remove(name)}
                        >
                          <g>
                            <g>
                              <path
                                className="icon-area-delect-hover"
                                d="M13.018895151901246,3.8133281469345093L10.834365151901245,3.8133281469345093C10.597365151901245,2.480938146934509,9.429915151901245,1.4695781469345093,8.026115151901244,1.4695781469345093C6.622315151901245,1.4695781469345093,5.454865151901245,2.480938146934509,5.2178551519012455,3.8133281469345093L2.026095151901245,3.8133281469345093L1.936219151901245,3.8213881469345092C1.702970151901245,3.8637181469345094,1.5260951519012451,4.06786814693451,1.5260951519012451,4.313328146934509C1.5260951519012451,4.589468146934509,1.749953151901245,4.813328146934509,2.026095151901245,4.813328146934509L3.0274251519012454,4.813328146934509L3.4103551519012454,13.039278146934508C3.4476151519012452,13.83977814693451,4.107425151901245,14.46957814693451,4.908735151901245,14.46957814693451L11.091005151901244,14.46957814693451C11.892295151901244,14.46957814693451,12.552095151901245,13.83977814693451,12.589395151901245,13.039278146934508L12.972295151901244,4.813328146934509L14.026095151901245,4.813328146934509L14.115995151901245,4.805268146934509C14.349195151901245,4.762938146934509,14.526095151901245,4.558788146934509,14.526095151901245,4.313328146934509C14.526095151901245,4.0371881469345094,14.302195151901245,3.8133281469345093,14.026095151901245,3.8133281469345093L13.018895151901246,3.8133281469345093ZM10.878305151901245,4.813328146934509L11.971195151901245,4.813328146934509L11.590495151901244,12.99217814693451C11.577995151901245,13.25897814693451,11.358095151901246,13.468978146934509,11.090995151901245,13.468978146934509L4.908725151901245,13.468978146934509L4.821655151901245,13.461378146934509C4.595315151901245,13.42157814693451,4.420305151901245,13.22937814693451,4.409265151901245,12.99217814693451L4.0273051519012455,4.813328146934509L10.878305151901245,4.813328146934509ZM9.792465151901245,3.756968146934509C9.569425151901244,3.0546881469345095,8.935385151901245,2.533958146934509,8.170905151901245,2.475128146934509L8.026105151901245,2.4695781469345093C7.197695151901245,2.4695781469345093,6.496725151901245,3.0107981469345093,6.259745151901245,3.756968146934509L6.243505151901245,3.8129981469345093L9.807505151901244,3.8129981469345093L9.792465151901245,3.756968146934509ZM6.527685151901245,6.9226981469345095C6.773145151901245,6.9226981469345095,6.977295151901245,7.099578146934509,7.019625151901245,7.332828146934509L7.027685151901245,7.4226981469345095L7.027685151901245,10.90640814693451C7.027685151901245,11.18255814693451,6.803825151901245,11.40640814693451,6.527685151901245,11.40640814693451C6.282225151901245,11.40640814693451,6.0780751519012455,11.229538146934509,6.035735151901245,10.996288146934509L6.027685151901245,10.90640814693451L6.027685151901245,7.4226981469345095C6.027685151901245,7.14655814693451,6.251535151901245,6.9226981469345095,6.527685151901245,6.9226981469345095ZM9.524505151901245,6.9226981469345095C9.769965151901244,6.9226981469345095,9.974115151901245,7.099578146934509,10.016455151901246,7.332828146934509L10.024505151901245,7.4226981469345095L10.024505151901245,10.90640814693451C10.024505151901245,11.18255814693451,9.800655151901244,11.40640814693451,9.524505151901245,11.40640814693451C9.279045151901245,11.40640814693451,9.074895151901245,11.229538146934509,9.032565151901245,10.996288146934509L9.024505151901245,10.90640814693451L9.024505151901245,7.4226981469345095C9.024505151901245,7.14655814693451,9.248365151901245,6.9226981469345095,9.524505151901245,6.9226981469345095Z"
                                fillRule="evenodd"
                                fill="currentColor"
                                fillOpacity="1"
                              />
                            </g>
                          </g>
                        </svg>
                      </Tooltip>
                    ) : null}
                  </div>
                ))}
              </ReactSortable>
            )}
            <Form.Item>
              <Button
                type="dashed"
                onClick={() => {
                  add({
                    forbiddenWord: '',
                    substituteWord: '',
                    index: fields.length
                  });
                }}
                style={{
                  width: '100%',
                  marginBottom: '24px',
                  marginTop: '12px'
                }}
                icon={<PlusOutlined />}
              >
                添加条目
              </Button>
              <Form.ErrorList errors={errors} />
            </Form.Item>
          </>
        )}
      </Form.List>
    </Form>
  );
});
