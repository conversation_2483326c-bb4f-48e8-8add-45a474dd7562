import style from './index.module.css';
import { NavLink, useLocation } from 'react-router-dom';
type Props = {
  src: string;
  children: React.ReactNode;
  to: string;
  acSrc: string;
};
const MenuItem = (props: Props): React.ReactElement => {
  const location = useLocation();
  return (
    <NavLink to={props.to}>
      <div className={`${style.item} ${location.pathname === props.to ? style.active : ''}`}>
        <div>
          <img
            src={location.pathname === props.to ? props.acSrc : props.src}
            className={style.icon}
            alt="icon"
          />
          <div className={style.text}>{props.children}</div>
        </div>
      </div>
    </NavLink>
  );
};

export default MenuItem;
