import Modal from '@/components/Modal';
import { Form, message, Select } from 'antd';
import { updateUserSonRole, getUserRoleList } from '@/api/kedangApi';
import { useState, useEffect } from 'react';
type Props = {
  open: boolean;
  userId: string;
  setOpen: (bool: boolean) => void;
  onSubmit: () => void;
};
const { Option } = Select;
const UpdateAccountRole: React.FC<Props> = ({ open, setOpen, userId, onSubmit }: Props) => {
  const [form] = Form.useForm();
  const [roleId, setRoleId] = useState<{ name: string; id: number }[]>([]);

  useEffect(() => {
    if (open) {
      getUserRoleList({}).then((res) => {
        setRoleId(res.data);
      });
    }
  }, [open]);
  const onFinish = (values): void => {
    updateUserSonRole({
      userId,
      roleId: values.roleId
    }).then(() => {
      resetFields();
      onSubmit();
      message.success('修改成功');
    });
  };

  const resetFields = () => {
    setOpen(false);
    form.resetFields();
  };
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      width={500}
      title="修改密码"
      okText="保存"
      onOk={() => form.submit()}
      onCancel={() => resetFields()}
    >
      <div
        className="filter"
        style={{
          marginTop: '24px'
        }}
      >
        <Form labelCol={{ span: 7 }} wrapperCol={{ span: 17 }} form={form} onFinish={onFinish}>
          <Form.Item
            name="roleId"
            label="角色："
            rules={[{ required: true, message: '请选择角色!' }]}
          >
            <Select placeholder="请选择角色" allowClear style={{ width: '268px', height: '32px' }}>
              {roleId.map((item) => {
                return (
                  <Option key={item.id} value={item.id}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default UpdateAccountRole;
