/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import _ from 'lodash';
import Help from './help';
import './toast.css';

window.onbeforeunload = (event) => {
  // Recommended
  event.preventDefault();

  // Included for legacy support, e.g. Chrome/Edge < 119
  event.returnValue = true;
};

type ToastMessage = {
  sender?: number;
  to?: number;
  topic: string;
  payload: {
    uid: string;
    cs: string;
    shop: string;
    nickname: string;
    time: number;
    message: string;
  };
};

// function usePrevious(value) {
//   const ref = useRef();
//   useEffect(() => {
//     ref.current = value;
//   });
//   return ref.current;
// }

const waitLimit = 3 * 60 * 1000;
const renderTime = (time, now, close) => {
  const estimate = dayjs(time).add(waitLimit);
  const t = dayjs(time);
  const n = dayjs(now);
  if (estimate.isAfter(n)) {
    return <span className="countdown red">{estimate.diff(n, 's')}秒后超时</span>;
  } else {
    return (
      <span className="countdown orange">
        {`已等待${n.diff(t, 'm')}分钟`}
        <span className="close" onClick={close}>
          x
        </span>
      </span>
    );
  }
};

// const handleItemClick = (message) => {
//   return (_e) => {
//     window.electronAPI.callbackToastMessage({
//       to: message.sender,
//       topic: 'response_to_user',
//       payload: message.payload
//     });
//   };
// };

const App: React.FC = () => {
  const [unDealMessage, setUnDealMessage] = useState<any>({});
  const [currentTime, setCurrentTime] = useState(dayjs().valueOf());
  const [clientStatus, setClientStatus] = useState<string>('启动中');
  const [exeStatus, setExeStatus] = useState<string>('未启动');
  const replyWindow = useRef<Window | null>(null);
  const detailsWindow = useRef<Window | null>(null);
  const [runningAction, setRunningAction] = useState<any>({});

  useEffect(() => {
    // window.xBrowser.on('mode', (_, mode) => {
    //   console.log('mode', mode);
    //   if (mode === 'assistant') {
    //     replyWindow.current = window.open(
    //       '../qianniu_reply/',
    //       undefined,
    //       'frameless,width=342,height=122'
    //     );
    //     detailsWindow.current = window.open(
    //       '../qianniu_details/',
    //       undefined,
    //       'frameless,width=320,height=724,left=1000,top=100'
    //     );
    //   }
    // });
    // window.addEventListener('message', (e) => {
    //   const { source, data } = e;
    //
    //   if (source === window) {
    //     return;
    //   }
    //
    //   console.log('received', e);
    //
    //   if (typeof data === 'object') {
    //     if (data.type === 'handover') {
    //       window.xBrowser.send('handover');
    //       // setRunningStatus(data.status);
    //     }
    //   }
    // });
    // const reply = window.open('../qianniu_reply/', undefined, 'frameless,width=320,height=122');
    // window.addEventListener('message', (e) => {
    //   console.log('received main', e);
    //   if (e.data === 'done') {
    //     window.xBrowser.send('done', 'done');
    //   }
    // });
    // window.xBrowser.on('qianniu-wait-reply', (_) => {
    //   replyWindow.current?.postMessage(
    //     {
    //       type: 'status',
    //       status: 'wait-reply'
    //     },
    //     '*'
    //   );
    // });
    // window.xBrowser.on('user-reply-list', (_, data) => {
    //   detailsWindow.current?.postMessage(
    //     {
    //       type: 'reply-list',
    //       data
    //     },
    //     '*'
    //   );
    // });
    // window.xBrowser.on('user-reply-selected', (_, data) => {
    //   detailsWindow.current?.postMessage(
    //     {
    //       type: 'select-message',
    //       data
    //     },
    //     '*'
    //   );
    // });
    // window.xBrowser.on('user-reply-add', (_, data) => {
    //   detailsWindow.current?.postMessage(
    //     {
    //       type: 'add-reply',
    //       data
    //     },
    //     '*'
    //   );
    // });
    // window.xBrowser.on('current-user', (_, data) => {
    //   detailsWindow.current?.postMessage(
    //     {
    //       type: 'current-user',
    //       data
    //     },
    //     '*'
    //   );
    // });
    window.xBrowser.on('qianniu-client-status', (_, status) => {
      // console.log('qianniu-client-status', status)
      setClientStatus((old) => {
        switch (status) {
          case 'connect':
            return '已启动';
          case 'error':
            if (old === '启动中') {
              return old;
            }
            return '错误';
          case 'disconnect':
            return '已停止';
          default:
            return '未知';
        }
      });
      // if (replyWindow.current) {
      //   replyWindow.current.postMessage(
      //     {
      //       type: 'status',
      //       status
      //     },
      //     '*'
      //   );
      // }
    });
    window.xBrowser.on('qianniu-exe-status', (_, status) => {
      // console.log('qianniu-client-status', status)
      setExeStatus((old) => {
        switch (status) {
          case 'running':
            return '已启动';
          case 'not-running':
            return '未启动';
          case 'unknown':
            return '未知';
          default:
            return '未知';
        }
      });
    });
    window.xBrowser.on('running-action', (_, data) => {
      // console.log('running-action', data);
      setRunningAction(data);
    });
  }, []);

  return (
    <div>
      <Help clientStatus={clientStatus} exeStatus={exeStatus} runningAction={runningAction} />
    </div>
  );
};

export default App;
