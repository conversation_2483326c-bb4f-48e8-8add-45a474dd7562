import { observer } from 'mobx-react-lite';
import { store } from '@/store';
import icon_cancel from '@/assets/icons/icon_cancel.svg';
import icon_add from '@/assets/icons/icon_addy.svg';
import { message, Tooltip } from 'antd';
import { ConfirmationDialog } from '@/components/ConfirmationDialog';
import cloneDeep from 'lodash-es/cloneDeep';
type Props = {
  kb_name: string;
  filterData: {
    not: boolean;
    has: boolean;
  };
  messageData: any[];
};
export const BewithdrawnQuery = observer(({ kb_name, filterData, messageData }: Props) => {
  const { message: messageStore, extractSession, knowledge } = store;
  const deleteQuestion = (value) => {
    ConfirmationDialog({ title: '是否从意图中移除此条问题?' }).then(() => {
      const arr = cloneDeep(extractSession.editList);
      const index = arr.map((item) => item.message_id).indexOf(value);
      knowledge.deleteProductQuery(arr[index].id).then(() => {
        knowledge.loadDomain(kb_name, knowledge.versionId, extractSession.search).then((res) => {
          knowledge.setList(res.data);
        });
        arr.splice(index, 1);
        extractSession.setEditList(arr);
      });
    });
  };

  function getQueryItem() {
    const userMessages = messageData.filter((item) => item?.role === 'user');
    return userMessages.map((item, index) => {
      return {
        ...item,
        iur: messageStore.queryIur[index]?.iur ?? '',
        recall: messageStore.queryIur[index]?.recall ?? ''
      };
    });
  }
  return (
    <div
      style={{
        width: '300px'
      }}
    >
      <div
        style={{
          height: '70vh',
          overflowY: 'auto'
        }}
      >
        {getQueryItem()
          .filter((item) => {
            if (filterData.has && filterData.not) {
              return true;
            } else if (filterData.has) {
              return item.recall !== 'NO_DOCS_FOUND';
            } else if (filterData.not) {
              return item.recall === 'NO_DOCS_FOUND';
            } else {
              return false;
            }
          })
          .map((item, index) => {
            return (
              <div
                key={index}
                style={{
                  display: 'flex',
                  height: '36px',
                  justifyContent: 'space-between',
                  alignItems: 'center'
                }}
              >
                <Tooltip title={`${item.content[0].text[0].value}｜${item.iur}`}>
                  <div
                    style={{
                      width: '260px',
                      fontSize: '14px',
                      textAlign: 'left',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                      whiteSpace: 'nowrap'
                    }}
                  >
                    {item.content[0].text[0].value}｜{item.iur}
                  </div>
                </Tooltip>
                <>
                  {extractSession.editList.filter(
                    (editItem) => editItem.value === item.content[0].text[0].value
                  ).length === 0 && (
                    <img
                      src={icon_add}
                      alt="icon"
                      style={{
                        width: '18px',
                        height: '18px',
                        cursor: 'pointer'
                      }}
                      onClick={() => {
                        if (extractSession.currentIntentionId) {
                          knowledge
                            .addProductQuery({
                              product_qa_id: extractSession.currentIntentionId,
                              query: item.iur || item.content[0].text[0].value,
                              product_id: '',
                              cate_id: ''
                            })
                            .then((res) => {
                              knowledge
                                .loadDomain(kb_name, knowledge.versionId, extractSession.search)
                                .then((res) => {
                                  knowledge.setList(res.data);
                                });
                              extractSession.setEditList([
                                ...extractSession.editList,
                                {
                                  value: item.content[0].text[0].value,
                                  query_id: res.data.query_id,
                                  message_id: item.id,
                                  id: res.data.id.replace(/-/g, '')
                                }
                              ]);
                            });
                        } else {
                          message.warning('请选择添加到的意图');
                        }
                      }}
                    />
                  )}
                  {extractSession.editList.filter((editItem) => editItem.message_id === item.id)
                    .length !== 0 && (
                    <img
                      src={icon_cancel}
                      alt="icon"
                      style={{
                        width: '20px',
                        height: '20px',
                        cursor: 'pointer'
                      }}
                      onClick={() => deleteQuestion(item.id)}
                    />
                  )}
                </>
              </div>
            );
          })}
      </div>
    </div>
  );
});
