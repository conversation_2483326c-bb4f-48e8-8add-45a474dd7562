import {
  WebContentsView,
  WebPreferences,
  WebContents,
  dialog,
  ipcMain,
  Event,
  BrowserWindow
} from 'electron';
import { log } from './log';
import { is } from '@electron-toolkit/utils';
import { join } from 'node:path';
import _ from 'lodash';
import { IntegrationConfig } from './integrations';
import { Integration } from './integrations/Integration';
import { create } from './integrations/factory';
import { randomUUID } from 'node:crypto';
import Window from './window';
import { name } from '../../package.json';

// const mode = import.meta.env.MODE;

export interface TabOptions {
  bounds: Electron.Rectangle;
  callbacks: {
    addChildView: (view: WebContentsView) => void;
    updateTab: (id: number) => void;
    newTab: (url: string, options: TabOptions) => Tab;
    tabDestroyed: (id: number, view: WebContentsView) => void;
    beforeUnload: (tab: Tab, event: Event) => void;
  };
  webPreferences?: WebPreferences;
  webContents?: WebContents;
  integration?: IntegrationConfig;
  advances?: {
    individualSession?: boolean;
    disableMinimization?: boolean;
    blockPowerSave?: 'display' | 'system';
    giveMeSomeAir?: boolean;
    dangerousDisableWebSecurity?: boolean;
    disableClose?: boolean;
    overrideTitle?: string;
  };
  opener?: Tab;
  currentWindow: Window;
}

export default class Tab {
  uuid: string;

  #view: WebContentsView;
  #integration: Integration | null;

  #favicons: string[] | undefined;
  #loadingUrl: string | undefined;

  constructor(
    url: string,
    private options: TabOptions
  ) {
    this.uuid = randomUUID();
    log.debug(
      'window',
      this.options.currentWindow.id,
      'tab',
      this.uuid,
      'new tab options',
      options
    );
    this.initView(url);
    this.setUserAgent();
    this.initViewEvent();
  }

  get id() {
    return this.#view.webContents.id;
  }

  get url() {
    return this.#loadingUrl || this.#view.webContents.getURL();
  }

  get title() {
    return this.options.advances?.overrideTitle || this.#view.webContents.getTitle();
  }

  get navigationHistory() {
    return [this.#view.webContents.canGoBack(), this.#view.webContents.canGoForward()];
  }

  get view() {
    return this.#view;
  }

  get isLoading() {
    return this.#view.webContents.isLoading();
  }

  get webContents() {
    return this.#view.webContents;
  }

  get minimizable() {
    return !this.options.advances?.disableMinimization;
  }

  get closeable() {
    return !this.options.advances?.disableClose;
  }

  get faviconUrl() {
    return this.#favicons?.[0];
  }

  get blockPowerSave() {
    return this.options.advances?.blockPowerSave;
  }

  get giveMeSomeAir() {
    return this.options.advances?.giveMeSomeAir;
  }

  get opener() {
    return this.options.opener;
  }

  get integration() {
    return this.#integration;
  }

  get currentWindow() {
    return this.options.currentWindow;
  }

  initView(url: string) {
    const param: {
      webPreferences: WebPreferences;
      webContents?: WebContents;
    } = {
      webPreferences: {
        preload: join(__dirname, '../preload/view.js')
      }
    };

    if (this.options.advances?.individualSession) {
      const partition = this.options.integration?.id
        ? `persist:${this.options.integration.id}`
        : randomUUID();
      param.webPreferences.partition = partition;
    }

    if (this.options.advances?.dangerousDisableWebSecurity) {
      param.webPreferences.webSecurity = false;
    }

    // if (!url) {
    //   // default-view
    //   this.#view = new WebContentsView(param);
    //   if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    //     // this.#view.webContents.loadURL(process.env['ELECTRON_RENDERER_URL'] + '/default-view/');
    //     this.#view.webContents.loadURL('http://localhost:8172/');
    //   } else {
    //     this.#view.webContents.loadFile(join(__dirname, '../renderer/default-view/index.html'));
    //   }
    // } else {
    if (this.options.webPreferences) {
      _.merge(param.webPreferences, this.options.webPreferences);
    }
    if (this.options.webContents) {
      param.webContents = this.options.webContents;
    }
    this.#view = new WebContentsView(param);
    this.#view.setBounds(this.options.bounds);

    if (!param.webContents) {
      if (/^https?:\/\//.test(url)) {
        this.#view.webContents.loadURL(url);
      } else {
        this.#view.webContents.loadFile(url);
      }
    }
    this.options.callbacks.addChildView(this.#view);
    log.debug(
      'window',
      this.options.currentWindow.id,
      'tab',
      this.uuid,
      'tab session path',
      this.#view.webContents.session.getStoragePath()
    );
    if (this.options.integration) {
      this.startIntegration(this.options.integration);
    }
  }

  initViewEvent() {
    this.#view.webContents.on('will-navigate', (_e, ...args) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'will-navigate',
        ...args
      );
      // this.options.callbacks.updateTab(this.id);
    });
    this.#view.webContents.on('did-navigate', (_e, url, httpResponseCode) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'did-navigate',
        url,
        httpResponseCode
      );
      this.#favicons = undefined;
      this.options.callbacks.updateTab(this.id);
    });
    this.#view.webContents.on('did-navigate-in-page', (_e, url) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'did-navigate-in-page',
        url
      );
      this.options.callbacks.updateTab(this.id);
    });
    // @ts-ignore did-start-loading exists
    this.#view.webContents.on('did-start-loading', (_e) => {
      log.debug('window', this.options.currentWindow.id, 'tab', this.uuid, 'did-start-loading');
      this.options.callbacks.updateTab(this.id);
    });
    // @ts-ignore did-stop-loading exists
    this.#view.webContents.on('did-stop-loading', (_e) => {
      log.debug('window', this.options.currentWindow.id, 'tab', this.uuid, 'did-stop-loading');
      this.#loadingUrl = undefined;
      this.options.callbacks.updateTab(this.id);
    });
    this.#view.webContents.on('page-title-updated', (_e, title) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'page-title-updated',
        title
      );
      this.options.callbacks.updateTab(this.id);
    });
    this.#view.webContents.on('page-favicon-updated', (_e, favicons) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'page-favicon-updated',
        favicons
      );
      this.#favicons = favicons;
      this.options.callbacks.updateTab(this.id);
    });

    // @ts-ignore destroyed event
    this.#view.webContents.on('destroyed', (_e, ...args) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'tab destroyed',
        ...args
      );
      this.options.callbacks.tabDestroyed(this.id, this.#view);
      this.stopIntegration();
    });
    this.#view.webContents.on('will-prevent-unload', (event) => {
      log.debug(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'will-prevent-unload',
        event
      );
      this.options.callbacks.beforeUnload(this, event);
    });
    this.#view.webContents.setWindowOpenHandler((details) => {
      return {
        action: 'allow',
        createWindow: (options) => {
          log.debug(
            'window',
            this.options.currentWindow.id,
            'tab',
            this.uuid,
            'window-open-create',
            details
          );
          // @ts-ignore webContents exists
          const { webContents, webPreferences } = options;
          const { integration, advances, ...otherOptions } = this.options;
          const _options = _.assign(
            {},
            otherOptions,
            { bounds: this.#view.getBounds(), opener: this },
            { webContents, webPreferences }
          ); // shallow copy only
          const { url, features, disposition } = details;

          // TODO open page in a popup window
          if (disposition === 'new-window') {
            const win = new Window({
              features,
              openTab: {
                url: url,
                options: {
                  webContents,
                  webPreferences,
                  opener: this
                }
              }
            });
            return win.activeTab.webContents;
          } else {
            const newTab = this.options.callbacks.newTab(details.url, _options);
            return newTab.#view.webContents;
          }
        }
      };
    });
  }

  loadURL(url) {
    log.debug('window', this.options.currentWindow.id, 'tab', this.uuid, 'loadURL', url);
    this.#loadingUrl = url;
    this.#view.webContents.loadURL(url);
  }

  goBack() {
    this.#view.webContents.goBack();
  }

  goForward() {
    this.#view.webContents.goForward();
  }

  reload() {
    this.#view.webContents.reload();
  }

  setBounds(bounds: Electron.Rectangle) {
    this.#view.setBounds(bounds);
  }

  setActive() {
    this.options.callbacks.addChildView(this.#view);
  }

  async startIntegration(config: IntegrationConfig) {
    try {
      log.debug('starting integration');
      this.#integration = create(this, config);
      await this.#integration?.start();
    } catch (e) {
      log.error(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'start integration failed',
        e
      );
    }
  }

  async stopIntegration() {
    try {
      await this.#integration?.stop();
    } catch (e) {
      log.error(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'stop integration failed',
        e
      );
    }
  }

  toggleDevTools() {
    this.#view.webContents.toggleDevTools();
  }

  newTab(url, options: Pick<TabOptions, 'integration' | 'advances'> = {}) {
    if (!url) {
      log.error(
        'window',
        this.options.currentWindow.id,
        'tab',
        this.uuid,
        'newTab: url is required'
      );
      throw new Error('url is required');
    }

    const { integration, advances } = options;
    this.options.callbacks.newTab(url, {
      integration,
      advances,
      opener: this
    } as unknown as TabOptions);
  }

  setUserAgent() {
    const ua = this.#view.webContents.getUserAgent();
    const appNameRegex = new RegExp(name + '/\\S+\\s');
    const electronRegex = /Electron\/\S+\s/;
    this.#view.webContents.setUserAgent(ua.replace(appNameRegex, '').replace(electronRegex, ''));
  }
  /**
   * close 有2种方式，一种是从tab中关闭，另一种是从webContents中关闭，
   * 如果是从webContents中关闭，则destroy方法会被调用
   * 如果是从tab中关闭，则destroy方法不会被调用，但是想要实现beforeunload事件，
   * 需要调用webContents.close()方法，则这时候会触发destroy方法
   * 统一的话，就都触发destroy方法，然后在destroy方法中统一处理后续流程
   * 如果要触发will-prevent-unload就一定要调用content.close()
   */
  close() {
    log.debug('window', this.options.currentWindow.id, 'tab', this.uuid, 'tab close', this.id);
    this.webContents.close({
      waitForBeforeUnload: true
    });
    // try {
    //   this.#integration?.stop();
    // } catch (e) {
    //   log.error('stop integration failed', e);
    // }
  }
}
