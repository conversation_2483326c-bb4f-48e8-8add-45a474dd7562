import OpenAI from 'openai';
import { createPromiseCapability } from '../../utils/promise';
import _ from 'lodash';
import { Logger } from 'log4js';
import { NewData, Order, UserGoods } from './type';
import dayjs from 'dayjs';
import pTimeout from 'p-timeout';
import ms from 'ms';

class MyQA {
  #openai: OpenAI;
  #assistantId: string;
  #threadId?: string;
  #createThreadPromise?: ReturnType<typeof createPromiseCapability>;
  #getThreadPromise: ReturnType<typeof createPromiseCapability<string>>;
  #log: Logger;

  constructor(
    private ctx,
    private user: string
  ) {
    this.#assistantId = ctx.env.assistantId;
    this.#log = ctx.log;
    const baseURL = ctx.env.endpoint;
    const apiKey = ctx.env.secretKey;
    this.#openai = new OpenAI({
      apiKey,
      baseURL,
      maxRetries: 4
    });
    this.#getThreadPromise = createPromiseCapability();
  }

  public getThreadId() {
    return this.#getThreadPromise.promise;
  }

  private async createThreadWrap(shop: string) {
    if (!this.#createThreadPromise) {
      this.#createThreadPromise = createPromiseCapability();
      try {
        const threadId = await this._createThread(shop);
        this.#threadId = threadId;
        this.#createThreadPromise.resolve(threadId);
        this.#getThreadPromise.resolve(threadId);
      } catch (e) {
        this.#createThreadPromise.reject(e);
        this.#getThreadPromise.promise.catch(() => {});
        this.#getThreadPromise.reject(e);
      }
    }
    return this.#createThreadPromise.promise;
  }

  /** 创建会话 */
  _createThread = async (shop: string) => {
    const thread = await this.#openai.beta.threads.create({
      metadata: {
        source: 'agent',
        assistant_id: this.#assistantId,
        uid: this.user,
        mall_id: shop
        // ...metadata,
      }
    });

    this.#log.info('会话创建成功', this.user, thread.id);
    return thread.id;
  };

  async addMessage(msg: NewData, goods: UserGoods, originMessages: any[], orders?: Order[]) {
    const shop = _.get(msg, 'metadata.shop');
    await this.createThreadWrap(shop);
    this.#log.debug('myqa addMessage', this.user, msg, goods, msg.id);
    let content;

    switch (msg.type) {
      case 'txt':
      case 'link':
        content = [
          {
            type: 'text',
            text: [
              {
                value: msg.msg,
                meta_data: null
              }
            ]
          }
        ];
        break;
      case 'image':
        content = [
          {
            type: 'pic',
            text: [
              {
                value: msg.metadata.image?.txt.replace(/\r|\n/g, '') || msg.msg,
                meta_data: {
                  pic: {
                    value: msg.metadata.image?.url
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'card':
        content = [
          {
            type: 'card',
            text: [
              {
                value: msg.msg,
                meta_data: null
              }
            ]
          }
        ];
        break;
      case 'emoji':
        content = [
          {
            type: 'emoji',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  emoji: {
                    value: msg.msg
                  }
                }
              }
            ]
          }
        ];
        break;
      case 'quote':
        content = [
          {
            type: 'quote',
            text: [
              {
                value: msg.msg,
                meta_data: {
                  ...msg.metadata.quoteMsg,
                  quotation: {
                    value: msg.metadata.quoteMsg?.msg
                  }
                }
              }
            ]
          }
        ];
        break;
      default:
        this.#log.error('Unsupported type', this.user, msg);
        throw new Error('Unsupported type ' + msg.type);
    }

    return await this.#openai.beta.threads.messages.create(this.#threadId!, {
      role: 'user',
      content,
      metadata: {
        // ...item.metadata,
        assistant_id: this.#assistantId,
        uid: this.user,
        thread_id: this.#threadId,
        originMessages,
        goods: {
          item_id: goods.id,
          url: goods.url,
          title: goods.name
        },
        userOrders: orders?.map((order) => {
          const { orderStatus, payStatus, shippingStatus } = this.processOrder(order);
          return {
            orderSn: order.orderId,
            orderStatus,
            payStatus,
            shippingStatus,
            orderTime: dayjs(order.orderTime).unix(),
            orderGoodsList: order.goods.map((goods) => {
              return {
                // 取不到goods.id
                goodsName: goods.name,
                skuId: goods.skuId,
                spec: goods.skuName
              };
            })
          };
        })
      }
    });
  }

  processOrder(order: Order) {
    if (this.ctx.platform === 'qianniu') {
      let orderStatus, payStatus, shippingStatus;
      switch (order.orderStatus) {
        case '未付款':
          orderStatus = 1;
          payStatus = 0;
          shippingStatus = 0;
          break;
        case '待发货':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 0;
          break;
        case '待收货':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 1;
          break;
        case '未评价':
        case '已评价':
          orderStatus = 1;
          payStatus = 1;
          shippingStatus = 2;
          break;
      }
      return { orderStatus, payStatus, shippingStatus };
    }
    return {};
  }

  async run() {
    // await this.createThreadWrap();
    const run = await pTimeout(
      this.#openai.beta.threads.runs.createAndPoll(this.#threadId!, {
        assistant_id: this.#assistantId
        // instructions: "Please address the user as Jane Doe. The user has a premium account."
      }),
      {
        milliseconds: ms('10m')
      }
    );

    if (run.status === 'completed') {
      const messages = await this.#openai.beta.threads.messages.list(this.#threadId!, {
        limit: 1
      });
      const msg = _.get(messages, 'data[0]', {});

      if (msg.role !== 'assistant') {
        this.#log.error('last message is not from assistant', this.user, messages);
        throw new Error('last message is not from assistant');
      }
      this.#log.debug('myqa run result', this.user, _.omit(msg, 'metadata'));

      return msg;
    } else {
      throw new Error('run status is ' + run.status);
    }
  }
}

export default MyQA;
