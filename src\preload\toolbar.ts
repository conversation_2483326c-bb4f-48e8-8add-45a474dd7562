import { contextBridge, ipc<PERSON>enderer } from 'electron/renderer';

contextBridge.exposeInMainWorld('xBrowser', {
  on: (channel, listener) => {
    ipcRenderer.on(channel, listener);
    return () => {
      ipcRenderer.off(channel, listener);
    };
  },
  offAll: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
  send: (channel, ...args) => {
    ipcRenderer.send(channel, ...args);
  },
  changeUrl: (url) => ipcRenderer.send('change-url', url),
  changeTab: (data) => ipcRenderer.send('change-tab', data),
  goBack: () => ipcRenderer.send('go-back'),
  goForward: () => ipcRenderer.send('go-forward'),
  reload: () => ipcRenderer.send('reload')
});
