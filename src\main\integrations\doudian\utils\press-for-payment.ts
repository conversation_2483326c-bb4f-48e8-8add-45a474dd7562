export class TimerActivate {
  timeList: number[];
  currentTimeout: null | NodeJS.Timeout;
  isCancelled: boolean;
  payment_list: string[];
  pressImgs: string[];
  uid: string;
  sendMessage: (uid: string, content: string, type: string) => void;
  executionTimes: number;
  haveBeenUsed: {
    txt: number[];
    img: number[];
  };
  log;
  constructor(
    uid: string,
    timeList: number[] = [],
    payment_list: string[] = [],
    pressImgs: string[] = [],
    sendMessage: (uid: string, content: string, type: string) => void,
    log
  ) {
    this.timeList = timeList; // 初始的时间数组
    this.currentTimeout = null; // 当前的计时器ID
    this.isCancelled = false; // 用于跟踪是否已取消
    this.payment_list = payment_list;
    this.pressImgs = pressImgs;
    this.uid = uid;
    this.sendMessage = sendMessage;
    this.executionTimes = 0;
    this.log = log;
    this.haveBeenUsed = {
      txt: [],
      img: []
    };
  }

  start() {
    this.isCancelled = false; // 重置取消标志
    this.triggerNext();
  }

  triggerNext() {
    if (this.timeList.length === 0) {
      this.log.debug(`${this.uid}所有催付已触发完毕`);
      return;
    }
    if (this.isCancelled) {
      this.log.debug(`${this.uid}催付事件已取消`);
      return;
    }
    const time = this.timeList[0] * 1000;
    this.log.debug(`${this.uid}等待 ${time} 毫秒后触发催付`);

    this.currentTimeout = setTimeout(() => {
      if (this.isCancelled) return;

      this.log.debug(`${this.uid}触发了催付: ${this.executionTimes + 1} 次`);

      const txtIndex = this.getIndex(this.payment_list.length, this.haveBeenUsed.txt);

      this.sendMessage(this.uid, this.payment_list[txtIndex], 'txt');
      this.haveBeenUsed.txt.push(txtIndex);

      if (this.executionTimes !== 0) {
        const imgIndex = this.getIndex(this.pressImgs.length, this.haveBeenUsed.img);
        this.sendMessage(this.uid, this.pressImgs[imgIndex], 'img');
        this.haveBeenUsed.img.push(imgIndex);
      }

      this.timeList.shift();
      this.executionTimes += 1;
      this.triggerNext();
    }, time);
  }

  getIndex(length, haveBeenUsed) {
    let index;
    do {
      index = Math.floor(Math.random() * length);
    } while (haveBeenUsed.includes(index));
    return index;
  }

  cancel() {
    this.isCancelled = true; // 设置取消标志
    if (this.currentTimeout) clearTimeout(this.currentTimeout); // 取消当前计时器
    this.log.info(`${this.uid}催付已取消`);
  }
}
