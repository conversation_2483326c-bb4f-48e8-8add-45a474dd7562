.item {
  width: 100%;
  height: 68px;
  background: #ffffff;
  border-radius: 8px;
  border: 1px solid rgba(18, 18, 18, 0.1);
  padding: 10px 16px;
  margin-top: 8px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.item:hover {
  border-color: #2e74ff;
}
.active {
  border-color: #2e74ff;
}

.icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.06);
}

.title {
  font-family: PingFang SC;
  font-size: 16px;
  font-weight: 500;
  line-height: 16px;
  letter-spacing: 0px;
  color: #121212;
  margin-left: 8px;
  white-space: nowrap;
  /* 禁止换行 */
  overflow: hidden;
  /* 超出部分隐藏 */
  text-overflow: ellipsis;
  /* 超出部分显示省略号 */
  width: 200px;
  /* 设置容器宽度 */
}

.tip {
  font-weight: 400;
  font-size: 12px;
  color: #808080;
  text-align: right;
  font-style: normal;
  text-transform: none;
}

.time {
  font-family: PingFang SC;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  letter-spacing: 0px;
  color: #7c7c7c;
  text-transform: none;
  margin-top: 4px;
}

.state {
  position: absolute;
  width: 3px;
  height: 64px;
  background-color: #086eff;
  top: 1px;
  left: 0px;
  border-radius: 8px;
}

.state_rag {
  background-color: #086eff;
}

.state_original {
  background-color: #44d961;
}

.state_reject {
  background-color: #ff4444;
}

.state_reason_fail {
  background-color: #ffa344;
}

.state_cls {
  background-color: #a344ff;
}

.state_null {
  background-color: #808080;
}
