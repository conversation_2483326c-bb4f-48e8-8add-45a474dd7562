- 2.5.8-beta.2 2025-5-29
  - feat:
    - 支持强制发消息，更新server2, 6acaa602400
- 2.5.8-beta.1 2025-5-29
  - feat:
    - 特殊场景转人工支持自定义报警文案
- 2.5.6-beta.3 2025-5-16
  - feat:
    - 判断预售定金，更新server2, 1139e520fe1
- 2.5.6-beta.2 2025-5-16
  - fix:
    - 支持已支付退款订单
- 2.5.6-beta.1 2025-5-15
  - feat:
    - 更新server2, 增加提问重复检测, 2742a4044d6
- 2.5.5-beta.1 2025-5-14
  - feat:
    - 更新server2, 兜底卡片, 7ad0c8c9f98
- 2.5.4-beta.1 2025-5-13
  - fix:
    - 订单数据更换接口
- 2.5.3-beta.1 2025-5-12
  - feat:
    - 客服状态增加客服卡片id
- 2.5.2-beta.2 2025-5-12
  - fix:
    - 客服数据更换接口
- 2.5.1-beta.1 2025-5-9
  - feat:
    - burrow保活
- 2.5.0-beta.2 2025-4-29
  - feat:
    - 增加抓商品通知
- 2.5.0-beta.1 2025-4-28
  - feat:
    - 上报发送失败的消息
    - 同步定时上架商品
    - 更新server2, 3fbfa3675db
  - refactor:
    - 更新burrow接口路径
- 2.4.0-beta.3 2025-4-16
  - fix:
    - 改用新事件上报订单
- 2.4.0-beta.2 2025-4-16
  - fix:
    - 同步订单增加预处理
- 2.4.0-beta.1 2025-4-16
  - feat:
    - 同步订单
    - 延迟发货转人工
- 2.3.1-beta.3 2025-4-3
  - fix:
    - 适配新的进店卡片格式，update server2, 0a6385a1e7e
- 2.3.1-beta.2 2025-4-3
  - feat:
    - support manual transfer
    - update server2, 7d30d4b1d44
- 2.3.1-beta.1 2025-4-3
  - feat:
    - 支持音频、视频格式
    - 增加商品详情
    - 千牛指标默认为0
    - 消息socket增加重试
    - 更新server2, 改为协程，	831912a978d
- 2.3.0-beta.2 2025-3-27
  - fix:
    - server2 sendimage, 53fe20225bf
- 2.3.0-beta.1 2025-3-27
  - feat:
    - 合并辅助回复消息，update server2, 52a6f42d61e
- 2.2.0-beta.11 2025-3-19
  - fix:
    - 上一个客服设置失败, update server2, f22176a3acd
- 2.2.0-beta.10 2025-3-19
  - fix:
    - 修复批量转人工没有转给上一个客服，update server2, f38dcf51519
- 2.2.0-beta.9 2025-3-19
  - fix:
    - 修复没有失败时也发送告警
- 2.2.0-beta.8 2025-3-19
  - fix:
    - upload duplicate message status
- 2.2.0-beta.7 2025-3-19
  - feat:
    - 批量转人工白名单只取第一个, update server2, 972f50ee05e
- 2.2.0-beta.6 2025-3-18
  - feat:
    - add system metrics
- 2.2.0-beta.5 2025-3-18
  - feat:
    - 上报转人工状态，更新server2, 732948d0521
- 2.2.0-beta.4 2025-3-14
  - feat:
    - 批量转人工v2, update server2, 0e66ef29b98
- 2.1.1-beta.4 2025-3-13
  - fix:
    - 调整配置不存在文案
- 2.0.1-beta.10 2025-3-7
  - feat:
    - 支持服务敏感用户、亲，请您核对订单信息、亲，商品已挑好，可直接购买哦、上新抢先看、商品降价提醒、亲，可以发起退换了，update server2, 0edc8366059
- 2.0.1-beta.2 2025-3-7
  - feat:
    - got增加钩子
    - update server2, 解析新消息类型，4f89bd09f65
- 2.0.1-beta.1 2025-3-4
  - feat:
    - 支持多个批量转人工时间配置
- 1.2.3-beta.2 2025-3-4
  - fix:
    - silence mode grab data error
- 1.2.3-beta.1 2025-3-3
  - build:
    - remove bytecodePlugin
- 2.0.0-beta.6 2025-3-3
  - feat:
    - 支持新消息格式，update server2, ac4ca16f885
- 2.0.0-beta.5 2025-3-3
  - fix:
    - 调整取用户名字方法，update server2, 728b8144ef4
- 2.0.0-beta.4 2025-2-28
  - fix:
    - 适配千牛用户名bug，update server2, e245f95d05f
- 2.0.0-beta.3 2025-2-28
  - feat:
    - support more types, update server2, 9d1e7fe36b7
- 2.0.0-beta.2 2025-2-27
  - chore:
    - 增加未知消息报警
- 2.0.0-beta.1 2025-2-26
  - feat:
    - 对接消息接口，更新server2，b34f2f7dc85
- 1.2.2-beta.2 2025-2-14
  - fix:
    - server2 发消息过滤最后一个用户，70b436e2107
- 1.2.2-beta.1 2025-2-13
  - fix:
    - server2 耗时上报异常，发消息时点用户设置y=0，831cf4e3781
- 1.2.1-beta.2 2025-2-6
  - fix:
    - server2 cwd, c9c5031c08b
- 1.2.1-beta.1 2025-2-6
  - perf:
    - 优化发送图片和下载图片的耗时，更新server2, 3db46ce4239
  - fix:
    - 用户转走后不执行转人工
- 1.2.0-beta.8 2025-1-24
  - fix:
    - reply窗口状态
- 1.2.0-beta.7 2025-1-24
  - fix:
    - 修改文案
    - 修复发送消息后查找元素过久的问题, 更新server2, 7162f80f1e6
- 1.2.0-beta.6 2025-1-24
  - style:
    - 修改样式
- 1.2.0-beta.5 2025-1-21
  - fix:
    - 修改报警文案
    - 更新server2, 修复日志变量bug，	e88ca26af44
- 1.2.0-beta.4 2025-1-21
  - fix:
    - 更新server2, sendMsg增加日志，2fa819afe5d
- 1.2.0-beta.3 2025-1-21
  - fix:
    - 停止后不执行获取客服状态的任务
- 1.2.0-beta.2 2025-1-20
  - feat:
    - 更新server2，批量转人工自动在线，76a43d9980a
  - chore:
    - 修改报警文案
    - 使用readline按行读取stdout
- 1.2.0-beta.1 2025-1-20
  - feat:
    - 更新server2，支持辅助接待是抓取消息、检测禁用词、客服状态，cd6a93d2d38
    - 辅助接待样式升级
    - 上报rpa和千牛客户端状态
    - 增加崩溃上报（sqlite3的回调中如果有未捕获的异常会导致程序崩溃）
  - fix:
    - 修复停止后额外创建grab任务的问题，uid为空时报错，
- 1.1.0-beta.3 2025-1-13
  - feat:
    - 增加显示当前执行的动作
    - 更新server2, 去掉parse_message的timeit，revert J_msgBtm，5628023b65f
- 1.1.0-beta.2 2025-1-13
  - feat:
    - 支持复制图片
  - fix:
    - 辅助接待发送图片
    - 发送消息前判断是否转走
    - 更新server2, b7edd3b0db3
- 1.1.0-beta.1 2025-1-8
  - feat:
    - 支持辅助接待
    - 更新server2, 47efe97d908
- 1.0.34-beta.1 2025-1-6
  - fix:
    - 更新server2,修复循环中的变量，7fac7846d6b
- 1.0.33-beta.1 2025-1-2
  - fix:
    - 更新server2, eb6b0442824
- 1.0.32-beta.1 2024-12-31
  - fix:
    - 取用户最后一个nickname
    - 更新server2, 兼容J_msgBtm不存在、没用户消息时不更新uid，增加clientid，7444847f807
- 1.0.31-beta.1 2024-12-30
  - fix:
    - 更新server2, 增加上报动作时间、支持会员专属消息，ab761e838d5
- 1.0.30-beta.5 2024-12-23
  - fix:
    - 更新server2, 提升查找notify的效率，85d7cdd85b9
- 1.0.30-beta.4 2024-12-23
  - fix:
    - 修复dialog未定义的问题，更新server2，99e4b333e0f
- 1.0.30-beta.3 2024-12-23
  - fix:
    - 更新发送图片逻辑，更新server2，518a8c782e8
- 1.0.30-beta.2 2024-12-23
  - fix:
    - 更新发送图片逻辑，更新server2，f49f110f637
- 1.0.30-beta.1 2024-12-23
  - fix:
    - 暂停会影响新一轮取消息
    - 发送图片会将图片滚动到可视区
    - 批量转人工增加try-catch
  - feat:
    - 支持历史订单
    - 更新server2，a8171f7eb70
- 1.0.29-beta.1 2024-12-19
  - feat:
    - 增加action可视化
  - fix:
    - 通过已读未读判断是否客服消息
    - 用户发送链接时覆盖其他商品信息
    - 更新server2，e3ae0bb93a5
- 1.0.28-beta.2 2024-12-19
  - fix:
    - 修复转人工时间配置跨天和off[0].replyMsg的问题
- 1.0.28-beta.1 2024-12-18
  - feat:
    - support new transfer requirements
    - 更新server2，transfer_v2，f3cdc6f1a28
- 1.0.27 2024-12-17
  - feat:
    - 更新server2，批量转人工之前先离线, 4f3f5c63e23
- 1.0.26 2024-12-17
  - fix:
    - 更新server2，修复日志慢, 7a166c71cab
- 1.0.26-alpha.3 2024-12-16
  - fix:
    - 更新server2，检测图片弹窗, 94d065e11fd
- 1.0.26-alpha.2 2024-12-16
  - fix:
    - 修复转接状态更新异常
    - 兼容图片地址为空
- 1.0.26-alpha.1 2024-12-13
  - fix:
    - 更新server2，time.sleep, a5148354ac2
- 1.0.25-alpha.1 2024-12-13
  - fix:
    - 回复消息取tbUid
- 1.0.24-alpha.1 2024-12-13
  - fix:
    - 回复消息取tbUid
- 1.0.23-alpha.1 2024-12-13
  - fix:
    - 更换uid字段
- 1.0.22-alpha.1 2024-12-13
  - feat:
    - 兼容已关闭的订单
    - 订单数过滤已关闭订单
    - 增加报警
    - 增加uid
    - 更新server2,增加用户id参数，Exists增加第二参数，批量转人工增加1s间隔，发送或收取图片时判断弹窗，内置uiautomation,a836413663d
- 1.0.21-alpha.1 2024-12-10
  - feat:
    - 更新server2,修复无用户消息串的问题，设置鼠标瞬移，0ad44ee300c
- 1.0.20-alpha.1 2024-12-09
  - feat:
    - 支持transferDisabled & transferMsg多条消息
    - 更新server2,增加抓订单log & 放开没有用户消息的限制，ffca6c53e3c
- 1.0.19-alpha.1 2024-12-06
  - refactor:
    - 重构抓订单逻辑，改成接口，2e5586a65a4
- 1.0.18-alpha.1 2024-12-05
  - feat:
    - 增加重复消息报警
  - fix:
    - 多条消息只上报第一条状态
    - 更新server2, remove ocr，b8a52ab7379
- 1.0.17-alpha.1 2024-12-04
  - fix:
    - 更新server2,fix 'to'，843841cd377
- 1.0.16-alpha.1 2024-12-03
  - fix:
    - 判断是否配置欢迎语和售前
    - 不上报转人工状态
    - 更新server2，提升回复速度, 6afab939482
    - 改回原始抓商品逻辑
- 1.0.15-alpha.1 2024-12-02
  - fix:
    - 更新server2,增加no参数 & fix last message，767852a4177
- 1.0.14-alpha.1 2024-12-02
  - fix:
    - 更新server2,增加try-catch & fix user variable，379f5d32542
- 1.0.13-alpha.1 2024-11-28
  - fix:
    - 更新server2,增加sentry，03bb385b968
- 1.0.12-alpha.1 2024-11-28
  - fix:
    - 更新server2,增加3层/4层结构稳定性，793e720e186
- 1.0.11-alpha.1 2024-11-27
  - fix:
    - 更新server2,增加无订单的兼容性，1ae90a0f991
- 1.0.10-alpha.1 2024-11-27
  - fix:
    - 更新server2,增加抓订单的兼容性，3b63a24c605
- 1.0.9-alpha.1 2024-11-27
  - fix:
    - reply和transfer死锁
- 1.0.8-alpha.1 2024-11-27
  - refactor:
    - 将burrow放到cos中
- 1.0.7-alpha.1 2024-11-26
  - fix:
    - reply和transfer加入action队列
- 1.0.6-alpha.1 2024-11-26
  - feat:
    - 增加商品详情public接口
    - 增加转人工话术
    - 更新burrow到4
  - chore:
    - 更新server2，日志增加时间，5a8e82282c3
- 1.0.5-alpha.1 2024-11-25
  - fix:
    - 关闭burrow增加Force参数
- 1.0.4-alpha.1 2024-11-25
  - fix:
    - 关闭千牛未正确退出server2
    - card关闭时，如果home已经关闭，会导致报错
  - feat:
    - 更新burrow到3
- 1.0.3-alpha.1 2024-11-25
  - fix:
    - 关闭千牛未重置店铺用户数状态
    - 存在已关闭订单时候可能出现的异常
- 1.0.2-alpha.1 2024-11-25
  - feat:
    - 增加自动下载千牛
    - 增加自定义onbeforeunload文案
- 1.0.1-alpha.1 2024-11-22
  - feat:
    - 限制只能启动一个应用
    - 增加opentelemetry和sentry
  - bugfix:
    - 修复应用关闭bug
    - 只针对burrow服务未返回客服时候做重试
- 1.0.0-alpha.10 2024-11-22
  - feat:
    - rpa处理之前先判断是否已转接客服
