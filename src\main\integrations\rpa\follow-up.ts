import { db } from '../../utils/sqlite';
import dayjs from 'dayjs';
import uniqueRandomArray from 'unique-random-array';
import ms from 'ms';

export default class FollowUp {
  checkInterval: NodeJS.Timeout;
  followUpInterval: number[];
  followUpText: string[];
  followUpImage: string[];
  delimiter = '@!@';

  constructor(
    private configId,
    private ctx
  ) {
    this.checkInterval = setInterval(this.check.bind(this), ms('10s'));
    let intervalSum = 0;
    this.followUpInterval = ctx.agentConfig.press_interval?.map((interval) => {
      intervalSum += interval;
      return intervalSum;
    });
    ctx.log.info('FollowUp interval is', this.followUpInterval);
    this.followUpText = ctx.agentConfig.press_payment_list || [];
    this.followUpImage = ctx.agentConfig.pressImgs || [];
    this.cleanup();
  }

  addMessage(shop, user, msg) {
    const stmt = db.prepare(
      'INSERT OR REPLACE INTO chat2 (config_id, shop, user, message, create_time, status, follow_up_text, follow_up_image) VALUES (?, ?, ?, ?, ?, ?, ?, ?)'
    );
    stmt.run(this.configId, shop, user, msg, dayjs().format(), 0, null, null);
  }

  check() {
    db.each(
      'SELECT * FROM chat2 WHERE config_id = ? AND status >= 0 AND status <= 100',
      this.configId,
      (err, result: any) => {
        if (err) {
          this.ctx.log.error('check follow up error', err);
          return;
        }

        const lastMsgTime = dayjs(result.create_time);

        if (dayjs().diff(lastMsgTime, 's') > this.followUpInterval[result.status]) {
          this.ctx.log.debug('start to follow up', result.user, result.status);
          let followUpTextArr;
          let followUpImageArr;

          if (!result.follow_up_text) {
            const randomTextFn = uniqueRandomArray(this.followUpText);
            followUpTextArr = this.followUpInterval.map(randomTextFn);
          } else {
            followUpTextArr = result.follow_up_text.split(this.delimiter);
          }
          if (!result.follow_up_image) {
            const randomImageFn = uniqueRandomArray(this.followUpImage);
            followUpImageArr = this.followUpInterval.map((_, i) =>
              i === 0 ? '' : randomImageFn()
            );
          } else {
            followUpImageArr = result.follow_up_image.split(this.delimiter);
          }
          this.ctx.log.debug('followUp content', result.user, followUpTextArr, followUpImageArr);

          this.ctx.userManager
            .get(result.user, result.shop)
            .checkOrders()
            .then((orders) => {
              if (orders.length) {
                this.ctx.log.info('有订单，不发送延迟消息', result.user, result.shop);
                db.run(
                  `UPDATE chat2 SET status = ?
                WHERE id = ?
                `,
                  200,
                  result.id,
                  (err, result) => {
                    if (err) {
                      this.ctx.log.error('update follow up error2', err);
                    } else {
                      this.ctx.log.info('update follow up success2');
                    }
                  }
                );
                return;
              }
              if (followUpTextArr[result.status]) {
                this.ctx.log.info(
                  'rpa:delay-msg 延迟消息',
                  result.user,
                  'text',
                  followUpTextArr[result.status],
                  'followUp-text'
                );
                this.ctx.userManager.get(result.user, result.shop).reply(
                  [
                    {
                      type: 'text',
                      text: [
                        {
                          value: followUpTextArr[result.status]
                        }
                      ]
                    }
                  ],
                  'followUp-text'
                );
              }

              if (followUpImageArr[result.status]) {
                this.ctx.log.info(
                  'rpa:delay-msg 延迟消息',
                  result.user,
                  'image',
                  followUpImageArr[result.status],
                  'followUp-image'
                );
                this.ctx.userManager.get(result.user, result.shop).reply(
                  [
                    {
                      type: 'picture',
                      text: [
                        {
                          value: followUpImageArr[result.status]
                        }
                      ]
                    }
                  ],
                  'followUp-image'
                );
              }

              db.run(
                `UPDATE chat2 SET status = ?, follow_up_text = ?, follow_up_image = ?
                WHERE id = ?
                `,
                result.status + 1 === this.followUpInterval.length ? 101 : result.status + 1,
                followUpTextArr.join(this.delimiter),
                followUpImageArr.join(this.delimiter),
                result.id,
                (err, result) => {
                  if (err) {
                    this.ctx.log.error('update follow up error', err);
                  } else {
                    this.ctx.log.info('update follow up success');
                  }
                }
              );
            });
        }
      }
    );
  }

  cleanup() {
    db.run(
      'DELETE FROM chat2 WHERE config_id = ? AND create_time < ?',
      this.configId,
      dayjs().startOf('d').format(),
      (err, result) => {
        if (err) {
          this.ctx.log.error('cleanup follow up error', err);
        } else {
          this.ctx.log.info('cleanup follow up success');
        }
      }
    );
  }

  destroy() {
    clearInterval(this.checkInterval);
  }
}
