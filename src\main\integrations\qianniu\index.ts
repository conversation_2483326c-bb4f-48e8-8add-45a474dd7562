import { Page } from 'puppeteer-core';
import { io } from 'socket.io-client';
import UserManager from '../rpa/user-manager';
import { Logger } from 'log4js';
import { NewData } from '../rpa/type';
import { ChildProcess, exec, spawn, spawnSync } from 'node:child_process';
import server from '../../../../resources/server2.exe?asset&asarUnpack';
import { app, globalShortcut } from 'electron';
import { Span, trace, metrics } from '@opentelemetry/api';
import { name, version } from '../../../../package.json';
import delay from 'delay';
import path from 'node:path';
import fs from 'fs-extra';
import { loadConfig } from '../rpa/client-config';
import { upload } from '../rpa/utils/upload-chat';
import ms from 'ms';
import FollowUp from '../rpa/follow-up';
import { getImgPathByUrl } from '../rpa/utils/download-image';
import * as Sentry from '@sentry/electron/main';
import _ from 'lodash';
import { CronJob } from 'cron';
import Chat, { ReplyMessageStatus, UserStatus } from '../rpa/chat';
import GrabGoods from './grab-goods';
import { Integration } from '../Integration';
import { createPromiseCapability } from '../../utils/promise';
import { db } from '../..//utils/sqlite';
import User from '../rpa/user';
import PQueue from 'p-queue';
import download from 'download';
import Feishu from '../../utils/notice/feishu';
import { isBetweenPeriod } from '../rpa/utils/time';
import { getRandomStrings } from '../rpa/utils/string';
import { randomUUID } from 'node:crypto';
import { report } from '../rpa/utils/monitor';
import MessageService from './message-service';
import Window from '../../window';
import { monitorWindow } from '../../utils/win32';
import { is } from '@electron-toolkit/utils';
import { syncStatistics } from './sync-statistics';
import { updateCSStatus, updateStatus } from './update-status';
import * as readline from 'node:readline';
import dayjs from 'dayjs';
import packageJson from '../../../../package.json';
import QianniuOrderService from './order-service';

const tracer = trace.getTracer(name, version);
const heartbeat = metrics.getMeter(packageJson.name).createGauge('muyan-qianniu-heartbeat');
const replyCount = metrics.getMeter(packageJson.name).createCounter('muyan-qianniu-reply');
const transferCount = metrics.getMeter(packageJson.name).createCounter('muyan-qianniu-transfer');

enum ReplyStatus {
  CANT_FIND_USER = -1,
  OK,
  DUPLICATE_MESSAGE,
  FORBIDDEN_WORD,
  DUPLICATE_QUESTION,
  UNKNOWN_ALERT,
  USER_HAS_BEEN_TRANSFERRED = -20
}

class Qianniu extends Integration {
  static runningCount = 0;
  // @ts-ignore maybe use later
  // #config: PddConfig | undefined;
  #page: Page | undefined;
  #runningPromise: ReturnType<typeof createPromiseCapability>;
  #initDBPromise: ReturnType<typeof createPromiseCapability>;
  #socket: ReturnType<typeof io>;
  #ctx: {
    env?: any;
    emit?: any;
    userManager?: UserManager;
    followUp?: FollowUp;
    chatService?: Chat;
    grabGoods?: GrabGoods;
    orderService?: QianniuOrderService;
    log?: Logger;
    agentConfig?: any;
    // cacheDir?: string;
    platform: 'qianniu';
    feishu?: Feishu;
    messageService?: MessageService;
  };
  #server: ChildProcess;
  #checkQianniuStatusInterval: NodeJS.Timeout;
  #checkBurrowInterval: NodeJS.Timeout;
  #timer: any;
  #transferBatchJob: CronJob[] | null;
  #syncStatisticsJob: CronJob[] | null;
  #actionQueue = new PQueue({ concurrency: 1 });
  #burrowPath?: string;
  #grabNo = 0;
  #lastGrabPromise: ReturnType<typeof createPromiseCapability>;
  #replyPromise: ReturnType<typeof createPromiseCapability>;
  #replyWindow: Window | undefined;
  #detailWindow: Window | undefined;
  #offQianniuWindowMonitor: () => void | undefined;
  #connectedNo: number = 0;
  #checkCSStatusInterval: NodeJS.Timeout;
  #status: {
    ai: 'initial' | 'connect' | 'disconnect' | 'error';
    qianniu: 'initial' | 'running' | 'not-running' | 'unkonwn';
  } = { ai: 'initial', qianniu: 'initial' };
  #updateStatusInterval: NodeJS.Timeout;
  #shops: Set<string> = new Set();
  #transferFailedUsers: { [key: string]: { [key: string]: boolean } } = {};

  constructor(tab, config) {
    super(tab, config);
    this.#ctx = {
      platform: 'qianniu'
    };
    this.#ctx.log = this.getLogger();
    this.#ctx.emit = this.emit.bind(this);
    // this.#ctx.cacheDir = path.join(this.getCacheDir());
    this.#runningPromise = createPromiseCapability();
    this.#initDBPromise = createPromiseCapability();
    this.initDB();
    this.#timer = null;
    this.#lastGrabPromise = createPromiseCapability();
    this.#actionQueue.on('completed', ([id, params]) => {
      // console.log('task completed', id, params);
      report(
        {
          id,
          action: 'completed'
        },
        this.#ctx
      );
    });
    this.#actionQueue.on('error', (error) => {
      this.#ctx.log!.error('action queue error', error);
    });
    // this.#actionQueue.on('add', () => {
    //   console.log(
    //     `Task is added.  Size: ${this.#actionQueue.size}  Pending: ${this.#actionQueue.pending}`
    //   );
    // });
    //
    // this.#actionQueue.on('next', () => {
    //   console.log(
    //     `Task is completed.  Size: ${this.#actionQueue.size}  Pending: ${this.#actionQueue.pending}`
    //   );
    // });
  }

  async start() {
    await this.run();
  }

  initDB() {
    db.serialize(() => {
      db.run(`CREATE TABLE IF NOT EXISTS chat2 (
        id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
        config_id TEXT NOT NULL,
        shop TEXT NOT NULL,
        user TEXT NOT NULL,
        message TEXT NOT NULL,
        create_time TEXT NOT NULL,
        status INTEGER NOT NULL DEFAULT 0,  -- 0未催付，1-100已完成n轮催付，101完成催付,200存在订单
        follow_up_text TEXT,
        follow_up_image TEXT
      ) STRICT`);
      db.run(`CREATE UNIQUE INDEX IF NOT EXISTS chat_idx_2 ON chat2(config_id, shop, user)`);
      db.run('DROP TABLE IF EXISTS chat');

      db.run(`CREATE TABLE IF NOT EXISTS grab_goods (
        id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
        shop TEXT NOT NULL,
        last_grab_time TEXT NOT NULL
      ) STRICT`);
      db.run(`CREATE UNIQUE INDEX IF NOT EXISTS grab_goods_shop_idx ON grab_goods(shop)`);

      db.run(`CREATE TABLE IF NOT EXISTS message (
        id INTEGER NOT NULL PRIMARY KEY AUTOINCREMENT,
        uuid TEXT NOT NULL,
        open_time TEXT NOT NULL,
        shop TEXT NOT NULL,
        uid TEXT NOT NULL,
        nickname TEXT NOT NULL,
        answer TEXT NOT NULL,
        question TEXT NOT NULL,
        create_time TEXT NOT NULL,
        updated_time TEXT DEFAULT NULL
      ) STRICT`);
      db.run(`CREATE INDEX IF NOT EXISTS message_idx ON message(open_time, shop, uid)`);
      this.#initDBPromise.resolve();
    });
  }

  async run() {
    try {
      if (Qianniu.runningCount++) {
        this.runCodeInView(`alert('Qianniu client is already running')`);
        // this.emit('error', {
        //   message: 'Qianniu client is already running'
        // });
        return;
      }
      const config = await loadConfig(
        this.config.options!.endpoint!,
        this.config.options!.token!,
        this.#ctx.log
      );
      if (config[0]) {
        // const e = new Error();
        // e.name = '配置文件加载失败';
        // throw e;
        if (config[0].message.includes('配置不存在')) {
          this.runCodeInView(`alert('当前AI客服不存在，请刷新页面')`);
        } else {
          this.runCodeInView(`alert('配置文件加载失败')`);
        }
        return;
      }
      this.#ctx.agentConfig = config[1];
      this.#ctx.log!.info('配置文件加载完成', this.#ctx.agentConfig);
      this.#ctx.env = {
        id: this.config.id,
        host: new URL(this.config.options!.endpoint!).origin,
        accessToken: this.config.options!.token!,
        aiAgentConfigId: this.config.options!.aiAgentConfigId,
        // endpoint: config.env.MYQA_ENDPOINT_V2 + '/api/v2',
        // assistantId: config.env.MYQA_ASSISTANT_ID,
        // secretKey: config.env.MYQA_SECRET_KEY,
        // serverEndpoint: config.env.SERVER_ENDPOINT,
        // @ts-ignore isSilence
        isSilence: this.config.options!.isSilence,
        // @ts-ignore isSilence
        isAssistant: this.config.options!.isAssistant,
        config: this.config,
        clientId: this.clientId
        // includeExe: config.env.INCLUDE_EXE
      };

      // @ts-ignore get exist
      const username = this.config.options.username;
      this.#timer = setInterval(() => {
        heartbeat.record(Date.now(), {
          mode: import.meta.env.MODE,
          version: packageJson.version,
          instance: this.clientId,
          loggedUser: username,
          // env: is.dev ? 'development' : 'production',
          type: this.#ctx.env.isSilence
            ? 'silence'
            : this.#ctx.env.isAssistant
              ? 'assistant'
              : 'formal'
        });
      }, ms('30s'));

      if (this.#ctx.env.isAssistant) {
        this.#ctx.log!.info('assistant mode');
        this.#ctx.messageService = new MessageService(this.#ctx);
        // this.tab.view.webContents.send('mode', 'assistant');
      }
      this.getPptrPage().then((page) => {
        this.#page = page;
      });

      await this.#initDBPromise.promise;
      this.registerHotKey();
      let serverAddr = 'ws://127.0.0.1:8000';
      if (this.#ctx.agentConfig.rpa?.serverEndpoint) {
        serverAddr = `ws://${this.#ctx.agentConfig.rpa?.serverEndpoint}:8000`;
        if (this.#ctx.env.isAssistant) {
          this.addAssistantWindow();
        }
      } else {
        this.startServer();
        if (this.#ctx.agentConfig.rpa?.includeExe) {
          this.checkQianniuStatus((err, status) => {
            if (status === 'not-running') {
              this.startQianniu();
            }
            if (this.#ctx.env.isAssistant) {
              this.addAssistantWindow();
            }
            this.#checkQianniuStatusInterval = setInterval(() => {
              this.checkQianniuStatus();
            }, ms('5s'));
          });
        }
        if (this.#ctx.agentConfig.rpa?.burrowVersion) {
          this.#burrowPath = path.join(
            app.getPath('appData'),
            'AliWork',
            `Burrow.Desktop.${this.#ctx.agentConfig.rpa?.burrowVersion}.exe`
          );

          const startBurrow = _.throttle(
            () => {
              this.startBurrow();
            },
            ms('30s'),
            { leading: true, trailing: false }
          );
          const intervalCheck = () => {
            this.#checkBurrowInterval = setInterval(() => {
              this.checkBurrowRunning((err, action) => {
                if (action === 'start') {
                  startBurrow();
                }
              });
            }, ms('2s'));
          };
          this.checkBurrowRunning((err, action) => {
            if (action === 'restart') {
              this.checkAndDownloadBurrow().then(() => {
                this.restartBurrow(() => {
                  intervalCheck();
                });
              });
            } else if (action === 'start') {
              this.checkAndDownloadBurrow().then(() => {
                this.startBurrow(() => {
                  intervalCheck();
                });
              });
            } else if (action === 'no-op') {
              intervalCheck();
            }
          });
        }
      }
      this.updateStatus();
      if (this.#ctx.agentConfig.checkCSStatusInterval && this.#ctx.agentConfig.checkCSStatusShop) {
        this.#checkCSStatusInterval = setInterval(
          () => {
            const connectedNo = this.#connectedNo;
            this.addToActionQueue(
              async () => {
                if (connectedNo !== this.#connectedNo) {
                  this.#ctx.log!.debug(
                    'connectNo发生变化，不检查客服状态',
                    connectedNo,
                    this.#connectedNo
                  );
                  return;
                }
                this.#ctx.log!.info('开始检查客服状态');
                const status = await this.#socket.emitWithAck(
                  'get-cs-status',
                  this.#ctx.agentConfig.checkCSStatusShop
                );
                this.#ctx.log!.info('客服状态', status);
                await updateCSStatus(this.#ctx, this.#ctx.agentConfig.checkCSStatusShop, status);
              },
              {
                type: 'get-cs-status'
              }
            );
          },
          ms(this.#ctx.agentConfig.checkCSStatusInterval as string)
        );
      }
      // const { endpoint, assistantId } = this.#ctx.env;
      // const conf = await loadConfig(
      //   endpoint,
      //   assistantId,
      //   this.#ctx.log,
      //   this.#ctx.env.accessToken
      // );
      // if (!conf?.meta_data) {
      //   const e = new Error();
      //   e.name = '配置文件加载失败';
      //   throw e;
      // }
      // this.#ctx.agentConfig = conf.meta_data;
      // if (conf.meta_data.secret_key) {
      //   this.#ctx.env.secretKey = conf.meta_data.secret_key;
      // }
      if (this.#ctx.agentConfig.transferBatchSwitch && this.#ctx.agentConfig.transferBatchCron) {
        let cron = this.#ctx.agentConfig.transferBatchCron;
        if (!Array.isArray(cron)) {
          cron = [cron];
        }
        this.#transferBatchJob = cron.map((c) => {
          return new CronJob(
            c, // cronTime
            () => {
              if (this.#ctx.env.isSilence) {
                this.#ctx.log!.info('Silence mode, skip transfer batch');
                return;
              }
              this.transferBatch();
            },
            null, // onComplete
            true // start
          );
        });
      }
      if (this.#ctx.agentConfig.syncStatisticsCron && this.#ctx.agentConfig.syncStatisticsShop) {
        let cron = this.#ctx.agentConfig.syncStatisticsCron;
        if (!Array.isArray(cron)) {
          cron = [cron];
        }
        this.#syncStatisticsJob = cron.map((cr) => {
          return new CronJob(
            cr,
            () => {
              syncStatistics(
                this.#ctx,
                this.#ctx.agentConfig.syncStatisticsShop,
                this.config.options!.aiAgentConfigId
              );
            },
            null, // onComplete
            true // start
          );
        });
      }
      if (this.#ctx.agentConfig.rpa?.feishuToken) {
        this.#ctx.log!.info('初始化飞书');
        this.#ctx.feishu = new Feishu(this.#ctx.agentConfig.rpa?.feishuToken, this.#ctx.log);
      }

      this.#ctx.userManager = new UserManager(this.#ctx);
      // this.#ctx.followUp = new FollowUp(String(this.config.id), this.#ctx);
      this.#ctx.chatService = new Chat(this.#ctx);
      this.#ctx.grabGoods = new GrabGoods(this.#ctx);
      this.#ctx.grabGoods.start();
      this.#ctx.orderService = new QianniuOrderService(this.#ctx);

      this.on(
        'response-to-user',
        async (
          user,
          content,
          { msgId, shop, backendId, uid, userStatus, question, transferReason, ignoreTransferred }
        ) => {
          if (this.#ctx.env.isSilence) {
            this.#ctx.log!.info('rpa:action 发送消息', user, JSON.stringify(content), msgId);
            this.#ctx.log!.info('rpa:action 回复消息或转人工', user, 'ok');
          } else if (this.#ctx.env.isAssistant) {
            await this.replyAssistant(
              user,
              content,
              msgId,
              shop,
              backendId,
              0,
              true,
              uid,
              userStatus,
              question
            );
          } else {
            await this.reply(
              user,
              content,
              msgId,
              shop,
              backendId,
              0,
              true,
              uid,
              userStatus,
              question,
              transferReason,
              ignoreTransferred
            );
          }
        }
      );

      this.on('grab-orders', async (uid, shop, promise) => {
        log.debug('grab-orders', uid, shop);
        // const res = await this.#socket.emitWithAck("grab-orders");
        const res = [];
        promise.resolve(res);
        log.debug('grab-orders-done', uid, shop, res);
      });

      const shopHasHangUp = {};

      this.on('user-count', async (shop, count) => {
        log.debug('user-count', shop, count);
        const hangUpShop = this.#ctx.agentConfig.hangUpShop;
        const hangUpThreshold = this.#ctx.agentConfig.hangUpThreshold;

        if (
          !this.#ctx.env.isSilence &&
          shop === hangUpShop &&
          count >= hangUpThreshold &&
          !shopHasHangUp[shop]
        ) {
          log.info('hang-up', shop);
          shopHasHangUp[shop] = true;
          const res = await this.#socket.emitWithAck('hang-up', hangUpShop);
          log.info('hang-up result', shop, res);
        }
      });

      const log = this.#ctx.log as Logger;
      this.#socket = io(serverAddr);

      this.#socket.on(
        'new-data',
        async (user: string, msg: Array<NewData>, shop: string, orders, batch, uid) => {
          await tracer.startActiveSpan('receive-msg', { root: true }, async (span: Span) => {
            log.debug(
              'new-data',
              shop,
              user,
              JSON.stringify(msg),
              span.spanContext().traceId,
              orders,
              batch,
              uid
            );
            this.#shops.add(shop);
            upload(
              {
                user,
                data: msg
              },
              this.#ctx
            );
            if (!uid) {
              this.tab.view.webContents.send('qianniu-client-status', 'error');
              this.#status.ai = 'error';
              this.#replyWindow?.setSize(360, 96);
              this.#replyWindow?.activeTab.view.webContents.send('qianniu-client-status', 'error');
              return;
            }
            try {
              const result = await this.#ctx
                .userManager!.get(uid, shop)
                .addNickname(user)
                .addMsgAndRun(msg, orders, batch, user);
            } catch (e) {
              log.error('handle new-data error', shop, user, uid, span.spanContext().traceId, e);
            }
            span.end();
          });
        }
      );

      this.#socket.on('grab-end-assistant', async (no) => {
        if (this.#replyPromise.isSettled) {
          log.debug('stop grab-end-assistant');
          return;
        }
        log.debug('grab-end-assistant', no, this.#replyPromise.isSettled);
        this.#grabNo = no + 1;
        await delay(100);
        this.#socket.emit('get-new-data-assistant', this.#grabNo);
      });

      this.#socket.on('grab-end', async (no) => {
        log.debug('grab-end', no);
        const nos = no.split('.');
        this.#grabNo = Number(nos[0]) + 1;
        const sentConnectedNo = Number(nos[1]);
        this.#lastGrabPromise.resolve();
        this.#lastGrabPromise = createPromiseCapability();
        if (sentConnectedNo !== this.#connectedNo) {
          this.#ctx.log!.debug('previous grab-end, stop loop');
          return;
        }
        if (this.#ctx.env.isSilence) {
          await delay(100);
          const connectedNo = this.#connectedNo;
          this.#socket.emit('get-new-data-silence', this.#grabNo + '.' + connectedNo);
        } else {
          // await delay(100);
          this.#ctx.log!.debug(
            'report:log',
            'action: get-new-data',
            '等待队列长度',
            this.#actionQueue.size + this.#actionQueue.pending
          );
          const connectedNo = this.#connectedNo;
          this.addToActionQueue(
            async () => {
              // console.log('emit get-new-data', this.#grabNo, connectedNo, this.#connectedNo);
              // console.log('开始执行emit', this.#grabNo + '.' + connectedNo);
              if (connectedNo !== this.#connectedNo) {
                this.#ctx.log!.debug('connectNo发生变化，不执行', connectedNo, this.#connectedNo);
                return;
              }
              this.#socket.emit('get-new-data', this.#grabNo + '.' + connectedNo);
              try {
                await this.#lastGrabPromise.promise;
              } catch (e) {
                this.#ctx.log!.error('get-new-data error', e);
              }
            },
            {
              type: 'get-new-data',
              no: this.#grabNo + '.' + connectedNo
            }
          );
          // this.#socket.emit('get-new-data', this.#grabNo);
        }
      });

      this.#socket.on('connect', () => {
        log.info('client socket connect');
        this.#connectedNo++;
        // 开发模式中这个事件在前端load之前触发，前端没接收到
        this.tab.view.webContents.send('qianniu-client-status', 'connect');
        this.#status.ai = 'connect';
        if (!this.#replyPromise || this.#replyPromise.isSettled) {
          this.#replyWindow?.setSize(320, 96);
          this.#replyWindow?.activeTab.view.webContents.send('qianniu-client-status', 'connect');
        } else {
          this.#replyWindow?.setSize(548, 96);
          this.#replyWindow?.activeTab.view.webContents.send('qianniu-wait-reply');
        }
        // if (this.#ctx.env.isSilence) {
        //   const connectedNo = this.#connectedNo;
        //   this.#socket.emit('get-new-data-silence', this.#grabNo + '.' + connectedNo);
        // } else {
        //   const connectedNo = this.#connectedNo;
        //   this.addToActionQueue(
        //     async () => {
        //       // console.log('开始执行emit', this.#grabNo + '.' + connectedNo);
        //       if (connectedNo !== this.#connectedNo) {
        //         this.#ctx.log!.debug('connectNo发生变化，不执行', connectedNo, this.#connectedNo);
        //         return;
        //       }

        //       this.#socket.emit('get-new-data', this.#grabNo + '.' + connectedNo);
        //       try {
        //         await this.#lastGrabPromise.promise;
        //       } catch (e) {
        //         this.#ctx.log!.error('get-new-data error2', e);
        //       }
        //     },
        //     {
        //       type: 'get-new-data',
        //       no: this.#grabNo + '.' + connectedNo
        //     }
        //   );
        // }
      });
      this.#socket.on('connect_error', (err) => {
        log.error('client socket error ' + this.#socket.active, err);
        if (!this.tab.view.webContents.isDestroyed()) {
          this.tab.view.webContents.send('qianniu-client-status', 'error');
          if (this.#status.ai !== 'initial') {
            this.#status.ai = 'error';
          }
          this.#replyWindow?.setSize(360, 96);
          this.#replyWindow?.activeTab.view.webContents.send('qianniu-client-status', 'error');
        }
      });
      this.#socket.on('disconnect', (reason) => {
        log.warn('client socket disconnect ' + this.#socket.active, reason);
        this.#lastGrabPromise.reject('disconnect');
        this.#lastGrabPromise = createPromiseCapability();
        if (!this.tab.view.webContents.isDestroyed()) {
          this.tab.view.webContents.send('qianniu-client-status', 'disconnect');
          this.#status.ai = 'disconnect';
          this.#replyWindow?.setSize(340, 96);
          this.#replyWindow?.activeTab.view.webContents.send('qianniu-client-status', 'disconnect');
        }
      });

      // await this.#page!.goto(
      //   "https://www.baidu.com",  // TODO url?
      //   { waitUntil: "networkidle0" },
      // );
    } catch (e: any) {
      this.#ctx.log!.error('qianniu run failed', e);
      let message = e.name;
      if (e.message === 'not supported os darwin') {
        message = 'mac下不能启动千牛';
      }
      this.runCodeInView(`alert('${message}')`);
      // this.emit('error', {
      //   message
      // });
    } finally {
      this.#runningPromise.resolve();
    }
  }

  addAssistantWindow() {
    this.#offQianniuWindowMonitor = monitorWindow('千牛接待台', (res) => {
      // this.#ctx.log!.debug('window monitor', res);
      if (!this.#replyWindow) {
        this.#replyWindow = new Window({
          features: 'frameless,width=320,height=96,resizable=false',
          openTab: {
            url: is.dev
              ? process.env['ELECTRON_RENDERER_URL'] + '/add-ons/qianniu_reply/'
              : path.join(__dirname, '../renderer/add-ons/qianniu_reply/index.html'),
            options: {
              // webContents,
              // webPreferences,
              // opener: this
            }
          }
        });
        this.#replyWindow.activeTab.webContents.ipc.on('handover', () => {
          this.#ctx.log!.info('恢复执行');
          this.#replyPromise.resolve();
        });
        this.#replyWindow.activeTab.webContents.ipc.on('transferred', () => {
          this.#ctx.log!.info('恢复执行,转交人工');
          this.#replyPromise.resolve('transferred');
        });
        if (res) {
          this.#replyWindow.setAlwaysOnTop(true, 'screen-saver');
        }
      }

      if (!this.#detailWindow) {
        this.#detailWindow = new Window({
          features: 'frameless,width=320,height=724,left=1000,top=100,resizable=false',
          openTab: {
            url: is.dev
              ? process.env['ELECTRON_RENDERER_URL'] + '/add-ons/qianniu_details/'
              : path.join(__dirname, '../renderer/add-ons/qianniu_details/index.html'),
            options: {
              // webContents,
              // webPreferences,
              // opener: this
            }
          }
        });
        if (res) {
          this.#detailWindow.setAlwaysOnTop(true, 'screen-saver');
        }
      }

      if (res) {
        this.#replyWindow.setPosition(res.left + 305, res.bottom);
        this.#detailWindow.setPosition(res.right, res.top);
      }
    });
  }

  startServer() {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }
    const imagePath = path.join(this.getCacheDir(), 'chat');
    fs.ensureDirSync(imagePath);
    this.#server = spawn(server, ['-d', imagePath], {
      stdio: ['ignore', 'overlapped', 'overlapped']
    });
    this.#server.stdout!.setEncoding('utf-8');
    const rl = readline.createInterface({ input: this.#server.stdout! });
    rl.on('line', (line) => {
      if (!line.startsWith('debug')) {
        return;
      }
      this.#ctx.log!.debug('server out', line);
    });
    // this.#server.stdout!.on('data', (data) => {
    //   if (!data.startsWith('debug')) {
    //     return;
    //   }
    //   this.#ctx.log!.debug('server out', data);
    // });
    this.#server.stderr!.setEncoding('utf-8');
    this.#server.stderr!.on('data', (data) => {
      this.#ctx.log!.debug('server err', data);
    });
    this.#server.on('close', (code, signal) => {
      this.#ctx.log!.info('server close', code, signal);
    });
  }

  async checkAndDownloadBurrow() {
    const appPath = app.getPath('appData');
    const exePath = path.join(appPath, 'AliWork');
    const burrowFilename = `Burrow.Desktop.${this.#ctx.agentConfig.rpa?.burrowVersion}.exe`;

    if (!fs.existsSync(path.join(exePath, burrowFilename))) {
      this.#ctx.log!.info('downloading burrow...');
      await download(
        `https://muyan-package-1312011744.cos.ap-shanghai.myqcloud.com/download/burrow/${burrowFilename}`,
        exePath
      );
      this.#ctx.log!.info('download burrow success');
    } else {
      this.#ctx.log!.info('burrow is already installed');
    }
  }

  checkBurrowRunning(cb?: (err, data?) => void) {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }
    const burrowFilename = path.basename(this.#burrowPath!, '.exe');
    exec(
      '(Get-Process Burrow.Desktop*).ProcessName',
      {
        shell: 'powershell.exe'
      },
      (err, data) => {
        // setTimeout(this.checkQianniuStatus.bind(this), ms('30s'));
        if (err) {
          this.#ctx.log!.error('check burrow running status failed', err);
          // this.startBurrow();
          // this.emit("qianniu-exe-status", "unkonwn");
          cb && cb(err);
          return;
        }
        if (data) {
          this.#ctx.log!.debug(data, 'is running');
          if (!data.includes(burrowFilename)) {
            // 版本不一致，需要重启
            this.#ctx.log!.debug('restart burrow...');
            cb && cb(null, 'restart');
            // this.checkAndDownloadBurrow().then(() => {
            //   this.restartBurrow();
            // });
          } else {
            cb && cb(null, 'no-op');
          }
        } else {
          this.#ctx.log!.debug('start burrow...');
          cb && cb(null, 'start');
          // this.checkAndDownloadBurrow().then(() => {
          //   this.startBurrow();
          // });
        }
        // this.#ctx.grabGoods?.start();
      }
    );
  }

  startBurrow(cb?) {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }
    exec(
      `Start-Process "${this.#burrowPath!}" -ArgumentList '-t -p qianniu' -WindowStyle Hidden`,
      {
        shell: 'powershell.exe'
      },
      (err, data) => {
        // setTimeout(this.checkQianniuStatus.bind(this), ms('30s'));
        if (err) {
          this.#ctx.log!.error('start burrow failed', err);
          // this.emit("qianniu-exe-status", "unkonwn");
          // cb && cb(err);
          return;
        }

        this.#ctx.log!.info('burrow started');
        cb && cb();
      }
    );
  }

  restartBurrow(cb?) {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }
    exec(
      `Start-Process "powershell" -WindowStyle Hidden -ArgumentList "Get-Process Burrow.Desktop* | Stop-Process -Force; Start-Process '${this.#burrowPath!}' -ArgumentList '-t -p qianniu' -WindowStyle Hidden"`,
      {
        shell: 'powershell.exe'
      },
      (err, data) => {
        // setTimeout(this.checkQianniuStatus.bind(this), ms('30s'));
        if (err) {
          this.#ctx.log!.error('restart burrow failed', err);
          // this.emit("qianniu-exe-status", "unkonwn");
          // cb && cb(err);
          return;
        }

        this.#ctx.log!.info('burrow started');
        cb && cb();
      }
    );
  }

  startQianniu() {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }

    const appPath = app.getPath('appData');
    const qianniuPath = path.join(appPath, 'AliWork', 'AliWorkbench', 'AliWorkbench.exe');

    spawn(qianniuPath, { detached: true, stdio: 'ignore' });
  }

  checkQianniuStatus(cb?: (err, data?) => void) {
    if (process.platform !== 'win32') {
      throw new Error('not supported os ' + process.platform);
    }
    const appPath = app.getPath('appData');
    const qianniuPath = path.join(appPath, 'AliWork');

    exec(
      '(Get-Process -Name AliWorkbench).path',
      {
        shell: 'powershell.exe'
      },
      (err, data) => {
        // setTimeout(this.checkQianniuStatus.bind(this), ms('30s'));
        if (err) {
          this.#ctx.log!.error('check qianniu running status failed', err);
          this.tab.view.webContents.send('qianniu-exe-status', 'unkonwn');
          this.#status.qianniu = 'unkonwn';
          // this.emit('qianniu-exe-status', 'unkonwn');
          cb && cb(err);
          return;
        }

        if (data.includes(qianniuPath)) {
          this.tab.view.webContents.send('qianniu-exe-status', 'running');
          this.#status.qianniu = 'running';
          // this.emit('qianniu-exe-status', 'running');
          cb && cb(null, 'running');
        } else {
          this.tab.view.webContents.send('qianniu-exe-status', 'not-running');
          this.#status.qianniu = 'not-running';
          // this.emit('qianniu-exe-status', 'not-running');
          cb && cb(null, 'not-running');
        }
      }
    );
  }

  addToActionQueue(fn, params) {
    const id = randomUUID();
    report(
      {
        id,
        action: 'add',
        params
      },
      this.#ctx
    );
    this.#actionQueue.add(async () => {
      this.tab.view.webContents.send('running-action', params);
      report(
        {
          id,
          action: 'run'
        },
        this.#ctx
      );
      await fn();
      return [id, params];
    });
  }

  async replyAssistant(
    user,
    content,
    msgId,
    shop,
    backendId?,
    transferStack = 0,
    reportReply = true,
    uid = -999,
    userStatus?: UserStatus,
    question?: { questionContent: string; questionRealAt: string; msgId: string }[]
  ) {
    if (this.#ctx.userManager!.get(uid, shop).transfered) {
      this.#ctx.log!.debug('用户已转走，不执行操作', user, uid, content, msgId, backendId);
      this.#ctx.chatService?.reply(
        backendId,
        ReplyMessageStatus.UserHasBeenTransfered,
        +new Date()
      );
      return;
    }

    const i = 0;
    const mergedContent: any[] = [];

    for (let j = 0; j < content.length; j++) {
      const cnt = content[j];
      if (cnt.type === 'picture') {
        const file = await getImgPathByUrl(cnt.text[0].value, this.getCacheDir(), this.#ctx.log);
        const data = {
          type: 'image',
          data: {
            path: path.dirname(file),
            file: path.basename(file),
            url: cnt.text[0].value
          }
        };
        mergedContent.push(data);
      } else {
        // 合并文本 text or transfer
        const data = {
          type: 'txt',
          data: j === 0 ? '' : '\n'
        };
        while (j < content.length && content[j].type !== 'picture') {
          if (content[j].type === 'text') {
            data.data += content[j].text[0].value + '\n';
          } else if (content[j].type === 'transfer') {
            data.data += '请转人工' + '\n';
          }
          j++;
        }
        j--;
        // data.data = data.data.trimEnd();
        mergedContent.push(data);
      }
    }
    if (mergedContent.length > 0 && mergedContent[mergedContent.length - 1].type === 'txt') {
      mergedContent[mergedContent.length - 1].data =
        mergedContent[mergedContent.length - 1].data.trimEnd();
    }

    const uuid = randomUUID();

    const _reply = async (span, i, uuid) => {
      if (this.#ctx.userManager!.get(uid, shop).transfered) {
        this.#ctx.log!.debug('用户已转走，不执行操作2', user, uid, content, msgId, backendId);
        this.#ctx.chatService?.reply(
          backendId,
          ReplyMessageStatus.UserHasBeenTransfered,
          +new Date()
        );
        return;
      }
      if (this.#ctx.env.isAssistant) {
        this.#detailWindow?.activeTab.view.webContents.send('current-user', {
          user,
          source: 'reply'
        });
        this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', uuid);
        this.#detailWindow?.activeTab.view.webContents.send(
          'user-reply-list',
          await this.#ctx.messageService?.getReplyList(shop, uid)
        );
      }
      const start = +new Date();
      const res: ReplyStatus = await this.#socket.emitWithAck(
        'reply-to-user-assistant',
        user,
        mergedContent,
        shop,
        this.#ctx.env.isAssistant
      );

      switch (res) {
        case ReplyStatus.CANT_FIND_USER:
          this.#ctx.log!.warn('无法找到用户', shop, user, uid, mergedContent, msgId);
          this.#ctx.log!.info('消息发送失败', shop, user, uid, mergedContent, msgId);
          this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
          this.#detailWindow?.activeTab.view.webContents.send('current-user', {
            user: undefined,
            source: 'after-reply'
          });
          // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, 'fail');
          this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.UserNotFound, +new Date());
          break;
        case ReplyStatus.OK:
          this.#ctx.chatService?.reportFillTime(backendId, +new Date());
          if (this.#ctx.env.isAssistant) {
            this.#ctx.log!.info(
              '消息填充成功',
              shop,
              user,
              uid,
              mergedContent,
              msgId,
              +new Date(),
              +new Date() - start
            );
            this.#replyWindow?.setSize(548, 96);
            this.#replyWindow?.activeTab.view.webContents.send('qianniu-wait-reply');
            this.#replyPromise = createPromiseCapability();
            this.#socket.emit('get-new-data-assistant', this.#grabNo);
            const res = await this.#replyPromise.promise;
            this.#replyWindow?.setSize(320, 96);
            this.#replyWindow?.activeTab.view.webContents.send('qianniu-client-status', 'connect');
            this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
            this.#detailWindow?.activeTab.view.webContents.send('current-user', {
              user: undefined,
              source: 'after-reply'
            });
            if (res === 'transferred') {
              this.#socket.emit('manual-transfer', user, shop, +new Date());
              this.#ctx.userManager!.get(uid, shop).transfered = true;
              this.#ctx.chatService?.updateUserStatus(shop, user, uid, true);
            }
          }
          this.#ctx.log!.info('消息发送成功', shop, user, uid, mergedContent, msgId);
          // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, 'ok');
          if (reportReply && !i) {
            this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.Success, +new Date());
          }
          break;
        case ReplyStatus.DUPLICATE_MESSAGE:
          this.#ctx.log!.warn('重复消息', shop, user, uid, mergedContent, msgId);
          if (reportReply && !i) {
            this.#ctx.chatService?.reply(
              backendId,
              ReplyMessageStatus.DuplicateMessage,
              +new Date()
            );
          }
          this.#ctx.feishu
            ?.send(
              '消息发送失败',
              `- **报警原因**：重复消息
- **报警优先级**：低
- **详情**：
- **店铺**：${shop}
- **用户**：${user}
- **uid**：${uid}
- **智能客服消息**：${JSON.stringify(mergedContent)}
- **msgId**：${msgId}
- **backendId**：${backendId}`
            )
            .then((res) => {
              this.#ctx.log!.info('报警结果', res);
            });
          // await this.transfer(user, shop, msgId, {
          //   backendId,
          //   isDuplicateMessage: true,
          //   transferStack,
          //   uid,
          //   userStatus
          // });
          break;
        case ReplyStatus.DUPLICATE_QUESTION:
          this.#ctx.log!.warn('重复提问', shop, user, uid, mergedContent, msgId);
          if (reportReply && !i) {
            this.#ctx.chatService?.reply(
              backendId,
              ReplyMessageStatus.DuplicateQuestion,
              +new Date()
            );
          }
          this.#ctx.feishu
            ?.send(
              '消息发送失败',
              `- **报警原因**：重复提问
- **报警优先级**：低
- **详情**：
- **店铺**：${shop}
- **用户**：${user}
- **uid**：${uid}
- **智能客服消息**：${JSON.stringify(mergedContent)}
- **msgId**：${msgId}
- **backendId**：${backendId}`
            )
            .then((res) => {
              this.#ctx.log!.info('报警结果', res);
            });
          // await this.transfer(user, shop, msgId, {
          //   backendId,
          //   isDuplicateMessage: true,
          //   transferStack,
          //   uid,
          //   userStatus
          // });
          break;
        case ReplyStatus.FORBIDDEN_WORD:
          this.#ctx.log!.warn('触发禁用词', shop, user, uid, mergedContent, msgId);
          this.#ctx.feishu
            ?.send(
              '消息发送失败',
              `- **报警原因**：团队禁用词
- **报警优先级**：🔴高
- **详情**：
- **店铺**：${shop}
- **用户**：${user}
- **uid**：${uid}
- **智能客服消息**：${JSON.stringify(mergedContent)}
- **msgId**：${msgId}
- **backendId**：${backendId}`
            )
            .then((res) => {
              this.#ctx.log!.info('报警结果', res);
            });
          // await this.transfer(user, shop, msgId, {
          //   backendId,
          //   isDuplicateMessage: true,
          //   transferStack,
          //   uid,
          //   userStatus
          // });
          break;
        case ReplyStatus.USER_HAS_BEEN_TRANSFERRED:
          this.#ctx.log!.debug(
            '用户已转走，rpa拒绝执行操作',
            shop,
            user,
            uid,
            content,
            msgId,
            backendId
          );
          this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
          this.#detailWindow?.activeTab.view.webContents.send('current-user', {
            user: undefined,
            source: 'after-reply'
          });
          this.#ctx.chatService?.reply(
            backendId,
            ReplyMessageStatus.UserHasBeenTransfered,
            +new Date()
          );
          break;
        default:
          this.#ctx.log!.error('未知的回复结果', res, shop, user, uid, mergedContent, msgId);
          this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
          this.#detailWindow?.activeTab.view.webContents.send('current-user', {
            user: undefined,
            source: 'after-reply'
          });
          this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.UnknownFailure, +new Date());
          this.#ctx.feishu
            ?.send(
              '消息发送失败',
              `- **报警原因**：消息发送失败
- **报警优先级**：低
- **详情**：
- **店铺**：${shop}
- **用户**：${user}
- **uid**：${uid}
- **智能客服消息**：${JSON.stringify(mergedContent)}
- **res**：${res}
- **msgId**：${msgId}
- **backendId**：${backendId}`
            )
            .then((res) => {
              this.#ctx.log!.info('报警结果2', res);
            });
      }

      replyCount.add(1, {
        mode: import.meta.env.MODE,
        version: packageJson.version,
        instance: this.#ctx.env.clientId,
        // @ts-ignore get exist
        loggedUser: this.config.options.username,
        // env: is.dev ? 'development' : 'production',
        shop: shop,
        uid: uid,
        user: user,
        status: res
      });

      span.end();
    };

    await tracer.startActiveSpan('send-msg-assistant', async (span) => {
      if (this.#ctx.env.isAssistant) {
        this.#ctx.messageService?.addReply(
          uuid,
          shop,
          uid,
          user,
          JSON.stringify(mergedContent), // TODO also change in html
          question?.map((q) => {
            const content = JSON.parse(q.questionContent);
            const value = _.get(content, '[0].text[0].value');
            return {
              question:
                value === '[图片]'
                  ? 'image|' + _.get(content, '[0].text[0].meta_data.pic.value')
                  : value,
              time: q.questionRealAt
            };
          }),
          (data) => {
            this.#detailWindow?.activeTab.view.webContents.send('user-reply-add', data);
          }
        );
      }
      if (transferStack) {
        // if (this.#ctx.env.isAssistant) {
        //   this.tab.view.webContents.send('current-user', {user, source: 'reply'});
        //   this.tab.view.webContents.send('user-reply-list', await this.#ctx.messageService?.getReplyList(shop, uid));
        // }
        // await _reply(span, i, uuid);  // no actual transfer in assistant mode
      } else {
        const start = +new Date();
        this.#ctx.log!.debug('reply加入action队列', shop, user, uid, mergedContent, msgId);
        this.#ctx.log!.debug(
          'report:log',
          'action: reply',
          '等待队列长度',
          this.#actionQueue.size + this.#actionQueue.pending
        );
        const _i = i;
        this.addToActionQueue(
          async () => {
            this.#ctx.log!.info(
              'rpa:action 发送消息/图片',
              shop,
              user,
              uid,
              mergedContent,
              msgId,
              _i
            );
            this.#ctx.log!.debug('等待时间', (+new Date() - start) / 1000, 's');
            // if (this.#ctx.env.isAssistant) {
            //   this.tab.view.webContents.send('current-user', {user, source: 'reply'});
            //   this.tab.view.webContents.send('user-reply-list', await this.#ctx.messageService?.getReplyList(shop, uid));
            // }
            await _reply(span, _i, uuid);
          },
          {
            type: 'reply-assistant',
            shop,
            user,
            uid,
            data: mergedContent,
            msgId
          }
        );
      }
    });
  }

  async reply(
    user,
    content,
    msgId,
    shop,
    backendId?,
    transferStack = 0,
    reportReply = true,
    uid = -999,
    userStatus?: UserStatus,
    question?: { questionContent: string; questionRealAt: string; msgId: string }[],
    transferReason?: string,
    ignoreTransferred: boolean = true
  ) {
    let i = 0;
    for (const cnt of content) {
      if (ignoreTransferred && this.#ctx.userManager!.get(uid, shop).transfered) {
        this.#ctx.log!.debug('用户已转走，不执行操作', user, uid, content, msgId, backendId);
        this.#ctx.chatService?.reply(
          backendId,
          ReplyMessageStatus.UserHasBeenTransfered,
          +new Date()
        );
        continue;
      }

      const uuid = randomUUID();
      let data;
      switch (cnt.type) {
        case 'text':
          data = {
            type: 'txt',
            data: cnt.text[0].value
          };

          this.#ctx.log!.info('rpa:action 准备发送消息', user, uid, data.data, msgId, i);
          break;
        case 'picture':
          const file = await getImgPathByUrl(cnt.text[0].value, this.getCacheDir(), this.#ctx.log);
          data = {
            type: 'image',
            data: {
              path: path.dirname(file),
              file: path.basename(file)
            }
          };
          this.#ctx.log!.info('rpa:action 准备发送图片', user, uid, file, msgId, i);
          break;
        case 'transfer':
          this.#ctx.log!.info('rpa:action 准备转接人工', user, uid, msgId, i);
          await this.transfer(user, shop, msgId, {
            backendId,
            transferStack,
            uid,
            userStatus,
            question
          });
          // this.#ctx.log!.info("rpa:action 发送转人工", user, msgId);
          return;
        case 'transfer-delay':
          this.#ctx.log!.info('rpa:action 延迟发货准备转接人工', user, uid, msgId, i);
          await this.transfer_delay(user, shop, msgId, {
            backendId,
            transferStack,
            uid,
            userStatus,
            question,
            transferReason
          });
          // this.#ctx.log!.info("rpa:action 发送转人工", user, msgId);
          return;
        default:
          this.#ctx.log!.error('Unsupported reply type', user, uid, cnt, msgId, i);
          throw new Error('Unsupported reply type ' + cnt.type);
      }
      const _reply = async (span, i, uuid) => {
        if (ignoreTransferred && this.#ctx.userManager!.get(uid, shop).transfered) {
          this.#ctx.log!.debug('用户已转走，不执行操作2', user, uid, content, msgId, backendId);
          this.#ctx.chatService?.reply(
            backendId,
            ReplyMessageStatus.UserHasBeenTransfered,
            +new Date()
          );
          return;
        }
        if (this.#ctx.env.isAssistant) {
          this.#detailWindow?.activeTab.view.webContents.send('current-user', {
            user,
            source: 'reply'
          });
          this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', uuid);
          this.#detailWindow?.activeTab.view.webContents.send(
            'user-reply-list',
            await this.#ctx.messageService?.getReplyList(shop, uid)
          );
        }
        const start = +new Date();
        const [res, reason]: [ReplyStatus, string | undefined] = await this.#socket.emitWithAck(
          'reply-to-user',
          user,
          data,
          shop,
          this.#ctx.env.isAssistant,
          ignoreTransferred
        );

        switch (res) {
          case ReplyStatus.CANT_FIND_USER:
            this.#ctx.log!.warn('无法找到用户', shop, user, uid, cnt, msgId);
            this.#ctx.log!.info('消息发送失败', shop, user, uid, cnt, msgId);
            this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
            this.#detailWindow?.activeTab.view.webContents.send('current-user', {
              user: undefined,
              source: 'after-reply'
            });
            // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, 'fail');
            this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.UserNotFound, +new Date());
            break;
          case ReplyStatus.OK:
            if (this.#ctx.env.isAssistant) {
              this.#ctx.log!.info(
                '消息填充成功',
                shop,
                user,
                uid,
                cnt,
                msgId,
                +new Date(),
                +new Date() - start
              );
              this.#replyWindow?.setSize(548, 96);
              this.#replyWindow?.activeTab.view.webContents.send('qianniu-wait-reply');
              this.#replyPromise = createPromiseCapability();
              this.#socket.emit('get-new-data-assistant', this.#grabNo);
              const res = await this.#replyPromise.promise;
              this.#replyWindow?.setSize(320, 96);
              this.#replyWindow?.activeTab.view.webContents.send(
                'qianniu-client-status',
                'connect'
              );
              this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
              this.#detailWindow?.activeTab.view.webContents.send('current-user', {
                user: undefined,
                source: 'after-reply'
              });
              if (res === 'transferred') {
                this.#ctx.userManager!.get(uid, shop).transfered = true;
                this.#ctx.chatService?.updateUserStatus(shop, user, uid, true);
              }
            }
            this.#ctx.log!.info('消息发送成功', shop, user, uid, cnt, msgId);
            // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, 'ok');
            if (reportReply && !i) {
              this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.Success, +new Date());
            }
            break;
          case ReplyStatus.DUPLICATE_MESSAGE:
            this.#ctx.log!.warn('重复消息', shop, user, uid, cnt, msgId);
            if (reportReply && !i) {
              this.#ctx.chatService?.reply(
                backendId,
                ReplyMessageStatus.DuplicateMessage,
                +new Date()
              );
            }
            if (this.#ctx.agentConfig.HITL) {
              this.#ctx.chatService?.refuseMessage(
                shop,
                user,
                uid,
                backendId,
                data.data,
                'duplicated'
              );
            } else {
              this.#ctx.feishu
                ?.send(
                  '消息发送失败',
                  `- **报警原因**：重复消息
- **报警优先级**：低
- **详情**：
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **智能客服消息**：${cnt.text[0].value}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
                )
                .then((res) => {
                  this.#ctx.log!.info('报警结果', res);
                });
              await this.transfer(user, shop, msgId, {
                backendId,
                isDuplicateMessage: true,
                transferStack,
                uid,
                userStatus
              });
            }
            break;
          case ReplyStatus.DUPLICATE_QUESTION:
            this.#ctx.log!.warn('重复提问', shop, user, uid, cnt, msgId);
            if (reportReply && !i) {
              this.#ctx.chatService?.reply(
                backendId,
                ReplyMessageStatus.DuplicateQuestion,
                +new Date()
              );
            }
            if (this.#ctx.agentConfig.HITL) {
              this.#ctx.chatService?.refuseMessage(
                shop,
                user,
                uid,
                backendId,
                data.data,
                'duplicated-question'
              );
            } else {
              this.#ctx.feishu
                ?.send(
                  '消息发送失败',
                  `- **报警原因**：重复提问
- **报警优先级**：低
- **详情**：
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **智能客服消息**：${cnt.text[0].value}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
                )
                .then((res) => {
                  this.#ctx.log!.info('报警结果', res);
                });
              await this.transfer(user, shop, msgId, {
                backendId,
                isDuplicateMessage: true,
                transferStack,
                uid,
                userStatus
              });
            }
            break;
          case ReplyStatus.FORBIDDEN_WORD:
            this.#ctx.log!.warn('触发禁用词', shop, user, uid, cnt, reason, msgId);
            if (this.#ctx.agentConfig.HITL) {
              this.#ctx.chatService?.refuseMessage(
                shop,
                user,
                uid,
                backendId,
                data.data,
                'forbidden',
                {
                  forbiddenWord: reason
                }
              );
            } else {
              this.#ctx.feishu
                ?.send(
                  '消息发送失败',
                  `- **报警原因**：团队禁用词
- **报警优先级**：🔴高
- **详情**：
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **智能客服消息**：${cnt.text[0].value}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
                )
                .then((res) => {
                  this.#ctx.log!.info('报警结果', res);
                });
              await this.transfer(user, shop, msgId, {
                backendId,
                isDuplicateMessage: true,
                transferStack,
                uid,
                userStatus
              });
            }
            break;
          case ReplyStatus.USER_HAS_BEEN_TRANSFERRED:
            this.#ctx.log!.debug(
              '用户已转走，rpa拒绝执行操作',
              shop,
              user,
              uid,
              content,
              msgId,
              backendId
            );
            this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
            this.#detailWindow?.activeTab.view.webContents.send('current-user', {
              user: undefined,
              source: 'after-reply'
            });
            this.#ctx.chatService?.reply(
              backendId,
              ReplyMessageStatus.UserHasBeenTransfered,
              +new Date()
            );
            break;
          default:
            this.#ctx.log!.error('未知的回复结果', res, shop, user, uid, cnt, msgId);
            this.#detailWindow?.activeTab.view.webContents.send('user-reply-selected', undefined);
            this.#detailWindow?.activeTab.view.webContents.send('current-user', {
              user: undefined,
              source: 'after-reply'
            });
            this.#ctx.chatService?.reply(backendId, ReplyMessageStatus.UnknownFailure, +new Date());
            this.#ctx.feishu
              ?.send(
                '消息发送失败',
                `- **报警原因**：消息发送失败
- **报警优先级**：低
- **详情**：
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **智能客服消息**：${cnt.text[0].value}
  - **res**：${res}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
              )
              .then((res) => {
                this.#ctx.log!.info('报警结果2', res);
              });
        }

        replyCount.add(1, {
          mode: import.meta.env.MODE,
          version: packageJson.version,
          instance: this.#ctx.env.clientId,
          // @ts-ignore get exist
          loggedUser: this.config.options.username,
          // env: is.dev ? 'development' : 'production',
          shop: shop,
          uid: uid,
          user: user,
          status: res
        });

        span.end();
      };

      await tracer.startActiveSpan('send-msg', async (span) => {
        if (this.#ctx.env.isAssistant) {
          this.#ctx.messageService?.addReply(
            uuid,
            shop,
            uid,
            user,
            data.type === 'image' ? 'image|' + cnt.text[0].value : cnt.text[0].value,
            question?.map((q) => {
              const content = JSON.parse(q.questionContent);
              const value = _.get(content, '[0].text[0].value');
              return {
                question:
                  value === '[图片]'
                    ? 'image|' + _.get(content, '[0].text[0].meta_data.pic.value')
                    : value,
                time: q.questionRealAt
              };
            }),
            (data) => {
              this.#detailWindow?.activeTab.view.webContents.send('user-reply-add', data);
            }
          );
        }
        if (transferStack) {
          // if (this.#ctx.env.isAssistant) {
          //   this.tab.view.webContents.send('current-user', {user, source: 'reply'});
          //   this.tab.view.webContents.send('user-reply-list', await this.#ctx.messageService?.getReplyList(shop, uid));
          // }
          await _reply(span, i, uuid);
        } else {
          const start = +new Date();
          this.#ctx.log!.debug('reply加入action队列', shop, user, uid, data, msgId);
          this.#ctx.log!.debug(
            'report:log',
            'action: reply',
            '等待队列长度',
            this.#actionQueue.size + this.#actionQueue.pending
          );
          const _i = i;
          this.addToActionQueue(
            async () => {
              this.#ctx.log!.info(
                'rpa:action 发送消息/图片',
                shop,
                user,
                uid,
                data.data,
                msgId,
                _i
              );
              this.#ctx.log!.debug('等待时间', (+new Date() - start) / 1000, 's');
              // if (this.#ctx.env.isAssistant) {
              //   this.tab.view.webContents.send('current-user', {user, source: 'reply'});
              //   this.tab.view.webContents.send('user-reply-list', await this.#ctx.messageService?.getReplyList(shop, uid));
              // }
              await _reply(span, _i, uuid);
            },
            {
              type: 'reply',
              shop,
              user,
              uid,
              data: data.data,
              msgId
            }
          );
        }
      });
      i++;
    }
  }

  async transfer_delay(
    user,
    shop,
    msgId,
    {
      backendId,
      isDuplicateMessage,
      transferStack = 0,
      uid,
      userStatus,
      question,
      transferReason
    }: {
      backendId?;
      isDuplicateMessage?;
      transferStack: number;
      uid: number;
      userStatus?: UserStatus;
      question?: { questionContent: string; questionRealAt: string; msgId: string }[];
      transferReason?: string;
    }
  ) {
    const addTransferCount = (args) => {
      transferCount.add(1, {
        mode: import.meta.env.MODE,
        version: packageJson.version,
        instance: this.#ctx.env.clientId,
        // @ts-ignore get exist
        loggedUser: this.config.options.username,
        // env: is.dev ? 'development' : 'production',
        shop: shop,
        uid: uid,
        user: user,
        ...args
      });

      switch (args.reason) {
        case 'success':
        case 'retry-success':
          this.#ctx.chatService?.updateUserStatus(shop, user, uid, true);
          break;
        case 'assistant-mode':
          // no op
          // this.#ctx.chatService?.updateUserStatus(shop, user, uid, false);
          break;
        default:
          this.#ctx.chatService?.updateUserStatus(shop, user, uid, false);
      }
    };

    await tracer.startActiveSpan('transfer-delay', async (span) => {
      const start = +new Date();
      this.#ctx.log!.debug('transfer-delay加入action队列', shop, user, uid, msgId);
      this.#ctx.log!.debug(
        'report:log',
        'action: transfer-delay',
        '等待队列长度',
        this.#actionQueue.size + this.#actionQueue.pending
      );
      this.addToActionQueue(
        async () => {
          this.#ctx.log!.info('start to transfer-delay', shop, user, uid, msgId, transferStack);
          this.#ctx.log!.debug('等待时间', (+new Date() - start) / 1000, 's');
          if (transferStack) {
            return;
          }
          if (this.#ctx.userManager!.get(uid, shop).transfered) {
            this.#ctx.log!.debug(
              '用户已转走，不执行transfer-delay',
              shop,
              user,
              uid,
              msgId,
              backendId
            );
            this.#ctx.chatService?.reply(
              backendId,
              ReplyMessageStatus.UserHasBeenTransfered,
              +new Date()
            );
            return;
          }
          const transfer = this.#ctx.agentConfig.transferDelay;
          const strategy = transfer.strategy;
          const whiteList = transfer.target;

          if (!strategy || !whiteList) {
            this.#ctx.log!.info(
              '未配置转人工策略或者目标-delay',
              shop,
              user,
              uid,
              msgId,
              userStatus,
              strategy,
              whiteList
            );
            addTransferCount({
              reason: 'missing-config'
            });
            this.#ctx.feishu
              ?.send(
                '转接失败',
                `- **报警原因**：日常转接人工失败
- **报警优先级**：🔴高
- **详情**：
  - **错误类型**：${transferReason}
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
              )
              .then((res) => {
                this.#ctx.log!.info('报警结果5', res);
              });
            span.end();
            return;
          }

          const result = await this.#socket.emitWithAck(
            'transfer_v2',
            user,
            shop,
            strategy,
            whiteList
          );
          this.#ctx.log!.info('转人工结果-delay', result, shop, user, uid, msgId);
          let status = 'ok';
          if (result !== 0) {
            status = 'fail';
            this.#ctx.log!.warn('transfer-delay failed', shop, user, uid, result, msgId);
            addTransferCount({
              reason: 'failed'
            });
            const userStatusMapping = {
              preSale: '转接售前失败',
              duringSale: '转接售中失败',
              afterSale: '转接售后失败'
            };
            this.#ctx.feishu
              ?.send(
                '转接失败',
                `- **报警原因**：日常转接人工失败
- **报警优先级**：🔴高
- **详情**：
  - **错误类型**：${transferReason}
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
              )
              .then((res) => {
                this.#ctx.log!.info('报警结果4', res);
              });
          } else {
            // this.#ctx.chatService?.reply(
            //   backendId,
            //   isDuplicateMessage
            //     ? ReplyMessageStatus.DuplicateMessageTransferSuccess
            //     : ReplyMessageStatus.TransferSuccess,
            //   +new Date()
            // );
            this.#ctx.userManager!.get(uid, shop).transfered = true;
            addTransferCount({
              reason: 'success'
            });
          }

          // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, status);
          span.end();
        },
        {
          type: 'transfer',
          shop,
          user,
          uid,
          msgId
        }
      );
    });
  }

  async transfer(
    user,
    shop,
    msgId,
    {
      backendId,
      isDuplicateMessage,
      transferStack = 0,
      uid,
      userStatus,
      question
    }: {
      backendId?;
      isDuplicateMessage?;
      transferStack: number;
      uid: number;
      userStatus?: UserStatus;
      question?: { questionContent: string; questionRealAt: string; msgId: string }[];
    }
  ) {
    const addTransferCount = (args) => {
      transferCount.add(1, {
        mode: import.meta.env.MODE,
        version: packageJson.version,
        instance: this.#ctx.env.clientId,
        // @ts-ignore get exist
        loggedUser: this.config.options.username,
        // env: is.dev ? 'development' : 'production',
        shop: shop,
        uid: uid,
        user: user,
        ...args
      });

      switch (args.reason) {
        case 'success':
        case 'retry-success':
          this.#ctx.chatService?.updateUserStatus(shop, user, uid, true);
          break;
        case 'assistant-mode':
          // no op
          // this.#ctx.chatService?.updateUserStatus(shop, user, uid, false);
          break;
        default:
          this.#ctx.chatService?.updateUserStatus(shop, user, uid, false);
      }
    };

    await tracer.startActiveSpan('transfer', async (span) => {
      const start = +new Date();
      this.#ctx.log!.debug('transfer加入action队列', shop, user, uid, msgId);
      this.#ctx.log!.debug(
        'report:log',
        'action: transfer',
        '等待队列长度',
        this.#actionQueue.size + this.#actionQueue.pending
      );
      this.addToActionQueue(
        async () => {
          this.#ctx.log!.info('start to transfer', shop, user, uid, msgId, transferStack);
          this.#ctx.log!.debug('等待时间', (+new Date() - start) / 1000, 's');
          if (transferStack) {
            return;
          }
          if (this.#ctx.userManager!.get(uid, shop).transfered) {
            this.#ctx.log!.debug('用户已转走，不执行transfer', shop, user, uid, msgId, backendId);
            this.#ctx.chatService?.reply(
              backendId,
              ReplyMessageStatus.UserHasBeenTransfered,
              +new Date()
            );
            return;
          }
          const transfer = this.#ctx.agentConfig.transfer;
          let strategy;
          let whiteList;
          let transferMsg;
          let transferFailedMsg;
          let off;
          let replyMsg;
          const backupStrategy = _.get(transfer, ['backupStrategy']);
          const backupTarget = _.get(transfer, ['backupTarget']);

          if (_.get(transfer, 'divide') && _.get(transfer, userStatus)) {
            strategy = _.get(transfer, [userStatus, 'strategy']);
            whiteList = _.get(transfer, [userStatus, 'target']);
            transferMsg = _.get(transfer, [userStatus, 'transferMsg']);
            transferFailedMsg = _.get(transfer, [userStatus, 'transferFailedMsg']);
            off = _.get(transfer, [userStatus, 'off'], []);
          } else {
            strategy = _.get(transfer, ['strategy']);
            whiteList = _.get(transfer, ['target']);
            transferMsg = _.get(transfer, ['transferMsg']);
            transferFailedMsg = _.get(transfer, ['transferFailedMsg']);
            off = _.get(transfer, ['off'], []);
          }

          off = off.filter((item) => isBetweenPeriod(item.period));

          if (off.length) {
            this.#ctx.log!.info('不执行转人工时段', shop, user, uid, msgId, userStatus, off[0]);
            replyMsg = getRandomStrings(_.get(off, '0.replyMsg'));
            if (!_.isEmpty(replyMsg)) {
              await this.reply(
                user,
                replyMsg.map((msg) => {
                  return {
                    type: 'text',
                    text: [
                      {
                        value: msg
                      }
                    ]
                  };
                }),
                msgId,
                shop,
                backendId,
                transferStack + 1,
                true,
                uid,
                userStatus,
                question
              );
            }

            if (!this.#transferFailedUsers[shop]) {
              this.#transferFailedUsers[shop] = {};
            }
            this.#transferFailedUsers[shop][user] = true;
            addTransferCount({
              reason: 'off-period'
            });
            span.end();
            return;
          }

          transferMsg = getRandomStrings(transferMsg);
          if (this.#ctx.env.isAssistant && _.isEmpty(transferMsg)) {
            transferMsg = ['请转人工'];
          }
          if (!_.isEmpty(transferMsg)) {
            await this.reply(
              user,
              transferMsg.map((msg) => {
                return {
                  type: 'text',
                  text: [
                    {
                      value: msg
                    }
                  ]
                };
              }),
              msgId,
              shop,
              backendId,
              transferStack + 1,
              true,
              uid,
              userStatus,
              question
            );
          }

          if (this.#ctx.env.isAssistant) {
            this.#ctx.log!.info('转人工结束', shop, user, uid, msgId);
            addTransferCount({
              reason: 'assistant-mode'
            });
            span.end();
            return;
          }

          if (!strategy || !whiteList) {
            this.#ctx.log!.info(
              '未配置转人工策略或者目标',
              shop,
              user,
              uid,
              msgId,
              userStatus,
              strategy,
              whiteList
            );
            addTransferCount({
              reason: 'missing-config'
            });
            span.end();
            return;
          }

          const result = await this.#socket.emitWithAck(
            'transfer_v2',
            user,
            shop,
            strategy,
            whiteList
          );
          this.#ctx.log!.info('转人工结果', result, shop, user, uid, msgId);
          let status = 'ok';
          if (result !== 0) {
            status = 'fail';
            this.#ctx.log!.warn('transfer failed', shop, user, uid, result, msgId);
            // let replyStatus = isDuplicateMessage
            //   ? ReplyMessageStatus.DuplicateMessageTransferFailed
            //   : ReplyMessageStatus.TransferFailed;
            // if (result === -20) {
            //   replyStatus = ReplyMessageStatus.UserHasBeenTransfered;
            // }
            // this.#ctx.chatService?.reply(backendId, replyStatus, +new Date());
            if (backupStrategy && backupTarget) {
              const result = await this.#socket.emitWithAck(
                'transfer_v2',
                user,
                shop,
                backupStrategy,
                backupTarget
              );

              if (result === 0) {
                this.#ctx.log!.info('转人工结果 retry', result, shop, user, uid, msgId);
                this.#ctx.userManager!.get(uid, shop).transfered = true;
                addTransferCount({
                  reason: 'retry-success'
                });
                span.end();
                return;
              }
              this.#ctx.log!.warn('transfer retry failed', result, shop, user, uid, msgId);
            }
            transferFailedMsg = getRandomStrings(transferFailedMsg);
            if (!_.isEmpty(transferFailedMsg)) {
              await this.reply(
                user,
                transferFailedMsg.map((msg) => {
                  return {
                    type: 'text',
                    text: [
                      {
                        value: msg
                      }
                    ]
                  };
                }),
                msgId,
                shop,
                backendId,
                transferStack + 1,
                false,
                uid
              );
            } else {
              this.#ctx.log!.warn('transfer_failed_msg not configured', shop, user, uid, msgId);
            }

            if (!this.#transferFailedUsers[shop]) {
              this.#transferFailedUsers[shop] = {};
            }
            this.#transferFailedUsers[shop][user] = true;
            addTransferCount({
              reason: 'failed'
            });
            const userStatusMapping = {
              preSale: '转接售前失败',
              duringSale: '转接售中失败',
              afterSale: '转接售后失败'
            };
            this.#ctx.feishu
              ?.send(
                '转接失败',
                `- **报警原因**：日常转接人工失败
- **报警优先级**：🔴高
- **详情**：
  - **错误类型**：${userStatusMapping[userStatus!] || '转接失败'}
  - **店铺**：${shop}
  - **用户**：${user}
  - **uid**：${uid}
  - **msgId**：${msgId}
  - **backendId**：${backendId}`
              )
              .then((res) => {
                this.#ctx.log!.info('报警结果4', res);
              });
          } else {
            // this.#ctx.chatService?.reply(
            //   backendId,
            //   isDuplicateMessage
            //     ? ReplyMessageStatus.DuplicateMessageTransferSuccess
            //     : ReplyMessageStatus.TransferSuccess,
            //   +new Date()
            // );
            this.#ctx.userManager!.get(uid, shop).transfered = true;
            addTransferCount({
              reason: 'success'
            });
          }

          // this.#ctx.log!.info('rpa:action 回复消息或转人工', user, status);
          span.end();
        },
        {
          type: 'transfer',
          shop,
          user,
          uid,
          msgId
        }
      );
    });
  }

  async transferBatch() {
    this.#ctx.log!.info('开始批量转人工');
    const time = dayjs();
    // const shop = this.#ctx.agentConfig.transferBatchShop;
    const whiteList = this.#ctx.agentConfig.transferGroupWhiteList;

    if (!whiteList || !Array.isArray(whiteList) || whiteList.length === 0) {
      this.#ctx.log!.error('批量转人工失败，没有白名单');
      return;
    }
    this.addToActionQueue(
      async () => {
        const result = await this.#socket.emitWithAck(
          'transfer-batch-v2',
          this.#transferFailedUsers,
          'group',
          whiteList[0]
        );
        this.#ctx.log!.info('批量转人工完成', result);

        const filteredData = _.mapValues(result, (shop) => {
          return _.pickBy(shop, (value) => value !== 0);
        });
        const successData = _.mapValues(result, (shop) => {
          return _.pickBy(shop, (value) => value === 0);
        });
        _.forEach(successData, (users, shopKey) => {
          _.forEach(users, (_, user) => {
            this.#ctx.chatService?.updateUserStatus(shopKey, user, undefined, true);
          });
        });

        let alarm = false;
        const resultString = _.reduce(
          filteredData,
          (acc, users, shopKey) => {
            const userList = _.keys(users).join(', ');
            if (userList) {
              alarm = true;
            }
            acc += `- **店铺**：${shopKey}\n  - **用户**：${userList}\n`;
            return acc;
          },
          ''
        ).trim();

        if (alarm) {
          this.#ctx.feishu
            ?.send(
              '转接失败',
              `- **报警原因**：批量转接人工失败
- **报警优先级**：🔴高
- **详情**：
  ${resultString}
  - **转接失败时间**：${time.format('HH:mm')}`
            )
            .then((res) => {
              this.#ctx.log!.info('报警结果3', res);
            });
        }

        this.#transferFailedUsers = {};
      },
      {
        type: 'transfer-batch'
      }
    );
  }

  registerHotKey() {
    globalShortcut.register('CommandOrControl+Shift+Q', () => {
      if (this.#ctx.env.isSilence) {
        return;
      }

      if (this.#socket.connected) {
        this.#ctx.log!.debug('close socket');
        this.#socket.disconnect();
      } else {
        this.#ctx.log!.debug('connect socket');
        this.#socket.connect();
      }
    });
    globalShortcut.register('CommandOrControl+Shift+R', () => {
      this.#replyPromise.resolve();
    });
  }

  updateStatus() {
    this.#updateStatusInterval = setInterval(() => {
      if (this.#status.ai !== 'initial' && this.#status.qianniu !== 'initial' && this.#shops.size) {
        this.#ctx.log!.info('开始同步客户端状态');
        updateStatus(this.#ctx, this.#shops, this.#status);
      }
    }, ms('1m'));
  }

  // stopBurrow() {
  //   // exec(`taskkill /f /im ${burrow}`, (err, stdout, stderr) => {
  // }

  async stop() {
    await this.#runningPromise.promise;
    globalShortcut.unregister('CommandOrControl+Shift+Q');
    Qianniu.runningCount--;
    User.shopUserCount = {};
    if (this.#socket) {
      this.#socket.close();
    }
    if (this.#server) {
      this.#ctx.log!.info('closing server');
      spawnSync('taskkill', ['/pid', String(this.#server.pid), '/f', '/t']);
    }
    this.sendMessageToOpener('exit', {
      id: this.config.id
    });
    // @ts-ignore get exist
    const username = this.config.options.username;
    // Sentry.captureMessage('qianniu stop', {
    //   tags: {
    //     username
    //   },
    //   level: 'warning'
    // });
    clearInterval(this.#checkQianniuStatusInterval);
    clearInterval(this.#checkBurrowInterval);
    clearInterval(this.#timer);
    clearInterval(this.#checkCSStatusInterval);
    clearInterval(this.#updateStatusInterval);
    this.#offQianniuWindowMonitor?.();
    this.#replyWindow?.activeTab.close();
    this.#detailWindow?.activeTab.close();
    this.#transferBatchJob?.forEach((job) => job.stop());
    this.#syncStatisticsJob?.forEach((job) => job.stop());
    this.#ctx.followUp?.destroy();
    this.#ctx.chatService?.close();
    this.#ctx.grabGoods?.close();
    // this.#page!.close();
    // this.#config = undefined;
    this.#page = undefined;
  }
}

export default Qianniu;
