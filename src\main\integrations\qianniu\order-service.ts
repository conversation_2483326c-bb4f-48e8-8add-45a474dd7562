import BurrowAdapter, { Order } from './burrow-adapter';

class QianniuOrderService {
  #burrowAdapter: BurrowAdapter;

  constructor(private ctx) {
    this.#burrowAdapter = new BurrowAdapter('http://127.0.0.1:22233');
  }

  async getOrders(cs, uid) {
    // const {cs} = extra;
    try {
      const orders = await this.#burrowAdapter.getOrders(cs, uid);

      return orders.map((order) => {
        return {
          orderId: order.bizOrderId,
          // '已关闭-已付款' if order.get('cardTypeText') == '已关闭' and order.get('payTime') else order.get('cardTypeText')
          orderStatus: this.calcStatus(order),
          orderTime: order.createTime,
          goods: order.itemList.map((good) => {
            return {
              id: good.auctionId,
              url: good.auctionUrl,
              name: good.auctionTitle,
              skuName: good.sku,
              skuId: good.skuCode
            };
          })
        };
      });
    } catch (e) {
      this.ctx.log.error('getOrders error', cs, uid, e);
      return [];
    }
  }

  calcStatus(order: Order): string {
    if (order.cardTypeText === '已关闭' && order.payTime) {
      return '已关闭-已付款';
    } else if (order.cardTypeText === '未付款' && order.extInfoItems.length > 0) {
      if (order.extInfoItems[0].value?.includes('已付款')) {
        return '未付尾款';
      } else {
        return order.cardTypeText;
      }
    } else {
      return order.cardTypeText;
    }
  }
}

export default QianniuOrderService;
