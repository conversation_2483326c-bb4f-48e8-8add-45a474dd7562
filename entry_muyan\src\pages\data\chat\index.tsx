import React, { useState, useRef } from 'react';
import { Checkbox } from 'antd';
import type { GetProp } from 'antd';
import ReactECharts from 'echarts-for-react';
const plainOptions = [
  '接待人数',
  'AI回复数',
  '转人工数',
  '独立接待数'
  // "下单数",
  // "直接下单率",
  // "转人工下单率",
];

type Props = {
  data: any[];
  isDay: boolean;
};
export const Chat: React.FC<Props> = ({ data, isDay = false }: Props) => {
  const [value, setValue] = useState<any[]>(['接待人数', 'AI回复数', '转人工数', '独立接待数']);
  const echartRef = useRef<any>(null);
  const options = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['接待人数', 'AI回复数', '转人工数', '独立接待数'],
      show: false
    },
    grid: {
      left: '40px',
      right: '40px',
      bottom: '0%',
      top: '5%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      splitLine: {
        show: true // 显示x轴的竖线
      },
      data: data.map((item) => {
        return isDay ? item.hour : item.bizdate;
      })
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '接待人数',
        type: 'line',
        data: data.map((item) => item.user_cnt),
        smooth: true,
        showSymbol: false,
        color: '#2971FA'
      },
      {
        name: 'AI回复数',
        type: 'line',
        data: data.map((item) => item.send_message_ok_cnt),
        smooth: true,
        showSymbol: false,
        color: '#F7A637'
      },
      {
        name: '转人工数',
        type: 'line',
        data: data.map((item) => item.change_human_user_cnt),
        smooth: true,
        showSymbol: true,
        color: '#44D961'
      },
      {
        name: '独立接待数',
        type: 'line',
        showSymbol: false,
        data: data.map((item) => item.user_cnt - item.change_human_user_cnt),
        smooth: true,
        color: '#FF6060'
      }
      // {
      //   name: "下单数",
      //   type: "line",
      //   stack: "Total",
      //   showSymbol: false,
      //   data: [820, 932, 901, 934, 1290, 1330, 1320],
      //   smooth: true,
      //   color: "#8560AA",
      // },
      // {
      //   name: "直接下单率",
      //   type: "line",
      //   stack: "Total",
      //   showSymbol: false,
      //   data: [80, 93, 91, 94, 120, 130, 120],
      //   smooth: true,
      //   color: "#61CFFF",
      // },
      // {
      //   name: "转人工下单率",
      //   type: "line",
      //   stack: "Total",
      //   showSymbol: false,
      //   data: [82, 93, 90, 93, 129, 133, 132],
      //   smooth: true,
      //   color: "#FAF04F",
      // },
    ]
  };
  const onChange: GetProp<typeof Checkbox.Group, 'onChange'> = (checkedValues: any[]) => {
    setValue(checkedValues);
    if (echartRef.current) {
      const echartsInstance = echartRef.current.getEchartsInstance();
      const allSelected = {};
      plainOptions.forEach((option) => {
        allSelected[option] = checkedValues.includes(option);
      });

      echartsInstance.setOption({
        legend: {
          selected: allSelected
        }
      });
    }
  };

  return (
    <div>
      <div
        style={{
          display: 'flex',
          alignItems: 'center'
        }}
      >
        <div
          style={{
            marginRight: '10px'
          }}
        >
          查看指标:
        </div>
        <Checkbox.Group value={value} options={plainOptions} onChange={onChange} />
      </div>

      <ReactECharts
        ref={echartRef}
        option={options}
        style={{
          width: '100%',
          height: '370px'
        }}
      />
    </div>
  );
};
