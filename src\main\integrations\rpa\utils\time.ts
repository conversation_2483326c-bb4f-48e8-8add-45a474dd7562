import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
dayjs.extend(isBetween);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrAfter);

export function isBetweenPeriod(period: [string, string]) {
  // 左闭右开
  // 全天表示：[00:00, 00:00];
  // const period = period ?? [
  //   "00:00",
  //   "00:00",
  // ];
  const start = dayjs(period[0], 'HH:mm');
  const end = dayjs(period[1], 'HH:mm');

  if (start.isBefore(end)) {
    return dayjs().isBetween(start, end, null, '[)');
  } else {
    return dayjs().isSameOrAfter(start) || dayjs().isBefore(end);
  }
}
