import Modal from '../../../../components/Modal';
import default_ceiling from '../../../../assets/imgs/default_ceiling.png';
import Button from '../../../../components/Button';
import { store } from '../../../../store';
type Props = {
  open: boolean;
  setOpen: (open: boolean) => void;
};
const UpperLimitTip: React.FC<Props> = ({ open, setOpen }: Props) => {
  const { userInfo } = store;
  return (
    <Modal title="温馨提示" open={open} setOpen={setOpen} footer={false} width={600}>
      <div
        style={{
          textAlign: 'center',
          padding: '44px 0px 28px'
        }}
      >
        <img
          src={default_ceiling}
          alt="default"
          style={{
            width: '120px',
            height: '120px'
          }}
        />
        <div
          style={{
            fontSize: '12px',
            color: '#666666'
          }}
        >
          抱歉，添加账户已达上限~
        </div>
        {userInfo.aikfPackageAsset.name !== '企业版' && (
          <Button
            type="primary"
            style={{
              width: '128px',
              height: '36px',
              lineHeight: '36px',
              marginTop: '24px'
            }}
          >
            去升级
          </Button>
        )}
      </div>
    </Modal>
  );
};

export default UpperLimitTip;
