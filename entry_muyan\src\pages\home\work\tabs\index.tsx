import React from 'react';
type Props = {
  value: string;
  accKeys: string[];
  onChange: (value: string) => void;
};

const TabsLine = () => {
  return (
    <div
      style={{
        width: '100%',
        height: '2px',
        backgroundColor: '#2E74FF',
        borderRadius: '2px',
        position: 'absolute',
        bottom: '-4px'
      }}
    ></div>
  );
};

const TabsItem = ({
  accKey,
  name,
  value,
  onChange
}: {
  accKey: string;
  name: string;
  value: string;
  onChange: (val: string) => void;
}) => {
  const itemStyle = {
    fontSize: '16px',
    lineHeight: '24px',
    fontStyle: 'normal',
    color: '#595B60',
    marginRight: '24px',
    cursor: 'pointer'
  };

  const activeStyle = {
    fontWeight: 500,
    color: '#121212'
  };
  return (
    <div
      style={{
        position: 'relative',
        ...itemStyle,
        ...(value === accKey ? activeStyle : {})
      }}
      onClick={() => {
        onChange(accKey);
      }}
    >
      <span>{name}</span>
      {value === accKey && <TabsLine />}
    </div>
  );
};

const Tabs: React.FC<Props> = ({ value, onChange, accKeys }) => {
  const tabs = [
    {
      name: '千牛-天猫',
      value: '6868833650239488'
    },
    {
      name: '抖店',
      value: '6868833650239489'
    },
    {
      name: '千牛-淘宝',
      value: '6868833650239490'
    }
  ];

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center'
      }}
    >
      <div
        style={{
          fontSize: '20px',
          marginRight: '31px',
          lineHeight: '28px',
          color: '#121212',
          fontWeight: '500'
        }}
      >
        智能体
      </div>
      <TabsItem value={value} accKey="all" onChange={onChange} name="全部" />
      {tabs
        .filter((item) => {
          return accKeys.includes(item.value);
        })
        .map((item) => (
          <TabsItem
            key={item.value}
            value={value}
            onChange={onChange}
            name={item.name}
            accKey={item.value}
          />
        ))}
    </div>
  );
};

export default Tabs;
