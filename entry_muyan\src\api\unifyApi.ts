import axios from 'axios';
const baseUrl = 'https://auth.dongchacat.cn';

export const dropOut = () => {
  return axios.get(baseUrl + '/api/v3/flows/executor/default-invalidation-flow/');
};

export const authentication = (data) => {
  return axios.post(baseUrl + '/api/v3/flows/executor/default-authentication-flow/', {
    component: 'ak-stage-identification',
    ...data
  });
};

export const muyuCreateUser = ({ username, password }) => {
  return axios.post(baseUrl + '/api/v3/flows/executor/muyu-create-user/', {
    component: 'ak-stage-prompt',
    username,
    password
  });
};

export const getCreateUser = () => {
  return axios.get(baseUrl + '/api/v3/flows/executor/muyu-create-user/');
};
