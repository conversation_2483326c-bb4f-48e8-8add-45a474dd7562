import { Config } from './type';
import axios from 'axios';

/** 加载阿根廷配置*/
export const loadConfig = async (
  endpoint: string,
  assistantId: string,
  log,
  accessToken?: string
): Promise<Config | undefined> => {
  const url = `${endpoint}/workbench/agentium?assistant_id=${assistantId}&page_size=1`;
  log.info('loadConfig url', url);

  const res = await axios.get<{
    code: number;
    msg: string;
    data: {
      data: Array<Config>;
    };
  }>(url, {
    headers: {
      'Cache-Control': 'no-cache',
      Authorization: accessToken ? `Bearer ${accessToken}` : ''
    },
    responseType: 'json',
    timeout: 5 * 1000
  });

  return res.data.data.data[0];
};
