{"clarify_mode": "追问模式", "clarify_mode_tip": "当出现重复询问消息或者未召回时，开启该模式会进行一次追问", "history_limit": "上下文个数", "history_limit_tip": "生成回答时参考的历史上下文消息数", "recall_vs_configs": "不同知识库粗召回配置", "recall_vs_configs_tip": "", "recall_vs_configs.xxx": "知识库名称", "recall_vs_configs.xxx.topk": "粗召回数量", "recall_vs_configs.xxx.topk_tip": "从知识库中召回与当前消息意图最相关的知识的个数", "preset_configs": "预置话术配置", "preset_configs_tip": "", "preset_configs.cls_model_threshold": "分类模型阈值", "preset_configs.cls_model_threshold_tip": "预置分类模型生成标签的置信度大于该阈值时被认为有效", "preset_configs.preset_llm_args": "预置大模型回答参数", "preset_configs.preset_llm_args_tip": "使用大模型生成预置话术时的大模型参数", "preset_configs.preset_llm_args.temperature": "温度参数", "preset_configs.preset_llm_args.temperature_tip": "", "preset_configs.preset_label_gen": "预置标签生成配置", "preset_configs.preset_label_gen_tip": "预置话术依赖于不同规则生成标签，包括分类模型、meta信息匹配，消息正则匹配", "preset_configs.preset_label_gen.cls_models": "分类模型", "preset_configs.preset_label_gen.cls_models_tip": "用于生产标签的模型名称，当前仅提供multiturn_cls和kedang_transfer两个模型，multiturn_cls可生成标签包括：['manual', 'require_detail', 'retrival', 'greeting', 'client-thx','client-ok'],kedang_transfer可生成标签包括：['rag', 'transfer']", "preset_configs.preset_label_gen.meta_matches": "meta匹配配置", "preset_configs.preset_label_gen.meta_matches_tip": "", "preset_configs.preset_label_gen.meta_matches.tool": "meta所属tool", "preset_configs.preset_label_gen.meta_matches.tool_tip": "会提取该tool生产的数据进行规则匹配（目前仅支持布尔条件判断）", "preset_configs.preset_label_gen.meta_matches.match": "匹配数据", "preset_configs.preset_label_gen.meta_matches.match_tip": "提取以下key的数据", "preset_configs.preset_label_gen.meta_matches.label": "生成标签", "preset_configs.preset_label_gen.meta_matches.label_tip": "metadata满足该规则时，生成的标签名", "preset_configs.preset_label_gen.query_reg_matches": "消息正则匹配规则", "preset_configs.preset_label_gen.query_reg_matches_tip": "", "preset_configs.preset_label_gen.query_reg_matches.regex": "正则表达式", "preset_configs.preset_label_gen.query_reg_matches.regex_tip": "消息匹配的正则表达式规则", "preset_configs.preset_label_gen.query_reg_matches.label": "生成标签", "preset_configs.preset_label_gen.query_reg_matches.label_tip": "满足该正则规则时，生成的标签名", "preset_configs.preset_label_resp": "预置标签回答配置", "preset_configs.preset_label_resp_tip": "", "preset_configs.preset_label_resp.子对象名": "标签", "preset_configs.preset_label_resp.子对象名_tip": "该标签对应的回答配置", "preset_configs.preset_label_resp.xxx.method": "回答方式", "preset_configs.preset_label_resp.xxx.method_tip": "支持两种回答text/llm，text会直接返回对应的prompt值，llm会基于prompt使用大模型生成回答。", "preset_configs.preset_label_resp.xxx.prompt": "回答内容", "preset_configs.preset_label_resp.xxx.prompt_tip": "根据回答方式选择回复内容，text会直接返回对应的prompt值，llm会基于prompt使用大模型生成回答。", "shop_configs": "店铺配置", "shop_configs_tip": "", "shop_configs.shop_id": "店铺id", "shop_configs.shop_id_tip": "该id会关联后台对应的文件配置实例，包括items,titles,activities。", "shop_configs.shop_name": "店铺名称", "shop_configs.shop_name_tip": "店铺名称配置，店铺名称将在prompt模版中作为模版变量值进行替换", "shop_configs.shop_desc": "店铺描述", "shop_configs.shop_desc_tip": "店铺描述，店铺描述将在prompt模版中作为模版变量值进行替换", "shop_configs.products": "店铺出售商品", "shop_configs.products_tip": "店铺出售商品将在prompt模版中作为模版变量值进行替换", "shop_configs.item_url_replace_by": "商品url替换规则", "shop_configs.item_url_replace_by_tip": "url消息识别到对应item后的替换规则，可配置为title/empty/any，设置为title时，会替换为items文件中对应的商品名称，若为empty，则会替换为空字符串，否则替换为设置的任何值", "shop_configs.platform": "电商平台配置", "shop_configs.platform_tip": "配置商品url识别的平台，包括tmall/pdd/jd", "shop_configs.items": "商品信息", "shop_configs.items_tip": "商品信息对应文件，后续将在数据库中配置", "shop_configs.instruction": "指令instruction", "shop_configs.instruction_tip": "向量库召回指令，该属性将逐步弃用", "shop_configs.embedding_instruction": "指令embedding_instruction", "shop_configs.embedding_instruction_tip": "向量库召回指令", "shop_configs.titles": "商品标题", "shop_configs.titles_tip": "商品id与商品标题映射表，提供商品别名配置", "shop_configs.activities": "活动话术", "shop_configs.activities_tip": "商品活动话术文件", "shop_configs.stream_mode": "streaming模式", "shop_configs.stream_mode_tip": "启用该模式时，回答内容将使用流输出，此时不会进行重复检查和改写。", "shop_configs.repeat_rewrite": "重复回答改写prompt", "shop_configs.repeat_rewrite_tip": "当stream_mode==false时，会对大模型回答进行重复回答检测，若大模型生成了和之前相同的回答，将使用该prompt进行重写", "shop_configs.role_prompts": "角色提示", "shop_configs.role_prompts_tip": "大模型生成回答时，上下文中的第一条角色提示", "shop_configs.shop_prompt_templates": "店铺回答提示", "shop_configs.shop_prompt_templates_tip": "当顾客没有询问相关商品时，会使用该提示生成回答", "shop_configs.product_prompt_templates": "商品回答提示", "shop_configs.product_prompt_templates_tip": "当顾客询问了具体商品时，会使用该提示生成回答", "shop_configs.iq_threshold": "IUR召回Query阈值", "shop_configs.iq_threshold_tip": "使用iur从向量库匹配问法阈值", "shop_configs.qq_threshold": "消息召回Query阈值", "shop_configs.qq_threshold_tip": "使用原始消息从向量库匹配问法阈值", "shop_configs.qa_threshold": "IUR召回Anser阈值", "shop_configs.qa_threshold_tip": "使用iur从知识库匹配回答阈值", "shop_configs.finegrain_threshold": "精排阈值", "shop_configs.finegrain_threshold_tip": "精排后知识过滤阈值", "assist_configs": "助手配置", "assist_configs_tip": "", "assist_configs.nick": "昵称", "assist_configs.nick_tip": "助手的昵称，将在提示模版中被替换", "assist_configs.role": "角色", "assist_configs.role_tip": "助手的角色，将在提示模版中被替换", "debug": "是否开启debug模式", "debug_tip": "debug模式会输出更多的日志，建议关闭", "keyword_replacement.forbiddenWord": "违禁词", "keyword_replacement.substituteWord": "替换词", "emoji.src": "emoji地址", "emoji.name": "emoji名称", "transfer_rate": "当前未回复数小于该值才转接", "transfer_black_list": "转接黑名单", "transfer_black_list_tip": "在该名单的都不会转接", "transfer_blacklist": "转接黑名单", "transfer_blacklist_tip": "在该名单的都不会转接", "greeting": "售前流程的欢迎语", "transer_chat": "转接客服时的提示文案", "press_interval": "催付等待时间", "press_payment_list": "催付话术列表", "keyword_replacement": "敏感词替换", "myses_indexs": "指标", "myses_indexs.dimension": "维度", "myses_indexs.index_name": "名称", "myses_indexs.scene": "场景", "myses_indexs.definition": "定义", "preset_configs.preset_label_gen.user_msg_meta_matches": "信息处理", "preset_configs.preset_label_gen.user_msg_meta_matches.query_match": "匹配", "preset_configs.preset_label_gen.user_msg_meta_matches.key_match": "键", "preset_configs.preset_label_gen.user_msg_meta_matches.value_match": "值", "preset_configs.preset_label_gen.user_msg_meta_matches.mode": "模式", "preset_configs.preset_label_gen.user_msg_meta_matches.label": "标签", "preset_configs.unsupport_region": "不支持地区", "preset_configs.unsupport_user_age": "不支持用户年龄", "preset_configs.user_info_check_label": "用户信息检查标签", "shop_configs.api_url": "店铺api地址", "shop_configs.finegrain_recall_topk": "精排召回的Top K值", "shop_configs.rerank_weight": "重排序权重", "shop_configs.qa_a_sep": "回答分隔符", "shop_configs.qa_q_sep": "问题分隔符", "shop_configs.cls_model_selection": "启用分类模型", "shop_configs.reason_mode": "推理模式", "shop_configs.item_info_config_type": "商品信息配置类型", "shop_configs.enable_orignal_answer": "启用原始回答", "shop_configs.qq_recall_min_query_len": "问题召回最小查询长度", "shop_configs.proc_check_tags": "处理检查标签", "shop_configs.proc_tag_exist_threshold": "处理标签存在阈值", "shop_configs.normal_llm_gen_args.temperature": "LLM生成参数温度", "shop_configs.normal_llm_gen_args.top_p": "LLM生成参数采样概率", "shop_configs.normal_llm_gen_args.presence_penalty": "LLM生成参数出现惩罚", "shop_configs.normal_llm_gen_args.frequency_penalty": "LLM生成参数频率惩罚", "shop_configs.normal_llm_gen_args.max_tokens": "LLM生成参数最大令牌数", "shop_configs.normal_llm_gen_args.n": "LLM生成参数生成数量", "shop_configs.reason_prompt_template": "推理提示", "shop_configs.reason_and_answer_prompt": "回答提示", "assist_configs.llm_topk": "使用模型回答的Top K数量"}