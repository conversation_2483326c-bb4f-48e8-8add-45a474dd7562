/* eslint-disable @typescript-eslint/no-explicit-any */
import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios';
import { message } from 'antd';
import { AxiosCanceler } from './axiosCancel';
import { showStatus } from './axiosUse';
const axiosCanceler = new AxiosCanceler();

const service: AxiosInstance = axios.create({
  // 默认地址请求地址，可在 .env 开头文件中修改
  baseURL: import.meta.env.gateway + '/api/v2',
  // 设置超时时间（60s）
  timeout: 6000000,
  // 跨域时候允许携带凭证
  withCredentials: true
});
service.interceptors.request.use(
  (config: any) => {
    // 将当前请求添加到 pending 中
    axiosCanceler.addPending(config);
    const org = localStorage.getItem('organization');
    const isCCookie = localStorage.getItem('user') && localStorage.getItem('secret_key');
    return {
      ...config,
      headers: {
        // Authorization: `Bearer ${localStorage.getItem("secret_key")}`,
        'Openai-Organization': org,
        CCookie: isCCookie
          ? `user=${encodeURIComponent(localStorage.getItem('user') ?? '{}')};secret_key=${localStorage.getItem('secret_key')}`
          : null,
        ...config.headers
      }
    };
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const { data, config, status } = response;
    if (status < 200 || status >= 300) {
      // 处理http错误，抛到业务代码
      const msg = showStatus(status);
      if (typeof response.data === 'string') {
        response.data = { msg };
      } else {
        response.data.msg = msg;
      }
      message.error(msg);
      return Promise.reject(data);
    }
    // 全局错误信息拦截（防止下载文件得时候返回数据流，没有code，直接报错）
    if (data.code && data.code !== 200) {
      message.error(data.msg);
      return Promise.reject(data);
    }
    // 在请求结束后，移除本次请求(关闭loading)
    axiosCanceler.removePending(config);
    return data;
  },
  (error: AxiosError) => {
    const { status } = error.response || {};
    if (status && (status < 200 || status >= 300)) {
      // 处理http错误，抛到业务代码
      const msg = showStatus(status);
      message.error(msg);
    }
    // 请求超时单独判断，请求超时没有 response
    if (error.message.indexOf('timeout') !== -1) message.error('请求超时，请稍后再试');
    return Promise.reject(error);
  }
);

export default service;
