import Modal from '@/components/Modal';
import React, { useState } from 'react';
import { Form, Input, Select, message } from 'antd';
import style from './index.module.css';
import { addUser } from '@/api/myqaApi';
type Props = {
  open: boolean;
  setOpen: (bool: boolean) => void;
  onSubmit: () => void;
  roleList: Array<[]>;
};

const { Option } = Select;
const AddAccount: React.FC<Props> = ({ open, setOpen, onSubmit, roleList }: Props) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const onFinish = (values): void => {
    setLoading(true);
    addUser(values)
      .then(() => {
        message.success('新增成功');
        setLoading(false);
        onSubmit();
        resetFields();
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const resetFields = () => {
    setOpen(false);
    form.resetFields();
  };
  return (
    <Modal
      open={open}
      setOpen={setOpen}
      width={480}
      title="创建账户"
      okText="确认创建"
      onOk={() => form.submit()}
      okLoading={loading}
      onCancel={() => {
        resetFields();
      }}
    >
      <div
        style={{
          width: '432px',
          margin: '24px auto'
        }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} form={form} onFinish={onFinish}>
          <Form.Item
            name="role_code"
            label="账号角色"
            rules={[{ required: true, message: '请选择角色' }]}
          >
            <Select placeholder="请选择角色" allowClear>
              {roleList.map((item: any) => {
                return (
                  <Option key={item.value} value={item.value}>
                    {item.name}
                  </Option>
                );
              })}
            </Select>
          </Form.Item>
          <Form.Item
            name="username"
            label="账号"
            rules={[
              { required: true, message: '请输入账号名称!' },
              {
                min: 4,
                message: '账号名称不能少于4个字符'
              },
              {
                max: 20,
                message: '账号名称不能大于20个字符'
              }
            ]}
          >
            <Input
              className={style.input}
              placeholder="请输入账号名称"
              style={{ width: '100%', marginRight: 0 }}
            />
          </Form.Item>
          <Form.Item
            name="password"
            label="账号密码："
            rules={[
              {
                required: true,
                pattern: /^[A-Za-z0-9]{6,64}$/,
                message: '请输入6-64位数字字母组合密码'
              }
            ]}
          >
            <Input.Password
              className={style.input}
              placeholder="请输入账号密码"
              style={{ width: '100%', marginRight: 0 }}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default AddAccount;
