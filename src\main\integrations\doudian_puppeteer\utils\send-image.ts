import path from 'path';
import fs from 'fs';
import fetch from 'node-fetch';
import { v4 as uuidv4 } from 'uuid';

/**
 * 异步函数，用于发送图片到指定的页面
 * @param page 页面对象，用于操作目标网页
 * @param imageUrl 图片URL，用于指定要发送的图片
 */
export const sendImage = async (page, imageUrl, log) => {
  // 使用node-fetch获取图片数据
  const response = await fetch(imageUrl);
  // 将图片数据转换为Buffer
  const buffer = await response.buffer();
  // 解析文件路径，并指定文件名
  const filePath = path.resolve(__dirname, `${uuidv4}.jpg`);
  // 将Buffer数据写入文件系统
  fs.writeFileSync(filePath, buffer);

  // 模拟文件上传
  const inputFile = await page.$("input[type='file']");
  await inputFile.uploadFile(filePath);

  // 触发change事件
  await page.evaluate(async () => {
    setTimeout(() => {
      const button: HTMLDivElement | null = document.querySelector(
        "div[data-qa-id='qa-send-img-popup-submit']"
      );

      if (button) {
        button.click();
      } else {
        log.debug('没有找到发送图片按钮');
      }
    }, 1000);
    const event = new Event('change', { bubbles: true });
    await inputFile.dispatchEvent(event);
  });

  // 删除临时文件
  fs.unlinkSync(filePath);
};
