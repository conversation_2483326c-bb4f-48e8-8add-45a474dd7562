import { app, dialog } from 'electron';
import { autoUpdater } from 'electron-updater';
// import log from "electron-log";
import { log } from './log';
import { coerce } from 'semver';
import { store } from './store';

autoUpdater.logger = log;

function updateHandle() {
  autoUpdater.autoDownload = false;
  autoUpdater.allowDowngrade = true;
  autoUpdater.channel = store.get('channel') ?? 'latest';

  autoUpdater.on('error', (error) => {
    log.error('update error', error);
    // dialog.showErrorBox(
    //   "Error: ",
    //   error == null ? "unknown" : (error.stack || error).toString(),
    // );
  });

  autoUpdater.on('checking-for-update', () => {
    log.info('Checking for update');
  });

  autoUpdater.on('update-available', (info) => {
    const { version: v } = info;
    if (autoUpdater.channel === 'alpha' && coerce(v)?.version === v) {
      return;
    }

    log.info('Got a new client version, will auto download it', info);
    autoUpdater.downloadUpdate();
  });

  autoUpdater.on('update-not-available', (info) => {
    log.info('Current version is up-to-date', info);
  });

  autoUpdater.on('update-downloaded', (info) => {
    log.info('new version', info);
    dialog
      .showMessageBox({
        type: 'info',
        title: '软件升级',
        message: `发现新版本${info.version}，是否立即升级？`,
        buttons: ['是的', '稍后']
      })
      .then((resp) => {
        if (resp.response === 0) {
          log.info('begin to install new version ...');
          autoUpdater.quitAndInstall();
        }
      });
  });

  autoUpdater.on('download-progress', function (progressObj) {
    log.debug('download progress', progressObj);
  });

  autoUpdater.checkForUpdates();
}

log.info('browser version', app.getVersion(), 'channel', autoUpdater.channel);

if (process.platform === 'win32') {
  updateHandle();
}

// export { updateHandle };
