/* eslint-disable @typescript-eslint/no-explicit-any */
export const getJsonData = (val: string): any => {
  const data = JSON.parse(val);
  if (data?.keyword_replacement) {
    data.keyword_replacement = Object.keys(data.keyword_replacement).map((item) => {
      return {
        forbiddenWord: item,
        substituteWord: data.keyword_replacement[item]
      };
    });
    delete data.secret_key;
    delete data.assistant_id;
  }
  return {
    transfer_rate: data.transfer_rate,
    transfer_blacklist: data.transfer_blacklist,
    greeting: data.greeting,
    transer_chat: data.transer_chat,
    press_interval: data.press_interval,
    press_payment_list: data.press_payment_list,
    keyword_replacement: data.keyword_replacement
  };
};
