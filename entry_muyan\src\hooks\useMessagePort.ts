import { useEffect, useState } from 'react';

function useMessagePort() {
  const [ports, setPorts] = useState<{
    [key: string]: { postMessage: <T>(data: T) => void; onmessage: (event: any) => void };
  }>({});

  useEffect(() => {
    window.onmessage = (event) => {
      if (event.source === window && event.data === 'port') {
        const [port] = event.ports;
        port.onmessage = (event) => {
          if (event.data?.handshake) {
            setPorts((ports) => {
              return {
                [event.data.source]: port,
                ...ports
              };
            });
          }
        };
      }
    };
    return () => {
      window.onmessage = null;
    };
  }, []);
  function postMessage(data: any, target: string = 'all') {
    if (target === 'all') {
      Object.values(ports).forEach((item) => {
        item.postMessage({
          source: 'home',
          data
        });
      });
    } else {
      if (ports[target]) {
        ports[target].postMessage({
          source: 'home',
          data
        });
      }
    }
  }

  function onmessage(callback: (data: any) => void) {
    Object.values(ports).forEach((item) => {
      item.onmessage = ({ data }) => {
        callback(data);
      };
    });
  }
  return {
    ports,
    postMessage,
    onmessage
  };
}

export default useMessagePort;
