{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/common/env.d.ts",
    "src/renderer/**/*",
    "src/renderer/**/*.tsx",
    "src/preload/*.d.ts"
  ],
  "compilerOptions": {
    "composite": true,
    "jsx": "react-jsx",
    "baseUrl": ".",
    "paths": {
      "@renderer/*": [
        "src/renderer/src/*"
      ]
    },
    "noUnusedLocals": false,  // 禁用未使用局部变量的检查
    "noUnusedParameters": false,  // 禁用未使用参数的检查
  }
}
