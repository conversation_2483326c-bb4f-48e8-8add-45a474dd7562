appId: works.muyu.muyan
productName: "牧言 智能客服"
directories:
  buildResources: build_muyan
files:
  - "!**/.vscode/*"
  - "!src/*"
  - "!electron.vite.config.{js,ts,mjs,cjs}"
  - "!{.eslintignore,.eslintrc.cjs,.prettierignore,.prettierrc.yaml,dev-app-update.yml,CHANGELOG.md,README.md}"
  - "!{.env,.env.*,.npmrc,pnpm-lock.yaml}"
  - "!{tsconfig.json,tsconfig.node.json,tsconfig.web.json}"
  - "!test/*"
  - "!*.yml"
  - "!build*/*"
asarUnpack:
  - resources/**
win:
  target:
    - target: nsis
      arch:
        - x64
#   executableName: agent-client-v2
nsis:
  artifactName: ${name}-${version}-setup.${ext}
  shortcutName: ${productName}
  uninstallDisplayName: ${productName}
  createDesktopShortcut: always
mac:
  entitlementsInherit: build_muyan/entitlements.mac.plist
  extendInfo:
#    - NSCameraUsageDescription: Application requests access to the device's camera.
#    - NSMicrophoneUsageDescription: Application requests access to the device's microphone.
#    - NSDocumentsFolderUsageDescription: Application requests access to the user's Documents folder.
#    - NSDownloadsFolderUsageDescription: Application requests access to the user's Downloads folder.
  notarize: false
  target:
    - target: dmg
#      arch:
#        - universal
dmg:
  artifactName: ${productName}-${version}-${arch}.${ext}
linux:
  target:
    - AppImage
    - snap
    - deb
  maintainer: electronjs.org
  category: Utility
# appImage:
#   artifactName: ${name}-${version}.${ext}
npmRebuild: false
publish:
  - provider: generic
    url: "https://muyan-package-**********.cos.ap-shanghai.myqcloud.com/download/muyan/${channel}/${os}/"
  - provider: custom
    providerName: cos
    path: /download/muyan/${channel}/${os}/
    bucket: muyan-package-**********
    region: ap-shanghai
    secretId: AKIDNgw8ITZecRMUmFqSsYQEplleVFB798zf
    secretKey: gIoEmNxjV9kKABNQJC1k1WDrMmOwLuRz
    channel: ${channel}
  # - provider: s3
  #   bucket: rpa
  #   endpoint: http://*************:9100
  #   accelerate: false
  #   path: "/haofubao-test/${version}/${os}"
electronDownload:
  mirror: https://npmmirror.com/mirrors/electron/
generateUpdatesFilesForAllChannels: false
extraMetadata:
  name: muyan
