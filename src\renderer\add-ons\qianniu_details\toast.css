body {
  margin: 0;
  font-family: 'PingFang SC', 'Microsoft YaHei';
}

h1 {
  margin: 0;
}

.title {
  border-bottom: 1px solid #E5E5E5;
  padding: 8px 12px;
  display: flex;
  font-size: 12px;
  justify-content: space-between;
  position: fixed;
  width: calc(100% - 24px);
  background: #fff;
  z-index: 999;
}

.title h1 {
  font-size: 12px;
  font-weight: 550;
}

.title .user {
  color: #595B60;
}

.title .collapse {
  color: #2E74FF;
}

.list {
  /*padding: 0 12px 8px;*/
  position: fixed;
  top: 33.5px;
  height: calc(100% - 33.5px - 16px);
  width: 100%;
  margin-top: 8px;
  /* overflow-y: scroll; */
}

.block:first-child {
  margin-top: 0;
}
.block {
  border: 1px solid #DADADA;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  line-height: 20px;
  margin-top: 8px;
}

/* .block:last-child {
  margin-bottom: 8px;
} */

.block .left {
  color: #7C7C7C;
  margin-right: 4px;
  flex: none;
}

.block .right {
  color: #121212;
  padding: 0 4px;
}

.block.selected {
  border: 1px solid #2E74FF;
}

.block .answer {
  display: flex;
  position: relative;
}

.block .answer .right {
  background: rgba(18, 18, 18, 0.04);
  border-radius: 4px;
  flex: 1;
  padding-right: 4px;
  font-weight: 550;
  display: inline-flex;
  word-break: break-all;
}

.block .answer .right .content-wrapper {
  display: flex;
  flex-direction: column;
}

.block .answer .right .content-wrapper p {
  margin: 0;
  display: inline-flex;
}

.block .answer .right .preview {
  width: 160px;
  border: 1px solid #DADADA;
  border-radius: 8px;
  /*height: 160px;*/
}

.block.selected .answer .right {
  background: rgba(46, 116, 255, 0.08);
}

.block .answer .copy {
  /*position: absolute;*/
  /*right: 4.5px;*/
  /*top: 3.5px;*/
  cursor: pointer;
  margin-left: 4px;
  /*float: right;*/
}

.block .answer .copy img {
  vertical-align: sub;
}

.block .questions {
  display: flex;
  margin-top: 8px;
}

.block .questions .question:not(:first-child) {
  margin-top: 4px;
}

.block .questions .content {
  word-break: break-all;
}

.block .questions .content .preview {
  width: 160px;
  border: 1px solid #DADADA;
  border-radius: 8px;
}

.block .questions .time {
  color: #ABABAB
}

.empty {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.empty .empty-text {
  color: #595B60;
  font-size: 14px;
  margin: 12px 0;
}

.simplebar-scrollbar::before {
  background-color: #ababab !important;
}
