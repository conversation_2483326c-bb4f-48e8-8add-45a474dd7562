import React from 'react';
import icon_shop from '@/assets/imgs/icon_shop.png';
import { store } from '@/store';
import dayjs from 'dayjs';
const TipHeader: React.FC = () => {
  const { userInfo } = store;
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        width: '300px',
        height: '40px',
        marginBottom: '15px'
      }}
    >
      <img
        src={icon_shop}
        alt="icon_shop"
        style={{ width: '40px', height: '40px', marginRight: '6px' }}
      />
      <div>
        <div
          style={{
            display: 'flex',
            alignItems: 'center'
          }}
        >
          <div
            style={{
              fontWeight: 'bold',
              fontSize: '14px',
              color: '#333333',
              lineHeight: '14px'
            }}
          >
            {JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? '-'}
          </div>
          <div
            style={{
              width: '36px',
              height: '14px',
              textAlign: 'center',
              lineHeight: '14px',
              backgroundColor: '#F63030',
              borderRadius: '8px',
              fontSize: '8px',
              color: '#FFFFFF',
              marginLeft: '6px'
            }}
          >
            {userInfo.aikfPackageAsset?.name ?? '-'}
          </div>
        </div>
        <div
          style={{
            fontSize: '12px',
            color: '#666666',
            marginTop: '2px'
          }}
        >
          到期时间：
          {userInfo.aikfPackageAsset?.packageEndTime
            ? dayjs(userInfo.aikfPackageAsset?.packageEndTime).format('YYYY年MM月DD日')
            : '-'}
        </div>
      </div>
    </div>
  );
};

export default TipHeader;
