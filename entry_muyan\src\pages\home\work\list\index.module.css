.list {
  margin-top: 16px;
  width: 100%;
}

.item_case {
  width: 100%;
  height: 124px;
  background-color: #ffffff;
  border-radius: 8px;
}

.item_case_header {
  height: 83px;
  display: flex;
  align-items: center;
  padding: 0px 16px;
}

.item_case_bottom {
  border-top: 1px solid rgba(18, 18, 18, 0.1);
  height: 40px;
  display: flex;
  align-items: center;
  color: "#595B60";
  font-size: 14px;
  text-align: center;
}

.item_case_bottom_button {
  width: 100%;
}

.item_case_label {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.item_case_label_avatar {
  position: relative;
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  margin-right: 12px;
}

.item_name_editor {
  :global .ant-input-group >.ant-input:first-child {
      border-start-end-radius: 0 !important;
      border-end-end-radius: 0 !important;
  }
  /*:global .ant-input-group-addon {*/
  /*  cursor: pointer;*/
  /*}*/
}

.name {
  width: 100%;
  font-weight: 400;
  font-size: 14px;
  line-height: 24px;
  color: #121212;
  text-align: left;
  font-style: normal;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.state {
  width: 60px;
  font-weight: 400;
  font-size: 14px;
  line-height: 22px;
  position: relative;
  margin-top: 2px;
  margin-left: 12px;
}

.status_online,
.state_success {
  color: #44d961;
}

.state_success::before {
  background-color: #44d961;
}

.status_offline,
.state_out {
  color: #999999;
}

.state_out::before {
  background-color: #999999;
}

.state_success::before,
.state_out::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  left: -12px;
  top: 7px;
}

.menu_item {
  text-align: center;
  width: 136px;
  height: 28px;
}

.menu_item:hover {
  background-color: #d6e0f4;
}

.status {
  font-size: 10px;
  line-height: 17px;
  margin-right: 6px;
}

.status_busy {
  color: #eb534c;
}

.editor_footer {
  padding: 0 12px 12px !important;
}
