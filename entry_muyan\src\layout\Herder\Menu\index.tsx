import icon_question from '@/assets/icons/icon_question.svg';
import style from './index.module.css';
import { Dropdown, message, Spin, Button, Tooltip } from 'antd';
import { useNavigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import 'github-markdown-css/github-markdown.css';
import { VersionModal } from './VersionModal';
import { observer } from 'mobx-react-lite';
import AccountItem from './AccountItem';
import AddAccount from './AddAccount';
import cloneDeep from 'lodash-es/cloneDeep';
import { getAssistants, userLogin } from '../../../api/myqaApi';
import { UpdatePassword } from './UpdatePassword';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
const Item = ({ onClick, text }: { onClick: () => void; text: string }): React.ReactElement => {
  return (
    <div
      style={{
        width: '86px',
        height: '24px',
        lineHeight: '24px',
        textAlign: 'center',
        color: '#333333'
      }}
      onClick={onClick}
    >
      {text}
    </div>
  );
};

const Menu = observer((): React.ReactElement => {
  const [versionOpen, setVersionOpen] = useState(false);
  const [open, setOpen] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [items, setItems] = useState([
    {
      key: '4',
      label: (
        <span
          style={{
            padding: '2px 0px 2px 15px',
            color: '#333333'
          }}
        >
          切换账号
        </span>
      ),
      children: []
    },
    {
      key: '1',
      label: (
        <Item
          onClick={() => {
            setIsModalOpen(true);
          }}
          text="修改密码"
        />
      )
    },
    {
      key: '2',
      label: (
        <Item
          onClick={() => {
            setVersionOpen(true);
          }}
          text="关于我们"
        />
      )
    },
    {
      key: '3',
      label: <Item text="退出登录" onClick={() => logout()} />
    }
  ]);

  useEffect(() => {
    setAccount();
  }, [localStorage.getItem('accountData')]);
  const navigate = useNavigate();
  const logout = (): void => {
    window.xBrowser?.send('TABS_DEl_All', {});
    window.xBrowser?.send('user-login', undefined);
    localStorage.removeItem('user');
    localStorage.removeItem('secret_key');
    localStorage.removeItem('assistantId');
    localStorage.removeItem('organization');
    localStorage.removeItem('kedang_token');
    // closetabs();
    localStorage.removeItem('configStates');
    localStorage.removeItem('phone');
    localStorage.removeItem('username');
    localStorage.removeItem('accessToken');
    localStorage.removeItem('roleCode');

    navigate('/login');
  };

  const [loginLock, setLoginLock] = useState(false);
  const switchLogin = async (values) => {
    window.xBrowser?.send('TABS_DEl_All', {});
    try {
      setLoginLock(true);
      const loginRes = await userLogin(values);
      const userData = loginRes.data.user;
      const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
      localStorage.setItem(
        'accountData',
        JSON.stringify([
          ...accountData.filter((item) => {
            return item.username !== values.username;
          }),
          values
        ])
      );
      localStorage.setItem('accessToken', loginRes.data.tokens.accessToken);

      localStorage.setItem('organization', userData.org_id);

      localStorage.setItem(
        'user',
        JSON.stringify({
          username: userData.user_name,
          id: userData.user_id,
          ...userData
        })
      );
      setLoginLock(false);

      message.success('登录成功');
      window.xBrowser?.send('user-login', values.username);
      navigate('/home');
      window.location.reload();
    } catch (error) {
      setLoginLock(false);
    }

    // try {
    //   setLoginLock(true);

    //   await dropOut();
    //   await authentication({ uid_field: item.username });
    //   const res = await authentication({ password: item.password });
    //   if (res.data.response_errors) {
    //     message.error("账号或密码错误！");
    //     let accountData = JSON.parse(
    //       localStorage.getItem("accountData") ?? "[]",
    //     );
    //     accountData = accountData.filter((account) => {
    //       return account.username !== item.username;
    //     });
    //     localStorage.setItem(
    //       "accountData",
    //       JSON.stringify([
    //         ...accountData.filter((account) => {
    //           return account.username !== item.username;
    //         }),
    //       ]),
    //     );
    //     setLoginLock(false);
    //     return;
    //   }
    //   await myqaLogin({
    //     username: item.username,
    //     password: item.password,
    //   });
    // } catch (error) {
    //   setLoginLock(false);
    //   message.error("账号或密码错误！");
    //   let accountData = JSON.parse(localStorage.getItem("accountData") ?? "[]");
    //   accountData = accountData.filter((account) => {
    //     return account.username !== item.username;
    //   });
    //   localStorage.setItem(
    //     "accountData",
    //     JSON.stringify([
    //       ...accountData.filter((account) => {
    //         return account.username !== item.username;
    //       }),
    //     ]),
    //   );
    // }
  };

  // const myqaLogin = ({ username, password }): Promise<any> => {
  //   return loginOidc().then(async (res) => {
  //     try {
  //       const d = new URL(res.url);
  //       // urt地址中的参数不影响调用结果
  //       const flowsRes = await axios.get(
  //         `https://auth.dongchacat.cn/api/v3/flows/executor/default-provider-authorization-implicit-consent/?query=response_type%3Dcode%26client_id%3D5DJM8fhLTkJCQltIq1Vhpv44PvShTrI6Y66g7N52%26redirect_uri%3Dhttps%253A%252F%252Faikf.tuzhou.net%253A2001%252Fapi%252Fv1%252Fauth%252Foidc%26scope%3Dopenid%2Bemail%2Bprofile%26state%3D${d.searchParams.get('state')}%26nonce%3D${d.searchParams.get('nonce')}`
  //       );
  //       localStorage.setItem('username', username);
  //       const toRes = await axios.get(flowsRes.data.to);
  //       const { data } = toRes;
  //       const masterData = await getMasterUser(data.secret_key.id);
  //       // const organ = await getOrganizations(data.secret_key.id);
  //       const rtres = await getUserRelation();
  //       const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
  //       localStorage.setItem(
  //         'accountData',
  //         JSON.stringify([
  //           ...accountData.filter((item) => {
  //             return item.username !== username;
  //           }),
  //           {
  //             username,
  //             password
  //           }
  //         ])
  //       );
  //       // const orgId =
  //       //   organ?.data?.find((item) => item.user_id === data.user.id)?.id ??
  //       //   null;
  //       const orgId = masterData?.data.master_org_id ?? null;
  //       const rtresItem = rtres.data?.data?.find((item) => item.fid === username);
  //       if (rtresItem) {
  //         localStorage.setItem('organization', rtresItem.oid);
  //         window.ipcRenderer.send('set-orgId', rtresItem.oid);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem(
  //           'user',
  //           JSON.stringify({
  //             ...data.user,
  //             id: rtresItem.meta_data.userId
  //           })
  //         );
  //       } else {
  //         localStorage.setItem('organization', orgId);
  //         window.ipcRenderer.send('set-orgId', orgId);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem('user', JSON.stringify(data.user));
  //       }
  //       // localStorage.setItem("secret_key", data.secret_key.id);
  //       localStorage.setItem('secret_key', masterData.data.master_user_sk);
  //       localStorage.setItem('old_secret_key', data.secret_key.id);
  //       window.ipcRenderer.send('set-secretKey', data.secret_key.id);
  //       // closetabs();
  //       localStorage.setItem('configStates', JSON.stringify([]));
  //       const assistantsRes = await getAssistants();
  //
  //       const id = assistantsRes.data[0]?.id;
  //       localStorage.setItem('assistantId', id);
  //       localStorage.setItem(
  //         'recovery_mode',
  //         assistantsRes.data[0]?.metadata?.recovery_mode ?? 'inquiry'
  //       );
  //       window.ipcRenderer.send('set-assistants', id);
  //       message.success('登录成功');
  //       setLoginLock(false);
  //       setOpen(false);
  //       navigate('/home');
  //       return { res };
  //     } catch (e) {
  //       message.error('登录失败');
  //       setLoginLock(false);
  //     }
  //   });
  // };
  const setAccount = () => {
    const newItem = cloneDeep(items);
    const data = JSON.parse(localStorage.getItem('accountData') ?? '[]');
    const currentUsername = localStorage.getItem('username');
    const dataItem = data.find((item) => item.username === currentUsername);
    newItem[0].children = data
      .filter((item) => !(item.username === currentUsername))
      .map((item) => {
        return {
          key: '4-' + item.username,
          label: (
            <div
              onClick={() => {
                switchLogin(item);
              }}
            >
              <AccountItem data={item} />
            </div>
          )
        };
      });
    newItem[0].children.push({
      key: '4-add',
      label: (
        <Button
          className={style.add_button}
          onClick={() => {
            setOpen(true);
          }}
        >
          <span
            style={{
              fontSize: '18px',
              marginRight: '4px',
              position: 'relative',
              bottom: '2px'
            }}
          >
            +
          </span>
          <span>添加账户</span>
        </Button>
      )
    });
    if (dataItem) {
      newItem[0].children.unshift({
        key: '4-' + dataItem.username,
        label: (
          <div>
            <AccountItem data={dataItem} />
          </div>
        )
      });
    }
    setItems(newItem);
  };
  const [dropdownOpen, setDropdownOpen] = useState(false);
  return (
    <div className={style.menu}>
      <Tooltip title="帮助">
        <img
          src={icon_question}
          alt="icon_service"
          onClick={() => {
            // noopener 参数确保新窗口无法访问原页面的 window 对象
            window.open('https://docs.qq.com/doc/DTG9pamVPT0lISGdY', '_blank', 'noopener');
          }}
          className={style.img}
        />
      </Tooltip>

      <Dropdown
        menu={{ items }}
        open={dropdownOpen}
        placement="bottom"
        trigger={['click']}
        onOpenChange={(open) => setDropdownOpen(open)}
      >
        <>
          <div
            className={style.text}
            style={{ marginRight: '6px', cursor: 'pointer' }}
            onClick={() => setDropdownOpen(!dropdownOpen)}
          >
            {JSON.parse(localStorage.getItem('user') ?? '{}')?.username ?? '未命名'}
          </div>
          {dropdownOpen ? (
            <UpOutlined
              style={{
                width: '10px',
                height: '5px',
                color: '#ABABAB'
              }}
            />
          ) : (
            <DownOutlined
              style={{
                width: '10px',
                height: '5px',
                color: '#ABABAB'
              }}
            />
          )}
        </>
      </Dropdown>
      <VersionModal open={versionOpen} setOpen={setVersionOpen} />
      <AddAccount open={open} setOpen={setOpen} />
      <UpdatePassword isModalOpen={isModalOpen} loginOut={logout} setIsModalOpen={setIsModalOpen} />
      <Spin spinning={loginLock} fullscreen={true} tip="切换中..." />
    </div>
  );
});

export default Menu;
