// import Versions from './components/Versions';
// import electronLogo from './assets/electron.svg';

function App(): JSX.Element {
  // const ipcHandle = (): void => window.electron.ipcRenderer.send('ping');

  const startQianniu = () => {
    window.xBrowser.send('new-tab', 'http://localhost:5173/add-ons/qianniu/', {
      integration: {
        id: 'someid',
        name: 'qianniu',
        options: {
          endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
          token: 'token'
        }
        // options: {
        //   isSilence: true,
        //   username: 'browser',
        //   myqa: {
        //     endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
        //     assistantId: 'asst_edeil7x4rwlouljntaosmc3byxssgtyp',
        //     secretKey: 'sk-8ISHTmbPJksfi6mZUo0uXjR4ArmrvNoatB0C3cOcBWdQih3Xo2HkoZvHGIeA7Yy6',
        //   },
        //   rpa: {
        //     server: '*************',
        //     startExe: true
        //   }
        // }
      },
      advances: {
        individualSession: true,
        disableMinimization: true,
        blockPowerSave: 'system',
        giveMeSomeAir: true
      }
    });
  };

  const startQianniu2 = () => {
    window.xBrowser.send('new-tab', 'http://localhost:5173/add-ons/qianniu/', {
      integration: {
        id: 'someid',
        name: 'qianniu',
        options: {
          endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
          token: 'token'
        }
        // options: {
        //   isSilence: true,
        //   username: 'browser',
        //   myqa: {
        //     endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
        //     assistantId: 'asst_edeil7x4rwlouljntaosmc3byxssgtyp',
        //     secretKey: 'sk-8ISHTmbPJksfi6mZUo0uXjR4ArmrvNoatB0C3cOcBWdQih3Xo2HkoZvHGIeA7Yy6',
        //   },
        //   rpa: {
        //     server: '*************',
        //     startExe: true
        //   }
        // }
      },
      advances: {
        individualSession: true,
        disableMinimization: true,
        blockPowerSave: 'display'
      }
    });
  };

  const startDoudian = () => {
    window.xBrowser.send('new-tab', 'https://fxg.jinritemai.com/login/common', {
      integration: {
        id: 'doudian_id',
        name: 'doudian',
        version: '2.0',
        options: {
          endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
          secretKey: 'sk-pZ9k8RHQ5fHaCENZMtWutUcvfOQEQSaYE7Lai4QIuTRPiuALTnZE2Sg0RvglHAfD',
          isSilence: false,
          assistantId: 'asst_7ni6yia3bhozchp6fesfax5ni6yzq7hw'
        }
      }
    });
  };

  const startSentry = () => {
    window.xBrowser.send('new-tab', 'https://sentry.dongchacat.cn/login/common', {
      integration: {
        id: 'sentry',
        name: 'sentry',
        options: {
          endpoint: 'https://aikf.tuzhou.net:2001/api/v2',
          secretKey: 'sk-pZ9k8RHQ5fHaCENZMtWutUcvfOQEQSaYE7Lai4QIuTRPiuALTnZE2Sg0RvglHAfD',
          isSilence: false,
          assistantId: 'asst_7ni6yia3bhozchp6fesfax5ni6yzq7hw'
        }
      },
      advances: {
        individualSession: true
      }
    });
  };

  return (
    <>
      <div className="text">欢迎使用x-browser</div>
      <button onClick={startQianniu}>千牛</button>
      <button onClick={startQianniu2}>千牛2</button>
      <button onClick={startDoudian}>抖店</button>
      <button onClick={startSentry}>sentry</button>
    </>
  );
}

export default App;
