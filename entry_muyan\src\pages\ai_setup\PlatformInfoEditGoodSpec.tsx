import React, { useEffect, useState } from 'react';
import Modal from '@/components/Modal';
import { Flex, Tag, Input, message } from 'antd';
import cloneDeep from 'lodash-es/cloneDeep';

type Props = {
  value: string;
  open: boolean;
  data: any[];
  setOpen: (value: boolean) => void;
  onChange: (value: string) => void;
};
const PlatformInfoEditGoodSpec: React.FC<Props> = ({
  value,
  open,
  setOpen,
  data,
  onChange
}: Props) => {
  const [selectedTags, setSelectedTags] = React.useState<string[]>([]);
  const [valueDate, setValueDate] = useState<any[]>([]);
  const handleChange = (tag: string, checked: boolean) => {
    const nextSelectedTags = checked
      ? [...selectedTags, tag]
      : selectedTags.filter((t) => t !== tag);
    setSelectedTags(nextSelectedTags);
    setValueDate(
      nextSelectedTags.map((item) => {
        const newItem = valueDate.find((vitem) => vitem.key === item);
        const dataItem = data.find((ditem) => ditem.name === item);
        if (newItem) {
          return newItem;
        } else {
          return {
            key: item,
            value: dataItem ? dataItem.defaultValue : ''
          };
        }
      })
    );
  };

  useEffect(() => {
    if (value && open) {
      const newValueDate = value.split('@').map((item) => {
        return {
          key: item.split(':')[0],
          value: item.split(':')[1]
        };
      });
      const newValueDateKeys = newValueDate.map((item) => item.key);
      const requireData = data.filter(
        (item) => item.require && !newValueDateKeys.includes(item.name)
      );
      setValueDate([
        ...newValueDate,
        ...requireData.map((item) => ({
          key: item.name,
          value: item.defaultValue
        }))
      ]);
      setSelectedTags([...newValueDateKeys, ...requireData.map((item) => item.name)]);
    } else if (open) {
      const requireData = data.filter((item) => item.require);
      setSelectedTags(requireData.map((item) => item.name));
      setValueDate(
        requireData.map((item) => ({
          key: item.name,
          value: item.defaultValue
        }))
      );
    } else {
      setValueDate([]);
      setSelectedTags([]);
    }
  }, [open]);

  return (
    <Modal
      title="商品规格"
      open={open}
      setOpen={setOpen}
      onOk={() => {
        if (valueDate.filter((item) => !item.value).length === 0) {
          onChange(valueDate.map((item) => item.key + ':' + item.value).join('@'));
          setOpen(false);
        } else {
          message.error('存在属性值未填写');
        }
      }}
    >
      <div
        style={{
          padding: '10px 20px'
        }}
      >
        <Flex gap={4} wrap align="center">
          <span>属性:</span>
          {data
            .filter((item) => {
              return item.enable;
            })
            .map((item) => {
              return (
                <Tag.CheckableTag
                  key={item.name}
                  checked={item.require || selectedTags.includes(item.name)}
                  onChange={(checked) => {
                    if (item.require) return;
                    handleChange(item.name, checked);
                  }}
                >
                  {item.name}
                </Tag.CheckableTag>
              );
            })}
        </Flex>
        <div
          style={{
            maxHeight: '240px',
            overflow: 'auto',
            marginTop: '10px'
          }}
        >
          {valueDate.map((item) => {
            return (
              <div key={item.key}>
                <div
                  style={{
                    fontSize: '12px',
                    margin: '8px 0px 4px',
                    position: 'relative',
                    paddingLeft: '5px'
                  }}
                >
                  {data.find((ditem) => ditem.name === item.key)?.require && (
                    <div
                      style={{
                        color: 'red',
                        position: 'absolute',
                        left: '0px',
                        top: '0px'
                      }}
                    >
                      *
                    </div>
                  )}
                  {item.key}:
                </div>
                <Input
                  value={item.value}
                  onInput={(e: any) => {
                    setValueDate(
                      cloneDeep(valueDate).map((vitem) => {
                        if (item.key === vitem.key) {
                          vitem.value = e.target.value;
                          return vitem;
                        }
                        return vitem;
                      })
                    );
                  }}
                />
              </div>
            );
          })}
        </div>
      </div>
    </Modal>
  );
};
export default PlatformInfoEditGoodSpec;
