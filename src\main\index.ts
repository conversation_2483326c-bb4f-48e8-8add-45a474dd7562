import './instrumentation';
import './crash-reporter';
import { app, BaseWindow, ipcMain, Menu, shell, WebContentsView } from 'electron';
import { electronApp, is, optimizer } from '@electron-toolkit/utils';
import * as Sentry from '@sentry/electron/main';
import os from 'node:os';
import './update-path';
import './global-handler';
import { log } from './log';
import { pptr } from './pptr';
import { store } from './store';
import Window from './window';
import packageJson from '../../package.json';

import './updater';

const mode = import.meta.env.MODE;
log.info('mode is', mode);
log.info('os is', os.platform(), os.arch(), os.release());
log.info('versions are', process.versions);
log.debug('userData path', app.getPath('userData'));
log.debug('logs path', app.getPath('logs'));
log.debug('temp path', app.getPath('temp'));
log.debug('crashDumps path', app.getPath('crashDumps'));

['muyan-browser'].includes(mode) &&
  Sentry.init({
    dsn: store.get('dsn'),
    environment: is.dev ? 'development' : 'production',
    tracesSampleRate: 1.0,
    release: packageJson.version
    // beforeBreadcrumb: (breadcrumb) => {
    //   if (breadcrumb.category === "console") {
    //     breadcrumb.message = breadcrumb.message?.replace(ansiRegex(), "");
    //   }
    //   return breadcrumb;
    // },
  });
Sentry.setTag('clientId', os.userInfo().username + '@' + os.hostname());
// Sentry.captureMessage('browser start', {
//   extra: {}, // 附带的数据
//   level: 'warning' // 错误等级，警告还是报错由你决定
// });

function createWindow(): void {
  new Window();
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
const pptrInitialized = pptr.initialize();
const modeImported = import(`./modes/${mode}.ts`);
const appReady = app.whenReady();

Promise.all([pptrInitialized, modeImported, appReady]).then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron');

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  // app.on('browser-window-created', (_, window) => {
  //   optimizer.watchWindowShortcuts(window)
  // })

  // IPC test
  // ipcMain.on('ping', () => console.log('pong'))

  createWindow();
  pptr.connect();

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BaseWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('before-quit', (e) => {
  log.debug('app before-quit');
  if (Window.size) {
    e.preventDefault();
    Window.closeAll();
  }
});

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// In this file you can include the rest of your app"s specific main process
// code. You can also put them in separate files and require them here.
