import { execFile } from 'node:child_process';
import path from 'node:path';

const __dirname = import.meta.dirname;

if (!process.env.npm_package_version) {
  console.log('error: npm_package_version is undefined, exit');
  process.exit(1);
}

const isAlphaOrBeta = /alpha|beta/.test(process.env.npm_package_version);

if (isAlphaOrBeta) {
  console.log('version is alpha or beta, exit');
  process.exit(0);
}

if (process.platform === 'win32') {
  execFile(
    path.join(__dirname, '../resources/coscli-windows-amd64.exe'),
    [
      'symlink',
      '--method',
      'create',
      `cos://muyan-package-1312011744/download/muyan/latest/win/muyan-${process.env.npm_package_version}-setup.exe`,
      '--link',
      'download/muyan/latest/win/muyan-setup.exe'
    ],
    (error, stdout, stderr) => {
      console.log(error, stdout, stderr);
    }
  );
} else if (process.platform === 'darwin') {
  // TODO
}
