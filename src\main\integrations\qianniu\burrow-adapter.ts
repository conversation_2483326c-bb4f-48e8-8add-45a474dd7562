import got from '../../utils/got';
import _ from 'lodash';

export default class BurrowAdapter {
  #endpoint: string;

  constructor(endpoint: string) {
    this.#endpoint = endpoint;
  }

  async getCs() {
    const path = '/qianniu/conn';
    const res = await got(this.#endpoint + path).json<string[]>();
    return res;
  }

  async getUser(cs) {
    const path = '/qianniu/dialog/active?target=' + cs;
    const res = await got(this.#endpoint + path).json<GetUserResult>();
    return res.targetId;
  }

  async getGoodsList(cs, page) {
    const path = `/qianniu/public/item/onsale?target=${cs}&pageNo=${page}`;
    const res = await got(this.#endpoint + path).json<Result<GoodsList>>();
    return res.data.itemList;
  }

  async getTimingSaleGoods(cs, shopType: 'tmall' | 'taobao', page) {
    if (!shopType) {
      throw new Error('shopType is required');
    }
    const path = `/qianniu/manage/product/onsaletiming?shopType=${shopType}&target=${cs}&pageNo=${page}`;
    const res = await got(this.#endpoint + path).json<Result<{ result: string }>>();
    const result = JSON.parse(res.data.result);

    if (result.success) {
      return result.data.table.dataSource.map((d) => {
        return {
          categoryId: _.get(d, 'catId'),
          haveGift: false,
          itemId: +_.get(d, 'itemId'),
          itemUrl: _.get(d, 'itemDesc.imgLink.href'),
          pic: 'https:' + _.get(d, 'itemDesc.img'),
          price: _.get(d, 'managerPrice.currentPrice', '').replace('¥ ', ''),
          quantity: _.get(d, 'managerQuantityNew.text'),
          soldQuantity: _.get(d, 'soldQuantity_m'),
          title: _.get(d, 'itemDesc.desc.0.copyText'),
          type: 'onSaleTiming',
          upShelfDate: _.get(d, 'upShelfDate_m.startTime', '').replace(' 上架销售', '')
        } as unknown as GoodsList['itemList'];
      });
    } else {
      throw new Error(res.ret[0]);
    }
  }

  async getGoodsDetail(cs, userId, itemId) {
    const path = `/qianniu/public/item/query?target=${cs}&userId=${userId}&itemId=${itemId}`;
    const res = await got(this.#endpoint + path).json<Result<GoodsDetail>>();
    return {
      propsName: res.data.propsName,
      skuList: res.data.skuList
    };
  }

  async getGoodsDesc(cs, userId, itemId) {
    const path = `/qianniu/public/item/render?target=${cs}&userId=${userId}&itemId=${itemId}`;
    const res = await got(this.#endpoint + path).json<Result<GoodsDesc>>();
    return res.data;
  }

  async getGoodsDetail2(cs, userId, itemId) {
    const path = `/qianniu/public/item/detail?target=${cs}&userId=${userId}&itemId=${itemId}`;
    const res = await got(this.#endpoint + path).json<Result<GoodsDetail2>>();
    return {
      skuBase: res.data.skuBase,
      extensionInfoVO: res.data.componentsVO.extensionInfoVO
    };
  }

  async getStatistics(cs) {
    const path = `/qianniu/service/oneself/data?target=${cs}`;
    const res = await got(this.#endpoint + path).json<Result<Data>>();
    const metrics = {
      ...res.data.data.offlineDO.metricDOMap,
      ...res.data.data.realTimeDO.metricDOMap
    };
    Object.values(metrics).forEach((value) => {
      if (!value.value) {
        // @ts-ignore default null
        value.value = null;
      }
    });
    return metrics;
  }

  async getOrders(cs, userId) {
    const path = `/qianniu/public/order/recent?target=${cs}&userId=${userId}`;
    const res = await got(this.#endpoint + path).json<Result<OrderData>>();
    return res.data.orders;
  }
}

/**
 * {
 *     "ctype": 0,
 *     "targetType": "3",
 *     "nick": "程诺2015cufe",
 *     "targetId": "2666455907",
 *     "ccode": "2666455907.1-3082119012.1#11001@cntaobao",
 *     "bizeType": "11001",
 *     "display": "禾zybf",
 *     "portrait": "pic:impicture|?filepath=D%3A%5CAliWorkbenchData%5CIMServiceDir%2Fportrait_image%5C6e%5C6ed1ae32a8c3e0e42c089d562419b8e4",
 *     "unreadcount": 0
 * }
 */

type GetUserResult = {
  ctype: number;
  targetType: string;
  nick: string;
  targetId: string;
  ccode: string;
  bizeType: string;
  display: string;
  portrait: string;
  unreadcount: number;
};

/**
 * {"api":"mtop.taobao.qianniu.cs.item.onsale.query","data":{"itemList":[{"categoryId":50023725,"haveGift":false,"itemId":18464346893,"itemUrl":"https://item.taobao.com/item.htm?id=18464346893","pic":"https://img.alicdn.com/bao/uploaded/i1/18331023969902079/T1IOy5XqldXXXXXXXX_!!0-item_pic.jpg","price":"1.00","quantity":140,"soldQuantity":122491,"title":"邮费 差价专拍链接,差多少补多少请勿乱拍"},{"categoryId":50012345,"haveGift":false,"itemId":679098527107,"itemUrl":"https://item.taobao.com/item.htm?id=679098527107","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN019d4srO2BPed4656X7_!!0-item_pic.jpg","price":"398.00","quantity":110337,"soldQuantity":50220,"title":"泰兰尼斯211秋冬款童鞋男童鞋子女头鞋婴儿学步鞋儿童软底机能鞋"},{"categoryId":50023728,"haveGift":false,"itemId":687211880990,"itemUrl":"https://item.taobao.com/item.htm?id=687211880990","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01Dpuf042BPePVypIUm_!!0-item_pic.jpg","price":"179.00","quantity":675,"soldQuantity":46683,"title":"泰兰尼斯宝宝棉袜袜子1双装（赠品勿拍）"},{"categoryId":50023728,"haveGift":false,"itemId":745226020199,"itemUrl":"https://item.taobao.com/item.htm?id=745226020199","pic":"https://img.alicdn.com/bao/uploaded/i3/1650438331/O1CN01s4qp502BPeW3zILYw_!!2-item_pic.png","price":"49.90","quantity":11236,"soldQuantity":46096,"title":"泰兰尼斯宝宝棉袜（赠品勿拍）"},{"categoryId":50012345,"haveGift":false,"itemId":719860781213,"itemUrl":"https://item.taobao.com/item.htm?id=719860781213","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01ns42Wf2BPed3SaWhO-1650438331.jpg","price":"398.00","quantity":20520,"soldQuantity":33986,"title":"泰兰尼斯211学步鞋男宝宝鞋子透气机能鞋秋冬新款女童鞋软底鞋子"},{"categoryId":121410006,"haveGift":false,"itemId":569961959535,"itemUrl":"https://item.taobao.com/item.htm?id=569961959535","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01OxAgp92BPeX5J2UCf_!!0-item_pic.jpg","price":"48.00","quantity":1183,"soldQuantity":33220,"title":"泰兰尼斯宝宝机能学步鞋垫透气吸汗避震婴儿1-7周岁大豆纤维垫"},{"categoryId":201332420,"haveGift":false,"itemId":714073817329,"itemUrl":"https://item.taobao.com/item.htm?id=714073817329","pic":"https://img.alicdn.com/bao/uploaded/i2/1650438331/O1CN0122fHdB2BPeb7hrrRm_!!1650438331.jpg","price":"1500.00","quantity":11863,"soldQuantity":28232,"title":"泰兰尼斯会员尊享购物金享受折上折-全店通用"},{"categoryId":50012345,"haveGift":false,"itemId":671564138797,"itemUrl":"https://item.taobao.com/item.htm?id=671564138797","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01wWPsiO2BPed59PxiY-1650438331.jpg","price":"368.00","quantity":8329,"soldQuantity":25038,"title":"泰兰尼斯男童鞋子婴儿学步鞋夏季女童透气儿童包头宝宝鞋凉鞋软底"},{"categoryId":50012345,"haveGift":false,"itemId":677666483227,"itemUrl":"https://item.taobao.com/item.htm?id=677666483227","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01T0fjNA2BPeczuQGsP_!!0-item_pic.jpg","price":"448.00","quantity":66129,"soldQuantity":23027,"title":"泰兰尼斯童鞋儿童学步鞋男宝宝春秋冬款篮球鞋女童机能防滑面包鞋"},{"categoryId":50012345,"haveGift":false,"itemId":827243019767,"itemUrl":"https://item.taobao.com/item.htm?id=827243019767","pic":"https://img.alicdn.com/bao/uploaded/i2/1650438331/O1CN01SIFpif2BPed2xVUar_!!0-item_pic.jpg","price":"398.00","quantity":5779,"soldQuantity":17281,"title":"泰兰尼斯秋冬季童鞋211学步鞋男宝宝鞋子防滑软底机能鞋女童鞋子"},{"categoryId":50023728,"haveGift":false,"itemId":639768560999,"itemUrl":"https://item.taobao.com/item.htm?id=639768560999","pic":"https://img.alicdn.com/bao/uploaded/i2/1650438331/O1CN01ibKuWY2BPeI7f7DRq_!!0-item_pic.jpg","price":"29.00","quantity":1575,"soldQuantity":17137,"title":"【u先】泰兰尼斯宝宝买鞋量脚器婴儿脚长测量器0-8岁儿童量脚尺"},{"categoryId":50012345,"haveGift":false,"itemId":730263884927,"itemUrl":"https://item.taobao.com/item.htm?id=730263884927","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN0104LIBG2BPecvZHOnF_!!0-item_pic.jpg","price":"598.00","quantity":15162,"soldQuantity":13377,"title":"泰兰尼斯稳稳鞋秋冬儿童鞋透气学步鞋男宝宝德训鞋机能鞋女童鞋子"},{"categoryId":127218010,"haveGift":false,"itemId":649395495140,"itemUrl":"https://item.taobao.com/item.htm?id=649395495140","pic":"https://img.alicdn.com/bao/uploaded/i2/1650438331/O1CN01xTdpe32BPed2qggd1-1650438331.jpg","price":"348.00","quantity":14579,"soldQuantity":12828,"title":"泰兰尼斯婴儿步前鞋秋冬女宝宝软底童鞋加绒男宝宝机能鞋子婴儿鞋"},{"categoryId":201195801,"haveGift":false,"itemId":685354167223,"itemUrl":"https://item.taobao.com/item.htm?id=685354167223","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01VoVAbf2BPed3YAalo-1650438331.jpg","price":"398.00","quantity":364,"soldQuantity":12816,"title":"泰兰尼斯FIT冬季新款儿童雪地靴加绒厚底防滑靴子男童女宝宝棉鞋"},{"categoryId":50012345,"haveGift":false,"itemId":719860929186,"itemUrl":"https://item.taobao.com/item.htm?id=719860929186","pic":"https://img.alicdn.com/bao/uploaded/i1/1650438331/O1CN01mOv7Wk2BPed43n4TE-1650438331.jpg","price":"398.00","quantity":9680,"soldQuantity":12708,"title":"泰兰尼斯面包鞋女童软底鞋子婴儿学步鞋男宝宝秋冬童鞋新款机能鞋"},{"categoryId":50012345,"haveGift":false,"itemId":775123791322,"itemUrl":"https://item.taobao.com/item.htm?id=775123791322","pic":"https://img.alicdn.com/bao/uploaded/i1/1650438331/O1CN01CQwO1B2BPed3STsWH-1650438331.jpg","price":"398.00","quantity":15244,"soldQuantity":12316,"title":"泰兰尼斯24夏季新款男童凉鞋防滑软底女宝宝网面学步鞋儿童踩水鞋"},{"categoryId":50012345,"haveGift":false,"itemId":764776646372,"itemUrl":"https://item.taobao.com/item.htm?id=764776646372","pic":"https://img.alicdn.com/bao/uploaded/i3/1650438331/O1CN018QTK5f2BPecrbA6ZE_!!0-item_pic.jpg","price":"398.00","quantity":45643,"soldQuantity":11646,"title":"泰兰尼斯童鞋秋冬新款儿童学步鞋男宝宝机能鞋软底面包鞋女童鞋子"},{"categoryId":50012345,"haveGift":false,"itemId":724994957823,"itemUrl":"https://item.taobao.com/item.htm?id=724994957823","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01YzCpQ32BPed2xqVLw-1650438331.jpg","price":"398.00","quantity":5108,"soldQuantity":11124,"title":"泰兰尼斯春秋新款运动童鞋学步鞋男宝宝软底鞋子防滑婴儿机能鞋女"},{"categoryId":50012341,"haveGift":false,"itemId":719860857033,"itemUrl":"https://item.taobao.com/item.htm?id=719860857033","pic":"https://img.alicdn.com/bao/uploaded/i4/1650438331/O1CN01R4ltVG2BPecwMK02s-1650438331.jpg","price":"498.00","quantity":4141,"soldQuantity":10645,"title":"泰兰尼斯春秋儿童平衡车鞋男童防撞运动鞋女童鞋子幼儿园休闲鞋"},{"categoryId":50012345,"haveGift":false,"itemId":710113610665,"itemUrl":"https://item.taobao.com/item.htm?id=710113610665","pic":"https://img.alicdn.com/bao/uploaded/i2/1650438331/O1CN01qwjU862BPed5JAZiY-1650438331.jpg","price":"368.00","quantity":2382,"soldQuantity":7694,"title":"泰兰尼斯夏季新款儿童鞋防滑软底鞋子男童凉鞋婴儿学步鞋女宝宝"}],"total":906},"ret":["SUCCESS::调用成功"],"v":"1.0"}
 */
// 根据上面注释的内容，生成以下类型
type Result<T> = {
  api: string;
  data: T;
  ret: string[];
  v: string;
};

export type GoodsList = {
  itemList: {
    categoryId: number;
    haveGift: boolean;
    itemId: number;
    itemUrl: string;
    pic: string;
    price: string;
    quantity: number;
    soldQuantity: number;
    title: string;
    propsName: string;
  }[];
  total: number;
};

export interface GoodsDetail {
  collected: boolean;
  deliveryLocation: string;
  deliveryTimeData: DeliveryTimeData;
  item: Item;
  itemDeliveryTimeMap: ItemDeliveryTimeMap;
  itemPicData: ItemPicData;
  itemServiceList: ItemServiceList[];
  props: string;
  propsName: string;
  saleInfo: SaleInfo;
  showDeliveryTime: boolean;
  showItemSizeTab: boolean;
  showPromotionPrice: boolean;
  skuList: Sku[];
  tabs: Tab[];
}

export interface DeliveryTimeData {
  deliveryLocation: string;
  deliveryServiceInfo: string;
  ipInfo: IPInfo;
  itemId: number;
  postInfoList: string[];
  skuId: number;
}

export interface IPInfo {
  area: string;
  areaId: number;
  city: string;
  country: string;
  province: string;
}

export interface Item {
  approveStatus: string;
  buttons: Button[];
  categoryId: number;
  haveGift: boolean;
  itemId: number;
  itemUrl: string;
  pic: string;
  price: string;
  promotionPrice: string;
  props: string;
  propsAlias: string;
  propsName: string;
  quantity: number;
  skus: Sku[];
  soldQuantity: number;
  tags: string;
  title: string;
}

export interface Button {
  behavior: string;
  code: string;
  hidden: boolean;
  name: string;
  type: string;
}

export interface Sku {
  outerId: string;
  price: string;
  promotionPrice: string;
  props: string;
  propsName: string;
  quantity: number;
  skuId: number;
}

export interface ItemDeliveryTimeMap {
  '************': DeliveryTimeData;
}

export interface ItemPicData {
  deliveryLocation: string;
  itemImageList: string[];
  skuImageDataList: SkuImageDataList[];
  wirelessDetailPic: string;
}

export interface SkuImageDataList {
  properties: string;
  skuId: number[];
  skuImageUrl: string;
}

export interface ItemServiceList {
  description: string;
  detailUrl?: string;
  serviceName: string;
}

export interface SaleInfo {}

export interface Tab {
  behavior: string;
  code: string;
  hidden: boolean;
  name: string;
  tag: string;
  tagExpireTime: Date;
  type: string;
}

export type GoodsDetail2 = {
  seller: Seller;
  item: DataItem;
  feature: Feature;
  plusViewVO: PlusViewVO;
  skuCore: SkuCore;
  services: null;
  params: Params;
  skuBase: SkuBase;
  pcTrade: PCTrade;
  componentsVO: ComponentsVO;
};

export type ComponentsVO = {
  headImageVO: HeadImageVO;
  headerVO: HeaderVO;
  storeCardVO: StoreCardVO;
  titleVO: TitleVO;
  debugVO: DebugVO;
  umpPriceLogVO: UmpPriceLogVO;
  deliveryVO: DeliveryVO;
  o2oVo: O2OVo;
  bottomBarVO: BottomBarVO;
  extensionInfoVO: ExtensionInfoVO;
  rightBarVO: RightBarVO;
  priceVO: PriceVO;
  webfontVO: WebfontVO;
  tabVO: TabVO;
};

export type BottomBarVO = {
  buyInMobile: string;
  leftButtons: TButton[];
  rightButtons: TButton[];
};

export type TButton = {
  background?: LeftButtonBackground;
  disabled: string;
  title: LeftButtonTitle;
  type: string;
  icon?: Icon;
};

export type LeftButtonBackground = {
  alpha: string;
  disabledAlpha: string;
  disabledColor: string[];
  gradientColor: string[];
};

export type Icon = {
  alpha: string;
  color: string;
  disabledAlpha: string;
  disabledColor: string;
  iconFontName: string;
  size: string;
};

export type LeftButtonTitle = {
  alpha: string;
  bold: string;
  color: string;
  disabledAlpha: string;
  disabledColor: string;
  fontSize: string;
  text: string;
};

export type DebugVO = {
  host: string;
  traceId: string;
};

export type DeliveryVO = {
  agingDesc: AgingDesc;
  areaId: string;
  deliverToCity: string;
  deliveryFromAddr: string;
  deliveryToAddr: string;
  deliveryToDistrict: string;
  freight: string;
};

export enum AgingDesc {
  承诺48小时内发货晚发必赔 = '承诺48小时内发货，晚发必赔'
}

export type ExtensionInfoVO = {
  infos: Info[];
};

export type Info = {
  items: InfoItem[];
  title: string;
  type: string;
};

export type InfoItem = {
  text: string[];
  icon?: string;
  title?: string;
};

export type HeadImageVO = {
  images: string[];
  videos: HeadImageVOVideo[];
};

export type HeadImageVOVideo = {
  itemId: string;
  spatialVideoDimension: string;
  url: string;
  videoId: string;
  videoThumbnailURL: string;
};

export type HeaderVO = {
  buttons: HeaderVOButton[];
  logoJumpUrl: string;
  mallLogo: string;
  searchText: string;
};

export type HeaderVOButton = {
  background: ButtonBackground;
  disabled: string;
  events: ButtonEvent[];
  subTitle: BizDataBuyParams;
  title: ButtonTitle;
  type: string;
};

export type ButtonBackground = {
  alpha: string;
  disabledAlpha: string;
};

export type ButtonEvent = {
  fields: PurpleFields;
  type: string;
};

export type PurpleFields = {
  url: string;
};

export type BizDataBuyParams = {};

export type ButtonTitle = {
  text: string;
};

export type O2OVo = {
  enableJzLocalizationProduct: string;
};

export type PriceVO = {
  futureQuanPrice: FutureQuanPriceClass;
  isNewStyle: string;
  mainBelt: MainBelt;
  price: FutureQuanPriceClass;
};

export type FutureQuanPriceClass = {
  hiddenPrice: string;
  priceColor: string;
  priceMoney: string;
  priceText: string;
  priceTitle: string;
  priceTitleColor: string;
  priceUnit: string;
};

export type MainBelt = {
  beltStyleType: string;
  logo: string;
  priceBeltImg: string;
  rightBelt: RightBelt;
};

export type RightBelt = {
  countdown: string;
  extraText: string;
  extraTextColor: Color;
  text: string;
  textColor: Color;
};

export enum Color {
  Ffffff = '#FFFFFF'
}

export type RightBarVO = {
  buyerButtons: BuyerButton[];
  sellerButtons: any[];
};

export type BuyerButton = {
  disabled: string;
  href?: string;
  icon?: string;
  label?: string;
  priority: string;
  type: string;
};

export type StoreCardVO = {
  buttons: StoreCardVOButton[];
  creditLevel: string;
  creditLevelIcon: string;
  evaluates: Evaluate[];
  labelList: LabelList[];
  overallScore: string;
  sellerType: string;
  shopIcon: string;
  shopName: string;
  shopUrl: string;
  startsIcon: string;
};

export type StoreCardVOButton = {
  disabled: string;
  image: Image;
  title: ButtonTitle;
  type: string;
  events?: ButtonEvent[];
};

export type Image = {
  gifAnimated: string;
  imageUrl: string;
};

export type Evaluate = {
  level: string;
  levelText: string;
  score: string;
  title: string;
  type: string;
};

export type LabelList = {
  contentDesc: string;
};

export type TabVO = {
  tabList: TabList[];
};

export type TabList = {
  icon?: string;
  name: string;
  sort: string;
  title: string;
};

export type TitleVO = {
  salesDesc: string;
  subTitles: TitleElement[];
  title: TitleElement;
};

export type TitleElement = {
  title: string;
};

export type UmpPriceLogVO = {
  bcType: string;
  bs: string;
  dumpInvoke: string;
  map: string;
  sellerId: string;
  sid: string;
  traceId: string;
  type: string;
  umpCreateTime: Date;
  version: string;
  xobjectId: string;
};

export type WebfontVO = {
  enableWebfont: string;
};

export type Feature = {
  pcResistDetail: string;
  tmwOverseasScene: string;
};

export type DataItem = {
  bottomIcons: any[];
  images: string[];
  itemId: string;
  pcADescUrl: string;
  qrCode: string;
  rightFloatIcons: RightFloatIcon[];
  spuId: string;
  title: string;
  titleIcon: string;
  useWirelessDesc: string;
  vagueSellCount: string;
  videos: ItemVideo[];
};

export type RightFloatIcon = {
  disabled: string;
  events: RightFloatIconEvent[];
  iconUrl: string;
  text: string;
  type: string;
};

export type RightFloatIconEvent = {
  fields: FluffyFields;
};

export type FluffyFields = {
  timeMovingId: string;
  itemId: string;
  anchorId: null;
  liveId: string;
};

export type ItemVideo = {
  actionEvent: ActionEvent;
  itemId: string;
  spatialVideoDimension: string;
  type: string;
  url: string;
  videoId: string;
  videoThumbnailURL: string;
  weexRecommendUrl: string;
};

export type ActionEvent = {
  exposureArgs: ExposureArgs;
  openUrlEventArgs: OpenURLEventArgs;
};

export type ExposureArgs = {
  item_id: string;
  video_id: string;
};

export type OpenURLEventArgs = {
  enableUserTrackEvent: string;
  userTrackArgs: UserTrackArgs;
};

export type UserTrackArgs = {
  page: string;
  type: string;
  item_id: string;
  arg1: string;
  video_id: string;
};

export type Params = {
  aplusParams?: string;
  trackParams?: TrackParams;
  userAbtestDetail: string;
  trackNamePre?: string;
  trackName?: string;
};

export type TrackParams = {
  detailabtestdetail?: string;
  spm?: string;
  scm?: string;
};

export type PCTrade = {
  bizDataBuyParams: BizDataBuyParams;
  buyNowUrl: string;
  pcBuyParams: PCBuyParams;
  pcCartParam: PCCartParam;
  tradeType: string;
};

export type PCBuyParams = {
  virtual: string;
  buy_now: string;
  auction_type: string;
  'x-uid': string;
  title: string;
  buyer_from: string;
  page_from_type: string;
  detailIsLimit: string;
  who_pay_ship: string;
  rootCatId: string;
  auto_post1: null;
  routeToNewPc: string;
  auto_post: string;
  seller_nickname: string;
  photo_url: string;
  current_price: string;
  region: string;
  seller_id: string;
  etm: string;
};

export type PCCartParam = {
  areaId: string;
};

export type PlusViewVO = {
  guessLikeVO: Vo;
  headAtmosphereBeltVO: HeadAtmosphereBeltVO;
  commentListVO: CommentListVO;
  pcFrontSkuQuantityLimitVO: Vo;
  buyParamVO: BuyParamVO;
};

export type BuyParamVO = {
  bizCode: string;
  ext: BuyParamVOEXT;
  hit: string;
  spm: string;
};

export type BuyParamVOEXT = {
  autoApplCoupSource: string;
  needAutoApplCoup: string;
};

export type CommentListVO = {
  bizCode: string;
  ext: CommentListVOEXT;
  hit: string;
  spm: string;
};

export type CommentListVOEXT = {
  countShow: string;
};

export type Vo = {
  bizCode: string;
  hit: string;
};

export type HeadAtmosphereBeltVO = {
  bizCode: string;
  hit: string;
  valid: string;
};

export type Seller = {
  creditLevel: string;
  creditLevelIcon: string;
  evaluates: Evaluate[];
  pcShopUrl: string;
  sellerId: string;
  sellerNick: string;
  sellerType: string;
  shopIcon: string;
  shopId: string;
  shopName: string;
  startsIcon: string;
  userId: string;
};

export type SkuBase = {
  components: any[];
  props: Prop[];
  skus: Skus[];
};

export type Prop = {
  comboProperty: string;
  hasImage: string;
  name: string;
  nameDesc?: string;
  packProp: string;
  pid: string;
  values: Value[];
};

export type Value = {
  comboPropertyValue: string;
  image?: string;
  name: string;
  sortOrder: string;
  vid: string;
  cornerBackgroundColor?: string;
  cornerBorderColor?: Color;
  cornerTextColor?: Color;
  skuV3cornerIcon?: string;
  skuV3cornerText?: string;
};

export type Skus = {
  propPath: string;
  skuId: string;
};

export type SkuCore = {
  relatedAuctions: RelatedAuctions;
  sku2info: { [key: string]: Sku2Info };
  skuItem: SkuItem;
};

export type RelatedAuctions = {
  items: RelatedAuctionsItem[];
  title: string;
};

export type RelatedAuctionsItem = {
  current: string;
  itemId: string;
  itemNameList: string[];
};

export type Sku2Info = {
  futureQuanPrice?: FutureQuanPrice;
  logisticsTime: AgingDesc;
  moreQuantity: string;
  price: Sku2InfoPrice;
  quantity: string;
  quantityDisplayValue: string;
  quantityText: QuantityText;
  cartParam?: CartParam;
  quantityErrorMsg?: string;
};

export type CartParam = {
  addCartCheck: string;
};

export type FutureQuanPrice = {
  priceBgColor: PriceBgColor;
  priceColor: Color;
  priceMoney: string;
  priceText: string;
  priceTitle: PriceTitle;
  priceTitleColor: Color;
};

export enum PriceBgColor {
  Ff5000 = '#FF5000'
}

export enum PriceTitle {
  折后 = '折后'
}

export type Sku2InfoPrice = {
  priceMoney: string;
  priceText: string;
};

export enum QuantityText {
  无货 = '无货',
  有货 = '有货'
}

export type SkuItem = {
  itemStatus: string;
  renderSku: string;
  unitBuy: string;
};

export interface Data {
  code: string;
  data: Statistics;
}

export interface Statistics {
  buyersDO: BuyersDo;
  noticeDO: NoticeDo;
  offlineDO: OfflineDo;
  realTimeDO: RealTimeDo;
  scheduleDO: ScheduleDo;
}

export interface BuyersDo {
  replyBuyerNoPayUvIds: any[];
  replyNoBuyerUvIds: number[];
}

export interface NoticeDo {
  level: string;
  title: string;
  toast: string;
}

export interface OfflineDo {
  done: boolean;
  forceUpdate: boolean;
  metricDOMap: MetricDomap;
}

export interface MetricDomap {
  manyiRate: ManyiRate;
  man3MinReplyRate: Man3MinReplyRate;
  avgReplyDur1D: AvgReplyDur1D;
}

export interface ManyiRate {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
  warningDO: WarningDo;
}

export interface WarningDo {
  hoverDataAndStandardLineContent: string;
  hoverDataRangeLineContent: string;
  hoverDiagnoseLineContent: string;
  hoverTipsLineContent: string;
}

export interface Man3MinReplyRate {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
  warningDO: WarningDo2;
}

export interface WarningDo2 {
  hoverDataAndStandardLineContent: string;
  hoverDataRangeLineContent: string;
  hoverDiagnoseLineContent: string;
  hoverTipsLineContent: string;
}

export interface AvgReplyDur1D {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
  warningDO: WarningDo3;
}

export interface WarningDo3 {
  hoverDataAndStandardLineContent: string;
  hoverDataRangeLineContent: string;
  hoverDiagnoseLineContent: string;
  hoverTipsLineContent: string;
}

export interface RealTimeDo {
  done: boolean;
  forceUpdate: boolean;
  metricDOMap: MetricDomap2;
}

export interface MetricDomap2 {
  replyBuyerHasPayUv: ReplyBuyerHasPayUv;
  replyBuyerUv: ReplyBuyerUv;
  payCstRate: PayCstRate;
  replyBuyerNoPayUv: ReplyBuyerNoPayUv;
  replyNoBuyerUv: ReplyNoBuyerUv;
}

export interface ReplyBuyerHasPayUv {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
}

export interface ReplyBuyerUv {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
}

export interface PayCstRate {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
}

export interface ReplyBuyerNoPayUv {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
}

export interface ReplyNoBuyerUv {
  name: string;
  needWarning: boolean;
  tips: string;
  type: number;
  value: number;
}

export interface ScheduleDo {
  nextCallTimeMs: number;
}

export interface GoodsDesc {
  components: Components;
  abtestParam: string;
  page: Page;
  weexData: WeexData;
}

export interface Components {
  layout: Layout[];
  componentData: ComponentData;
  componentActions: ComponentActions;
}

export interface Layout {
  ID: string;
  key: string;
}

export interface ComponentData {
  division_blank_id: DivisionBlankId;
  detail_pic_tmallPriceDesc_6: DetailPicTmallPriceDesc6;
  detail_pic_1742277288016_4_4: DetailPic174227728801644;
  detail_pic_1742277288016_5_5: DetailPic174227728801655;
  detail_pic_1742277288016_2_2: DetailPic174227728801622;
  division_id: DivisionId;
  detail_pic_1742277288016_3_3: DetailPic174227728801633;
  detail_pic_1742277288016_1_1: DetailPic174227728801611;
}

export interface DivisionBlankId {
  model: Model;
  styles: Styles;
}

export interface Model {
  text: string;
}

export interface Styles {
  topLine: string;
  bgcolor: string;
  type: string;
}

export interface DetailPicTmallPriceDesc6 {
  children: any[];
  model: Model2;
  styles: Styles2;
  actions: string[];
}

export interface Model2 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles2 {
  size: Size;
}

export interface Size {
  width: string;
  height: string;
}

export interface DetailPic174227728801644 {
  children: any[];
  model: Model3;
  styles: Styles3;
  actions: string[];
}

export interface Model3 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles3 {
  size: Size2;
}

export interface Size2 {
  width: string;
  height: string;
}

export interface DetailPic174227728801655 {
  children: any[];
  model: Model4;
  styles: Styles4;
  actions: string[];
}

export interface Model4 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles4 {
  size: Size3;
}

export interface Size3 {
  width: string;
  height: string;
}

export interface DetailPic174227728801622 {
  children: any[];
  model: Model5;
  styles: Styles5;
  actions: string[];
}

export interface Model5 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles5 {
  size: Size4;
}

export interface Size4 {
  width: string;
  height: string;
}

export interface DivisionId {
  model: Model6;
  styles: Styles6;
}

export interface Model6 {
  text: string;
}

export interface Styles6 {
  topLine: string;
  bgcolor: string;
  type: string;
}

export interface DetailPic174227728801633 {
  children: any[];
  model: Model7;
  styles: Styles7;
  actions: string[];
}

export interface Model7 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles7 {
  size: Size5;
}

export interface Size5 {
  width: string;
  height: string;
}

export interface DetailPic174227728801611 {
  children: any[];
  model: Model8;
  styles: Styles8;
  actions: string[];
}

export interface Model8 {
  picUrl: string;
  itemId: string;
  locateId: string;
  sellerId: string;
  anchorPoints: any[];
}

export interface Styles8 {
  size: Size6;
}

export interface Size6 {
  width: string;
  height: string;
}

export interface ComponentActions {
  utHotArea: UtHotArea;
  utGotoVideoTimeline: UtGotoVideoTimeline;
  utFetchCoupons: UtFetchCoupons;
  utGotoDetailRec: UtGotoDetailRec;
  utOpenStoreList: UtOpenStoreList;
  utGotoActivityPage: UtGotoActivityPage;
  utExposureRcmdVideo: UtExposureRcmdVideo;
  utSkuBar: UtSkuBar;
  OpenPageWeex: OpenPageWeex;
  utExposureWeexComponent: UtExposureWeexComponent;
  utCharity: UtCharity;
  gotoPage: GotoPage;
  utExposureRcmd: UtExposureRcmd;
  utGotoDetail: UtGotoDetail;
  utOpenBigPic: UtOpenBigPic;
  utGotoService: UtGotoService;
  utGotoSpecification: UtGotoSpecification;
  utOpenStore: UtOpenStore;
  utOpenThreeD: UtOpenThreeD;
  utCinemaGraph: UtCinemaGraph;
  utExposureUserTalk: UtExposureUserTalk;
  utOpenFans: UtOpenFans;
  utUserTalk: UtUserTalk;
}

export interface UtHotArea {
  params: Params;
  key: string;
}

export interface UtGotoVideoTimeline {
  params: Params2;
  key: string;
}

export interface Params2 {
  trackParams: TrackParams2;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams2 {
  spm: string;
  item_id: string;
  seller_id: string;
}

export interface UtFetchCoupons {
  params: Params3;
  key: string;
}

export interface Params3 {
  trackParams: TrackParams3;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams3 {
  spm: string;
  coupon_id: string;
  coupon_price: string;
  scm: string;
}

export interface UtGotoDetailRec {
  params: Params4;
  key: string;
}

export interface Params4 {
  trackParams: TrackParams4;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams4 {
  rec_item_id: string;
  pvid: string;
  match_type: string;
  index: string;
}

export interface UtOpenStoreList {
  params: Params5;
  key: string;
}

export interface Params5 {
  trackNamePre: string;
  trackName: string;
}

export interface UtGotoActivityPage {
  params: Params6;
  key: string;
}

export interface Params6 {
  trackParams: TrackParams5;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams5 {
  spm: string;
  activity_id: string;
  scm: string;
}

export interface UtExposureRcmdVideo {
  params: Params7;
  key: string;
}

export interface Params7 {
  spm: string;
  trackPage: string;
  item_id: string;
  seller_id: string;
}

export interface UtSkuBar {
  params: Params8;
  key: string;
}

export interface Params8 {
  trackParams: TrackParams6;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams6 {
  spm: string;
  scm: string;
}

export interface OpenPageWeex {
  params: Params9;
  key: string;
}

export interface Params9 {
  urlParams: UrlParams;
  url: string;
}

export interface UrlParams {
  itemId: string;
  spm: string;
  showId: string;
  sellerId: string;
  from: string;
  type: string;
  pgcId: string;
}

export interface UtExposureWeexComponent {
  params: Params10;
  key: string;
}

export interface Params10 {
  spm: string;
  trackPage: string;
  item_id: string;
  seller_id: string;
}

export interface UtCharity {
  params: Params11;
  key: string;
}

export interface Params11 {
  spm: string;
  trackPage: string;
  item_id: string;
  seller_id: string;
}

export interface GotoPage {
  params: Params12;
  key: string;
}

export interface Params12 {
  url: string;
}

export interface UtExposureRcmd {
  params: Params13;
  key: string;
}

export interface Params13 {
  rec_item_id: string;
  trackPage: string;
  pvid: string;
  index: string;
  match_type: string;
}

export interface UtGotoDetail {
  params: Params14;
  key: string;
}

export interface Params14 {
  trackParams: TrackParams7;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams7 {
  spm: string;
  rec_item_id: string;
  pvid: string;
  index: string;
  scm: string;
  rn: string;
  av_type: string;
}

export interface UtOpenBigPic {
  params: Params15;
  key: string;
}

export interface Params15 {
  trackParams: TrackParams8;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams8 {
  spm: string;
  scm: string;
}

export interface UtGotoService {
  params: Params16;
  key: string;
}

export interface Params16 {
  trackParams: TrackParams9;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams9 {
  spm: string;
}

export interface UtGotoSpecification {
  params: Params17;
  key: string;
}

export interface Params17 {
  trackNamePre: string;
  trackName: string;
}

export interface UtOpenStore {
  params: Params18;
  key: string;
}

export interface Params18 {
  trackNamePre: string;
  trackName: string;
}

export interface UtOpenThreeD {
  params: Params19;
  key: string;
}

export interface Params19 {
  trackNamePre: string;
  trackName: string;
}

export interface UtCinemaGraph {
  params: Params20;
  key: string;
}

export interface Params20 {
  spm: string;
  shop_id: string;
  trackPage: string;
  mediaType: string;
  page: string;
  seller_id: string;
  video_id: string;
}

export interface UtExposureUserTalk {
  params: Params21;
  key: string;
}

export interface Params21 {
  spm: string;
  trackPage: string;
  pcg_id: string;
}

export interface UtOpenFans {
  params: Params22;
  key: string;
}

export interface Params22 {
  trackNamePre: string;
  trackName: string;
}

export interface UtUserTalk {
  params: Params23;
  key: string;
}

export interface Params23 {
  trackParams: TrackParams10;
  trackNamePre: string;
  trackName: string;
}

export interface TrackParams10 {
  spm: string;
  pgc_id: string;
}

export interface Page {
  pageActions: PageAction[];
}

export interface PageAction {
  params: Params24;
  key: string;
}

export interface Params24 {
  spm: string;
  trackPage: string;
  isNative: string;
}

export interface WeexData {}

export interface OrderData {
  orders: Order[];
  pageNum: number;
  useNewChangeRefund: boolean;
  useNewInsteadRefund: boolean;
}

export interface Order {
  adjustFee: string;
  afterSaleText: string;
  bizOrderId: string;
  buyAmount: number;
  cardTypeText: string;
  category: string;
  collapse: boolean;
  consignTime: string;
  createTime: string;
  expressCompany: string;
  expressOrderNumber: string;
  extAttributes: ExtAttributes;
  extInfoItems: any[];
  itemList: ItemList[];
  moreOperators: any[];
  operators: Operator2[];
  orderPrice: string;
  overseasUser: boolean;
  payTime: string;
  postFee: string;
  promotionDetails: PromotionDetail2[];
  promotionTotalFee: string;
  receiverAddress: string;
  receiverMobilePhone: string;
  receiverName: string;
  refundFee: string;
  riskOrder: boolean;
  sellerFlag: number;
  sellerMemo: string;
  tags: string[];
  underInquiry: boolean;
  xsdOrder: boolean;
}

export interface ExtAttributes {
  _F_zfbt_06: string;
}

export interface ItemList {
  adjustFee: string;
  auctionId: string;
  auctionPrice: string;
  auctionTitle: string;
  auctionUrl: string;
  bizOrderId: string;
  buyAmount: number;
  buyerAmount: number;
  buyerRateStatus: number;
  cardType: string;
  cardTypeText: string;
  createTime: string;
  extInfoItems: ExtInfoItem[];
  itemCode: string;
  logisticsStatus: number;
  oldPrice: string;
  operators: Operator[];
  outerId: string;
  payStatus: number;
  payTime: string;
  picUrl: string;
  price: string;
  promotionDetails?: PromotionDetail[];
  refundFee: string;
  refundStatus: number;
  sku?: string;
  skuCode: string;
  snapshotUrl?: string;
  subOrderId: string;
  supportPriceProtect: boolean;
  tags: any[];
  underInquiry: boolean;
}

export interface ExtInfoItem {
  code: string;
  desc: string;
  textBefore: string;
  value: string;
}

export interface Operator {
  action: string;
  behavior: string;
  code: string;
  extendInfo?: string;
  position: number;
  title: string;
}

export interface PromotionDetail {
  discountFee: string;
  promotionDesc: string;
  promotionId: string;
  promotionName: string;
}

export interface Operator2 {
  action: string;
  behavior: string;
  code: string;
  extendInfo?: string;
  position: number;
  title: string;
  tag?: string;
  tagExpireTime?: string;
}

export interface PromotionDetail2 {
  discountFee: string;
  promotionDesc: string;
  promotionId: string;
  promotionName: string;
}
