/* src/Login.css */
.login-container {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100vh;
  width: 100vw;
  background-image: url("../../assets/imgs/login_bg.png");
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  padding: 0 8% 0 16%;
  box-sizing: border-box;
}
.muyanIcon {
  position: absolute;
  left: 32px;
  top: 32px;
}
.login-container .dev {
  position: absolute;
  top: 40px;
  color: #ababab;
  left: 270px;
}
.login-box {
  width: 440px;
  height: 360px;
  border-radius: 16px;
  padding: 52.5px 46px;
  box-sizing: border-box;
  background-image: url("../../assets/imgs/login-box-bg.png");
  background-size: 616px 500px;
  background-repeat: no-repeat;
  background-position: center;
}

.login-title {
  font-weight: bold;
  font-size: 20px;
  color: #ffffff;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  text-transform: none;
  margin-bottom: 28px;
}

.login-content {
  width: 348px;
  height: 255px;
  margin: 0 auto;
}

.login-form {
  margin: 0 auto;
}

.login-form input::placeholder {
  color: rgba(255, 255, 255, 0.45);
}

.login-form .ant-input-suffix svg {
  color: rgba(255, 255, 255, 0.65);
}

.login-container .version {
  position: absolute;
  bottom: 10px;
  left: 20px;
  color: #eef4fa;
}

.login-input-icon {
  width: 20px;
  height: 20px;
}

.login-input {
  height: 40px;
  background-color: transparent !important;
  border: 1px solid rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.login-input:hover {
  border: 1px solid #4794ff;
  background-color: transparent;
}

.login-input:focus .login-input:active {
  border: 1px solid #2958e6 !important;
  background-color: transparent;
}
