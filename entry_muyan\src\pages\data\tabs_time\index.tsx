import dayjs from 'dayjs';
import { DatePicker, Segmented } from 'antd';
import { useState } from 'react';
type Props = {
  value: any;
  onChange: (value: any) => void;
};
const { RangePicker } = DatePicker;
const TabsTime: React.FC<Props> = ({ value, onChange }: Props) => {
  const [activeTabKey, setActiveTabKey] = useState<string>('Yesterday');
  const data = [
    {
      label: '昨日',
      value: 'Yesterday',
      time: [
        dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD')),
        dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD'))
      ]
    },
    {
      label: '今日',
      value: 'Today',
      time: [
        dayjs(dayjs(new Date()).format('YYYY-MM-DD')),
        dayjs(dayjs(new Date()).format('YYYY-MM-DD'))
      ]
    },
    {
      label: '近7日',
      value: 'Last7Days',
      time: [
        dayjs(dayjs(new Date()).subtract(7, 'day').format('YYYY-MM-DD')),
        dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD'))
      ]
    },
    {
      label: '近30日',
      value: 'Last30Days',
      time: [
        dayjs(dayjs(new Date()).subtract(30, 'day').format('YYYY-MM-DD')),
        dayjs(dayjs(new Date()).subtract(1, 'day').format('YYYY-MM-DD'))
      ]
    }
  ];

  const onOk = (val: any): void => {
    onChange(val);
    setActiveTabKey('');
  };

  return (
    <div
      className="filter"
      style={{
        display: 'flex',
        alignItems: 'center',
        marginTop: '10px'
      }}
    >
      {/* <div className={style.tabs}>
        {data.map((item) => {
          return (
            <div
              key={item.value}
              onClick={() => {
                setActiveTabKey(item.value);
                onChange(item.time);
              }}
              className={`${style.tabs_item} ${activeTabKey === item.value ? style.tabs_item_active : ""}`}
            >
              {item.label}
            </div>
          );
        })}
      </div> */}
      <Segmented
        style={{ marginRight: '10px' }}
        value={activeTabKey}
        options={data}
        onChange={(e: any) => {
          setActiveTabKey(e);

          onChange(data.find((item) => item.value === e)?.time);
        }}
      />
      <RangePicker
        style={{
          borderRadius: '2px',
          fontSize: '12px'
        }}
        // showTime={{
        //   format: "HH:mm:ss",
        //   defaultValue: [
        //     dayjs("00:00:00", "HH:mm:ss"),
        //     dayjs("23:59:59", "HH:mm:ss"),
        //   ],
        // }}
        value={value}
        format="YYYY-MM-DD"
        onChange={onOk}
        allowClear={false}
      />
    </div>
  );
};

export default TabsTime;
