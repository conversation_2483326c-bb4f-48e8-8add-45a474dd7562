{"version": "0.2.0", "configurations": [{"name": "Debug Main Process (Local)", "type": "node", "request": "launch", "cwd": "${workspaceRoot}", "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron-vite", "windows": {"runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron-vite.cmd"}, "runtimeArgs": ["dev", "--sourcemap"], "env": {"REMOTE_DEBUGGING_PORT": "9222"}}, {"name": "Debug Renderer Process (Local)", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}/src/renderer", "timeout": 60000, "presentation": {"hidden": true}}, {"name": "Remote: Start Electron App", "type": "node", "request": "launch", "cwd": "${workspaceRoot}", "runtimeExecutable": "${workspaceRoot}/node_modules/.bin/electron-vite.cmd", "runtimeArgs": ["dev", "--sourcemap"], "env": {"REMOTE_DEBUGGING_PORT": "9222", "NODE_OPTIONS": "--inspect=0.0.0.0:9229"}, "console": "integratedTerminal", "presentation": {"group": "remote", "order": 1}}, {"name": "Remote: <PERSON><PERSON><PERSON> to Renderer", "port": 9222, "request": "attach", "type": "chrome", "webRoot": "${workspaceFolder}/src/renderer", "timeout": 60000, "url": "http://localhost:9222", "presentation": {"group": "remote", "order": 2}}, {"name": "Remote: Attach to Main Process", "type": "node", "request": "attach", "port": 9229, "localRoot": "${workspaceFolder}", "remoteRoot": "${workspaceFolder}", "skipFiles": ["<node_internals>/**"], "presentation": {"group": "remote", "order": 3}}], "compounds": [{"name": "Debug All (Local)", "configurations": ["Debug Main Process (Local)", "Debug Renderer Process (Local)"], "presentation": {"order": 1}}, {"name": "Remote: Debug All", "configurations": ["Remote: Attach to Main Process", "Remote: <PERSON><PERSON><PERSON> to Renderer"], "presentation": {"order": 2}}]}