import Modal from '@/components/Modal';
import { Form, Input, message } from 'antd';
import { useNavigate } from 'react-router-dom';
import {
  // loginOidc,
  // getOrganizations,
  getAssistants,
  // getUserRelation,
  // getMasterUser,
  userLogin
} from '@/api/myqaApi';
// import { dropOut, authentication } from '@/api/unifyApi';
import { useEffect, useState } from 'react';
// import axios from 'axios';
type Props = {
  open: boolean;
  setOpen: (bool: boolean) => void;
};
const AddAccount = ({ open, setOpen }: Props) => {
  const [form] = Form.useForm();
  const navigate = useNavigate();

  const [loginLock, setLoginLock] = useState(false);
  const onFinish = async (values) => {
    window.xBrowser?.send('TABS_DEl_All', {});
    try {
      setLoginLock(true);
      const loginRes = await userLogin(values);
      const userData = loginRes.data.user;
      const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
      localStorage.setItem(
        'accountData',
        JSON.stringify([
          ...accountData.filter((item) => {
            return item.username !== values.username;
          }),
          values
        ])
      );
      localStorage.setItem('accessToken', loginRes.data.tokens.accessToken);
      localStorage.removeItem('roleCode');
      localStorage.setItem('organization', userData.org_id);
      localStorage.setItem(
        'user',
        JSON.stringify({
          username: userData.user_name,
          id: userData.user_id,
          ...userData
        })
      );
      localStorage.setItem('configStates', JSON.stringify([]));
      setLoginLock(false);

      message.success('登录成功');
      window.xBrowser?.send('user-login', values.username);
      navigate('/home');
      setOpen(false);
      window.location.reload();
    } catch (error) {
      setLoginLock(false);
    }
    // try {
    //   setLoginLock(true);
    //   localStorage.setItem("username", values.username);
    //   await dropOut();
    //   await authentication({ uid_field: values.username });
    //   const res = await authentication({ password: values.password });
    //   if (res.data.response_errors) {
    //     message.error("账号或密码错误！");
    //     setLoginLock(false);
    //     return;
    //   }
    //   await myqaLogin({
    //     username: values.username,
    //     password: values.password,
    //   });
    // } catch (error) {
    //   setLoginLock(false);
    //   message.error("账号或密码错误！");
    // }
  };

  // const myqaLogin = ({ username, password }): Promise<any> => {
  //   return loginOidc().then(async (res) => {
  //     try {
  //       const d = new URL(res.url);
  //       // urt地址中的参数不影响调用结果
  //       const flowsRes = await axios.get(
  //         `https://auth.dongchacat.cn/api/v3/flows/executor/default-provider-authorization-implicit-consent/?query=response_type%3Dcode%26client_id%3D5DJM8fhLTkJCQltIq1Vhpv44PvShTrI6Y66g7N52%26redirect_uri%3Dhttps%253A%252F%252Faikf.tuzhou.net%253A2001%252Fapi%252Fv1%252Fauth%252Foidc%26scope%3Dopenid%2Bemail%2Bprofile%26state%3D${d.searchParams.get('state')}%26nonce%3D${d.searchParams.get('nonce')}`
  //       );
  //       const toRes = await axios.get(flowsRes.data.to);
  //       const { data } = toRes;
  //       const masterData = await getMasterUser(data.secret_key.id);
  //       const organ = await getOrganizations(data.secret_key.id);
  //       const rtres = await getUserRelation();
  //       const accountData = JSON.parse(localStorage.getItem('accountData') ?? '[]');
  //       localStorage.setItem(
  //         'accountData',
  //         JSON.stringify([
  //           ...accountData.filter((item) => {
  //             return item.username !== username;
  //           }),
  //           {
  //             username,
  //             password
  //           }
  //         ])
  //       );
  //       // const orgId =
  //       //   organ?.data?.find((item) => item.user_id === data.user.id)?.id ??
  //       //   null;
  //       const orgId = masterData?.data.master_org_id ?? null;
  //       const rtresItem = rtres.data?.data?.find((item) => item.fid === username);
  //       if (rtresItem) {
  //         localStorage.setItem('organization', rtresItem.oid);
  //         window.ipcRenderer.send('set-orgId', rtresItem.oid);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem(
  //           'user',
  //           JSON.stringify({
  //             username: '',
  //             ...data.user,
  //             masterUserId: masterData.data.master_user_id
  //           })
  //         );
  //       } else {
  //         localStorage.setItem('organization', orgId);
  //         window.ipcRenderer.send('set-orgId', orgId);
  //         window.ipcRenderer.send('set-myqaUser', {
  //           username: '',
  //           ...data.user,
  //           masterUserId: masterData.data.master_user_id
  //         });
  //         localStorage.setItem(
  //           'user',
  //           JSON.stringify({
  //             username: '',
  //             ...data.user,
  //             masterUserId: masterData.data.master_user_id
  //           })
  //         );
  //       }
  //       // localStorage.setItem("secret_key", data.secret_key.id);
  //       localStorage.setItem('secret_key', masterData.data.master_user_sk);
  //       localStorage.setItem('old_secret_key', data.secret_key.id);
  //       window.ipcRenderer.send('set-secretKey', masterData.data.master_user_sk);
  //       // closetabs();
  //       localStorage.setItem('configStates', JSON.stringify([]));
  //       const assistantsRes = await getAssistants();
  //
  //       const id = assistantsRes.data[0]?.id;
  //       localStorage.setItem('assistantId', id);
  //       localStorage.setItem(
  //         'recovery_mode',
  //         assistantsRes.data[0]?.metadata?.recovery_mode ?? 'inquiry'
  //       );
  //       window.ipcRenderer.send('set-assistants', id);
  //       message.success('登录成功');
  //       setLoginLock(false);
  //       setOpen(false);
  //       navigate('/home');
  //       return { res, organ };
  //     } catch (e) {
  //       message.error('登录失败');
  //       setLoginLock(false);
  //     }
  //   });
  // };

  useEffect(() => {
    form.resetFields();
  }, [open]);

  return (
    <Modal
      title="添加账户"
      open={open}
      onOk={() => {
        form.submit();
      }}
      width={480}
      okLoading={loginLock}
      setOpen={setOpen}
    >
      <div
        style={{
          width: '432px',
          margin: '24px auto'
        }}
      >
        <Form form={form} onFinish={onFinish} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名!' }]}
          >
            <Input placeholder="用户名" />
          </Form.Item>
          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码!' }]}
          >
            <Input.Password placeholder="密码" onPressEnter={() => form.submit()} />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default AddAccount;
