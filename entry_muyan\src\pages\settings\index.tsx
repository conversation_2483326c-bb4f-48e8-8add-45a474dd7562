import React from 'react';
import AccountManagement from './account_management';
const Settings: React.FC = () => {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'start',
        height: '100%'
      }}
    >
      {/* <Menu
        value={key}
        onChange={(val) => {
          setKey(val);
        }}
      /> */}
      <div
        style={{
          display: 'flex',
          height: '100%',
          overflow: 'initial',
          flex: 1
        }}
      >
        <AccountManagement />
      </div>
    </div>
  );
};

export default Settings;
