import React from 'react';
import nocode from '@/assets/imgs/nocode.png';

const Documentary: React.FC = () => {
  const titleStyle = {
    width: '108px',
    height: '25px',
    fontWeight: 'bold',
    fontSize: '18px',
    color: '#333333'
  };

  const tipStyle = {
    fontSize: '14px',
    color: '#333333',
    margin: '16px 0px 40px'
  };

  const imgStyle = {
    width: '160px',
    height: '160px',
    margin: '52px 0px 20px'
  };
  return (
    <div
      style={{
        height: '100%',
        padding: '20px',
        width: '100%',
        backgroundColor: '#ffffff'
      }}
    >
      <div style={titleStyle}>智能跟单说明</div>
      <div style={tipStyle}>
        智能跟单功能用于同步自由系统中的订单状态，在订单不同状态，给用户推送不同推送消息，促成用户转化。
      </div>
      <div style={titleStyle}>智能跟单对接流程</div>
      <div style={{ textAlign: 'center' }}>
        <img src={nocode} alt="nocode" style={imgStyle} />
        <div style={{ fontSize: '12px', color: '#666666', lineHeight: '18px' }}>正在开发中…</div>
      </div>
    </div>
  );
};

export default Documentary;
