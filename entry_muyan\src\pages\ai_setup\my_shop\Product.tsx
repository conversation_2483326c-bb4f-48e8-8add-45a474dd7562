/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useMemo } from 'react';
import { Table, message, Input, Button } from 'antd';
import type { TableColumnsType } from 'antd';
import style from './index.module.css';
import icon_back from '@/assets/icons/icon_back.svg';
import { getProduct, updateProduct, snapshot } from '@/api/myqaApi';
import { getPageAll } from './getPageAll';
import cloneDeep from 'lodash-es/cloneDeep';
import icon_interface_search from '@/assets/icons/icon_interface_search.svg';
import debounce from 'lodash-es/debounce';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { NodeContent } from './NodeContent';
import { Synchronization } from './Synchronization';
import { ProductEdit } from './ProductEdit';
type Props = {
  shopName: string;
  setShopName: (val: string) => void;
  setKey: (val: string) => void;
};

const Product: React.FC<Props> = ({ shopName, setShopName, setKey }) => {
  const [data, setData] = useState<any[]>([]);
  const [meta, setMeta] = useState<any>([]);
  const [shopKey, setShopKey] = useState('全部');
  const [title, setTitle] = useState('');
  const [open, setOpen] = useState(false);
  const [tbOpen, setTbOpen] = useState(false);
  const [editOpen, setEditOpen] = useState(false);
  const [editData, setEditData] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const columns: TableColumnsType<any> = [
    {
      title: '商品名称',
      dataIndex: 'title',
      key: 'title',
      width: 300
    },
    { title: '商品ID', dataIndex: 'item_id', key: 'item_id', width: 150 },
    {
      title: '商品类型',
      dataIndex: 'sub_type',
      key: 'sub_type',
      width: 100
    },
    {
      title: '商品信息状态',
      dataIndex: 'perfect',
      key: 'perfect',
      width: 120,
      render: (val) => {
        return (
          <div className={style.state}>
            <div className={`${val === 0 ? style.state_warning : style.state_success}`} />
            <div>{val === 0 ? '待补充' : '已完善'}</div>
          </div>
        );
      }
    },
    {
      title: '店铺名称',
      dataIndex: 'mall_name',
      key: 'mall_name',
      width: 100
    },
    // { title: "平台", dataIndex: "pingtai", key: "pingtai", width: 100 },
    {
      title: '操作',
      dataIndex: '',
      key: 'x',
      width: 180,
      fixed: 'right',
      render: (r) =>
        r.sub_type === '子商品' && r.parentId ? (
          <>
            <span
              className={`${style.text_button}`}
              onClick={() => {
                setEditData(r);
                setEditOpen(true);
              }}
            >
              编辑
            </span>
            <span
              className={`${style.text_button} ${r.code === r.mainly_promote ? style.text_button_active : ''}`}
              onClick={() => handleOpen(r)}
            >
              设为主推产品
            </span>
          </>
        ) : (
          <></>
        )
    }
  ];

  const handleOpen = (r: any): void => {
    if (r.code === r.mainly_promote) return;
    const argument = meta.find((item) => item.code === r.parentId) ?? {};
    argument.meta_data.mainly_promote = r.code;
    updateProduct(argument.id, argument).then(() => {
      message.success('设置成功');
      getData();
    });
  };
  const [snapshotDate, setSnapshotDate] = useState<any>({});
  useEffect(() => {
    getData();
    setHeight(getWindowHeight());
    setWidth(getWindowWidth());
    getSnapshot();
    // const goodsStateEvent = (_event, data) => {
    //   if (data.state === 'upload_goods_done') {
    //     getSnapshot();
    //   }
    // };
    // const remove = window.ipcRenderer.on('SYNC_GOODS_STATE_EVENT', goodsStateEvent);
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
      // if (remove) remove();
    };
  }, []);

  const [shopData, setShopData] = useState<string[]>([]);
  const getSnapshot = () => {
    snapshot().then((res) => {
      setSnapshotDate(res.data.data?.[0]);
    });
  };
  useEffect(() => {
    if (shopName) {
      setShopKey(shopName);
    }
  }, [shopName]);

  const getData = (title?: string): void => {
    setLoading(true);
    getPageAll(1000, 1, getProduct, (res) => res.data.data, [], { title }).then((res: any[]) => {
      setMeta(res);
      const formatRes = cloneDeep(formatData(res, res));
      const hiddenKey = formatRes
        .map((item) => {
          return Object.keys(item?.childrenObj ?? {});
        })
        .flat();
      setData(
        formatRes
          .filter((item) => {
            return !hiddenKey.includes(item.code);
          })
          .map((item) => {
            if (item.children) {
              if (item.children.find((item) => item.perfect === 0)) {
                return {
                  ...cloneDeep(item),
                  perfect: 0
                };
              } else {
                return {
                  ...cloneDeep(item),
                  perfect: 1
                };
              }
            }
            return item;
          })
      );
      setShopData([
        ...new Set(
          res
            .filter((item) => item?.meta_data?.mall_name)
            .map((item) => item?.meta_data?.mall_name ?? '')
        )
      ]);
      setLoading(false);
    });
  };
  const domainSearch = useMemo(
    () =>
      debounce((title: string) => {
        getData(title);
      }, 800),
    []
  );
  const typeMap = new Map().set('0', '商品').set('1', '子商品');
  function formatData(arr: any[], allData: any[], tier = 1): any {
    return arr.map((item) => {
      const children = Object.keys(item?.children ?? {})
        .map((key) => {
          const obj = {
            ...allData.find((aItem) => aItem.code === key),
            add_at: item?.children[key]?.add_at ?? 1,
            parentId: item?.code ?? '',
            mainly_promote: item?.meta_data?.mainly_promote ?? ''
          };
          const cItem = formatData(obj ? [obj] : [], allData, tier + 1);
          return cItem;
        })
        .flat();
      return {
        title: item?.title ?? '',
        key: item?.code || uuidv4(),
        code: item?.code,
        perfect: item?.meta_data?.files?.[0]?.url ? 1 : 0,
        sub_type: typeMap.get(item?.meta_data?.sub_type) ?? '-',
        children: children.length === 0 ? null : children,
        mall_name: item?.meta_data?.mall_name ?? '-',
        item_id: item?.item_id ?? '',
        childrenObj: item?.children,
        tier,
        // pingtai: "拼多多",
        parentId: item?.parentId ?? null,
        mainly_promote: item?.mainly_promote ?? ''
      };
    });
  }
  const [height, setHeight] = useState<number>(0);
  const [width, setWidth] = useState<number>(0);
  const getWindowHeight = (): number => {
    return (
      (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) -
      312
    );
  };
  const getWindowWidth = (): number => {
    return (
      (window.innerWidth || document.documentElement.clientWidth || document.body.clientWidth) - 288
    );
  };
  const handleResize = (): void => {
    setHeight(getWindowHeight());
    setWidth(getWindowWidth());
  };

  return (
    <div
      style={{
        height: '100%',
        padding: '20px',
        width: '100%',
        backgroundColor: '#FFFFFF'
      }}
    >
      <div className={style.header}>
        {shopName && (
          <img
            src={icon_back}
            style={{
              width: '24px',
              height: '24px',
              cursor: 'pointer',
              marginRight: '20px'
            }}
            onClick={() => {
              setKey('2');
              setShopName('');
            }}
            alt="icon_back"
          />
        )}
        <div className={style.titleCss}>我的店铺</div>
        <div>
          店铺商品 最近一次同步时间:
          {snapshotDate?.updated_at
            ? dayjs(snapshotDate.updated_at).format('YYYY-MM-DD HH:mm:ss')
            : '-'}
          <Button
            type="link"
            onClick={() => {
              setTbOpen(true);
            }}
          >
            同步
          </Button>
          <Button
            type="link"
            style={{
              marginLeft: '-20px'
            }}
            onClick={() => {
              setOpen(true);
            }}
          >
            查看历史
          </Button>
        </div>
      </div>
      <div style={{ margin: '20px 0px' }}>
        <Input
          className={style.input}
          value={title}
          onInput={(e: any) => {
            setTitle(e.target.value);
          }}
          prefix={
            <img
              src={icon_interface_search}
              alt="icon"
              style={{
                width: '14px',
                height: '14px'
              }}
            />
          }
          placeholder="请输入商品名称"
        />
        <Button
          type="primary"
          onClick={() => {
            domainSearch(title);
          }}
        >
          搜索
        </Button>
        <Button
          style={{
            marginLeft: '12px'
          }}
          onClick={() => {
            setTitle('');
            domainSearch('');
          }}
        >
          重置
        </Button>
        {/* <Select
          className={style.select}
          defaultValue="pdd"
          options={[{ value: "pdd", label: "拼多多" }]}
        /> */}
        {/* <Select
          className={style.select}
          value={shopKey}
          onChange={setShopKey}
          options={["全部", ...shopData].map((item) => {
            return { value: item, label: item };
          })}
        /> */}
      </div>
      <div style={{ width: width + 'px' }}>
        <Table
          loading={loading}
          columns={columns}
          dataSource={data.filter((item) => {
            if (shopKey === '全部') return true;
            return item.mall_name === shopKey;
          })}
          bordered
          scroll={{ x: width, y: height, scrollToFirstRowOnChange: true }}
        />
      </div>

      <NodeContent
        jsonData={snapshotDate?.shot ?? {}}
        open={open}
        setOpen={setOpen}
        title="同步详情"
      />
      <Synchronization title="" shopData={shopData} open={tbOpen} setOpen={setTbOpen} />
      <ProductEdit data={editData} title="编辑商品" open={editOpen} setOpen={setEditOpen} />
    </div>
  );
};

export default Product;
