/*instrumentation.ts*/
import { NodeSDK } from '@opentelemetry/sdk-node';
import { ConsoleSpanExporter } from '@opentelemetry/sdk-trace-node';
import { PeriodicExportingMetricReader, ConsoleMetricExporter } from '@opentelemetry/sdk-metrics';
import { Resource } from '@opentelemetry/resources';
import {
  SEMRESATTRS_SERVICE_NAME,
  SEMRESATTRS_SERVICE_VERSION
} from '@opentelemetry/semantic-conventions';
const { SimpleSpanProcessor, BatchSpanProcessor } = require('@opentelemetry/sdk-trace-base');
const { ZipkinExporter } = require('@opentelemetry/exporter-zipkin');
const { OTLPTraceExporter } = require('@opentelemetry/exporter-trace-otlp-grpc');
const { OTLPMetricExporter } = require('@opentelemetry/exporter-metrics-otlp-proto');
const { HttpInstrumentation } = require('@opentelemetry/instrumentation-http');
const { HostMetrics } = require('host-metrics-extend');
import { metrics } from '@opentelemetry/api';
import packageJson from '../../package.json';
import os from 'node:os';

const sdk = new NodeSDK({
  resource: new Resource({
    [SEMRESATTRS_SERVICE_NAME]: packageJson.name,
    [SEMRESATTRS_SERVICE_VERSION]: packageJson.version,
    token: 'clIxcpysyFiOCPQAtcdI'
  }),
  spanProcessors: [
    // new SimpleSpanProcessor(new ConsoleSpanExporter()),
    new BatchSpanProcessor(
      new OTLPTraceExporter({
        url: 'http://ap-shanghai.apm.tencentcs.com:4317'
      })
    )
  ],
  // traceExporter: new ConsoleSpanExporter(),
  // @ts-ignore it's ok
  metricReader: new PeriodicExportingMetricReader({
    exporter: new OTLPMetricExporter({
      url: 'https://prome.dongchacat.cn/api/v1/otlp/v1/metrics',
      headers: {
        Authorization: 'Basic YWRtaW46YWRtaW5AMTIz'
      }
    }),
    exportIntervalMillis: 15000
  }),
  instrumentations: [
    new HttpInstrumentation({
      ignoreOutgoingRequestHook(request) {
        const { host } = request;

        if (/\.aliyuncs\.com/.test(host)) {
          return true;
        }

        return false;
      }
    })
  ]
});

sdk.start();

const hostMetrics = new HostMetrics({
  name: packageJson.name,
  metricPrefix: import.meta.env.MODE + '_',
  attributes: {
    mode: import.meta.env.MODE,
    version: packageJson.version,
    instance: os.userInfo().username + '@' + os.hostname()
  }
});
hostMetrics.start();

// @ts-ignore required for instrumentation
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const http = require('http'); // required for instrumentation
// @ts-ignore required for instrumentation
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const https = require('https'); // required for instrumentation

// metrics.setGlobalMeterProvider(new MeterProvider());
// const heartbeat = metrics.getMeter(packageJson.name, packageJson.version).createGauge('browser-heartbeat');
//
// setInterval(() => {
//   heartbeat.record(Date.now(), {
//     name: packageJson.name,
//     version: packageJson.version,
//     clientId: 'abcd'
//   });
// }, 5000);
