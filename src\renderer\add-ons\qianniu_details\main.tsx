// import "../common/instrumentation.ts";
import ReactD<PERSON> from 'react-dom/client';
import App from './App';
// import "../common/global.css";

ReactDOM.createRoot(document.getElementById('root')!).render(<App />);

// Remove Preload scripts loading
postMessage({ payload: 'removeLoading' }, '*');

// Use contextBridge
// window.ipcRenderer.on("main-process-message", (_event, message) => {
//   console.log(message);
// });

// window.electronAPI.onToastMessage((message) => {
//   console.log('receive toast message', message);
// });
